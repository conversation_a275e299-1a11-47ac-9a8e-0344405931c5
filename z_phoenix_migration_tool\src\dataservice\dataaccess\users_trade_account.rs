use crate::dataservice::{
    customer_entities::{prelude::*, users_trade_account},
    dbsetup::DbConnection,
};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, DbErr, EntityTrait, QueryFilter, QuerySelect};
// use serde_json::json;

impl users_trade_account::Model {
    pub async fn find_user_id_by_account_no(db: &DbConnection, account_no: i64) -> Result<Option<i64>> {
        let ret_data: Result<Option<i64>, DbErr> = UsersTradeAccountEntity::find()
            .filter(users_trade_account::Column::AccountNo.eq(account_no))
            .select_only()
            .column(users_trade_account::Column::UserId)
            .into_tuple()
            .one(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(format!("UsersTradeAccount: {:?}", ret_data.err())));
        }

        Ok(ret_data.unwrap())
    }
}
