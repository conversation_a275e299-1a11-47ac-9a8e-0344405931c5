//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_dividend_record")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub record_id: i64,
    pub position_no: i64,
    pub sys_date: i32,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub today_dividend: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub total_dividend: Decimal,
    pub today_right_amount: i32,
    pub total_right_amount: i32,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub ontheway_dividend: Decimal,
    pub stock_code: String,
    pub unit_id: i64,
    pub busin_flag: i32,
    #[sea_orm(column_type = "Decimal(Some((16, 6)))")]
    pub rate: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 6)))")]
    pub taxation: Decimal,
    pub position_amount: i32,
    pub ontheway_right_amount: i32,
    pub status: i32,
    pub dividend_no: i64,
    pub currency_no: String,
    pub regist_date: i32,
    pub remark: String,
    pub channel_account: i32,
    pub modify_time: i64,
    pub create_time: i64,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
