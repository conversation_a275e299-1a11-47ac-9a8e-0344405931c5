syntax = "proto3";
package phoenixmatchserver;

service MatchService {
  rpc ManualMatch(ManualMatchReq) returns (MatchResp) {}   // 手动撮合
}

//--------------------------------------------------------------------
//message AutoMatchReq {
//  int64   msg_id         = 1;
//  int64   unit_id        = 2;         // 用户id
//  int64   stock_id       = 3;         // 证券id
//  int32   order_direction= 4;         // 委托方向  1=买  2=卖
//  int32   order_qty      = 5;         // 订单数量
//  int32   price_type     = 6;         // 价格类型(市价限价)
//  double  order_price    = 7;         // 委托价格
//}

message MatchResp {
  int64  msg_id       = 1;          // 与请求消息ID对应
  int32  error_code   = 2;
  string error_msg    = 3;
}

message ManualMatchReq {
  int64   msg_id         = 1;
  int64   unit_id        = 2;         // 用户id
  int64   stock_id       = 3;         // 证券id
//  int32   order_direction= 4;         // 委托方向  1=买  2=卖
  int32   order_qty      = 5;         // 订单数量
  string  trade_time = 6;              //时间
  double  order_price    = 7;         // 委托价格
  int64   to_unit_id        = 8;         // 目标账号
  int64   user_id        = 9;         // 用户id
  int64   to_user_id        = 10;         // 目标账号
}