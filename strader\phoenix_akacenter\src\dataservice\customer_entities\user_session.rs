//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "user_session")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    #[sea_orm(unique)]
    pub mail: String,
    #[sea_orm(unique)]
    pub session: String,
    pub user_id: Option<i64>,
    pub source: Option<i8>,
    pub session_type: i8,
    pub create_time: i64,
    pub update_time: i64,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
