// use crate::protoes::heartbeat::{heartbeat_service_client::HeartbeatServiceClient, HeartbeatRequest};
use chrono::Utc;
use common::logclient::log_error;
use protoes::{HeartbeatRequest, heartbeat_service_client::HeartbeatServiceClient};
use tokio::sync::broadcast;
use tokio::time::{Duration, interval, timeout};
use tokio_stream::wrappers::ReceiverStream;
use tonic::{Request, Status};
use tracing::*;

#[derive(Clone, Debug)]
pub struct HeartbeatClient {
    client_id: String,
    pub service_name: String,
    is_healthy: tokio::sync::watch::Receiver<bool>,
    shutdown_tx: broadcast::Sender<()>, // Add shutdown sender
                                        // 不持有 client，因為重連會創建新 client
}

impl HeartbeatClient {
    pub async fn new(
        endpoint: &str,
        client_id: String,
        service_name: String,
        send_heartbeat: bool, // 控制是否發送客戶端心跳
    ) -> Self {
        let (is_healthy, shutdown_tx, client_id_clone, service_name_clone) = Self::start_heartbeat_monitor(endpoint.to_string(), client_id.clone(), service_name.clone(), send_heartbeat).await;

        Self {
            client_id: client_id_clone,
            service_name: service_name_clone,
            is_healthy,
            shutdown_tx,
        }
    }

    pub fn stop(&self) {
        info!(service = %self.service_name, "Sending shutdown signal to heartbeat monitor");
        // A send can fail if there are no receivers. This is okay during shutdown.
        let _ = self.shutdown_tx.send(());
    }

    async fn start_heartbeat_monitor(endpoint: String, client_id: String, service_name: String, send_heartbeat: bool) -> (tokio::sync::watch::Receiver<bool>, broadcast::Sender<()>, String, String) {
        let (tx, rx) = tokio::sync::watch::channel(false);
        let (shutdown_tx, mut shutdown_rx) = broadcast::channel(1); // Create shutdown channel

        let client_id_clone = client_id.clone();
        let service_name_clone = service_name.clone();
        let shutdown_tx_clone = shutdown_tx.clone();
        let interval = 10;
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    _ = shutdown_rx.recv() => {
                        info!(service = %service_name, "Heartbeat monitor received shutdown. Exiting...");
                        break;
                    },
                    _ = async {
                        debug!(service = %service_name, endpoint, "Heartbeat,Attempting to connect");
                        match tonic::transport::Channel::from_shared(endpoint.clone()) {
                            Ok(channel) => match channel.connect().await {
                                Ok(channel) => {
                                    let mut client = HeartbeatServiceClient::new(channel);
                                    let _ = tx.send(true);
                                    debug!(service = %service_name, "Connection established");
                                    if let Err(e) = Self::monitor_heartbeat(&mut client, client_id.clone(), service_name.clone(), tx.clone(), send_heartbeat, shutdown_tx.subscribe()).await {
                                        error!(service = %service_name, "Heartbeat monitor failed: {}", e);
                                    }else{
                                        debug!(service = %service_name, "Heartbeat monitor completed successfully");
                                    }
                                    // if Self::monitor_heartbeat(&mut client, client_id.clone(), service_name.clone(), tx.clone(), send_heartbeat, shutdown_tx.subscribe()).await.is_ok() {
                                    //     debug!(service = %service_name, "Heartbeat monitor completed successfully");
                                    // }else{
                                    //     warn!(service = %service_name, "Heartbeat monitor failed ***************");
                                    // }
                                }
                                Err(e) => {
                                    error!(service = %service_name, "Connection failed: {}", e);
                                    log_error(&format!("Connection failed: {}", e)).await;
                                    let _ = tx.send(false);
                                }
                            },
                            Err(e) => {
                                error!(service = %service_name, "Invalid endpoint: {}", e);
                                log_error(&format!("Invalid endpoint: {}", e)).await;
                                let _ = tx.send(false);
                            }
                        }
                        warn!(service = %service_name, "Retrying connection in {} seconds",interval);
                        tokio::time::sleep(Duration::from_secs(interval as u64)).await;
                    } => {}
                }
            }
            info!(service = %service_name, "Heartbeat monitor task terminated,");
        });

        (rx, shutdown_tx_clone, client_id_clone, service_name_clone)
    }

    async fn monitor_heartbeat(
        client: &mut HeartbeatServiceClient<tonic::transport::Channel>,
        client_id: String,
        service_name: String,
        tx: tokio::sync::watch::Sender<bool>,
        send_heartbeat: bool,
        mut shutdown_rx: broadcast::Receiver<()>,
    ) -> Result<(), Status> {
        // 創建客戶端心跳流（即使不發送也要初始化流）
        let (tx_stream, rx_stream) = tokio::sync::mpsc::channel(100);
        let heart_interval = 10;
        // 如果啟用客戶端心跳，發送心跳消息
        if send_heartbeat {
            let client_id_clone = client_id.clone();
            let mut shutdown_rx_heartbeat = shutdown_rx.resubscribe();
            tokio::spawn(async move {
                let mut interval = interval(Duration::from_secs(heart_interval));
                // 發送初始心跳以建立流
                if tx_stream
                    .send(HeartbeatRequest {
                        client_id: client_id_clone.clone(),
                        timestamp: Utc::now().timestamp(),
                    })
                    .await
                    .is_err()
                {
                    warn!(client_id = %client_id_clone, "Initial heartbeat send failed");
                    return;
                }

                loop {
                    tokio::select! {
                        _ = shutdown_rx_heartbeat.recv() => {
                            info!(client_id = %client_id_clone, "Heartbeat sender received shutdown signal. Exiting.");
                            break;
                        },
                        _ = interval.tick() => {
                            let timestamp = Utc::now().timestamp();
                            debug!(client_id = %client_id_clone, timestamp, "Sending heartbeat");
                            if tx_stream
                                .send(HeartbeatRequest {
                                    client_id: client_id_clone.clone(),
                                    timestamp,
                                })
                                .await
                                .is_err()
                            {
                                warn!(client_id = %client_id_clone, "Heartbeat send channel closed");
                                break;
                            }
                        }
                    }
                }
            });
        } else {
            // 發送初始心跳以建立流
            let _ = tx_stream
                .send(HeartbeatRequest {
                    client_id: client_id.clone(),
                    timestamp: Utc::now().timestamp(),
                })
                .await;
        }

        let request = Request::new(ReceiverStream::new(rx_stream));
        let mut server_stream = client.heartbeat_stream(request).await?.into_inner();
        let heartbeat_timeout = Duration::from_secs(25); // 2.5x the heartbeat interval

        // let _ = tx.send(true); // 連接成功，設置健康狀態

        loop {
            tokio::select! {
                _ = shutdown_rx.recv() => {
                    warn!(service = %service_name, "Heartbeat stream cancelled due to shutdown signal.");
                    let _ = tx.send(false);
                    return Err(Status::cancelled("Shutdown signal received"));
                },
                response_result = timeout(heartbeat_timeout, server_stream.message()) => {
                     match response_result {
                        Ok(Ok(Some(response))) => {
                            debug!(
                                service = %service_name,
                                timestamp = response.timestamp,
                                status = response.status,
                                "Received heartbeat"
                            );
                            let _ = tx.send(true);
                        }
                        Ok(Ok(None)) => {
                            warn!(service = %service_name, "Heartbeat stream closed by server");
                            let _ = tx.send(false);
                            return Err(Status::unavailable("Server closed stream"));
                        }
                        Ok(Err(e)) => {
                            error!(service = %service_name, "Heartbeat error: {}", e);
                            let _ = tx.send(false);
                            return Err(e);
                        }
                        Err(_) => {
                            error!(service = %service_name, "Heartbeat timeout, server may be down");
                            let _ = tx.send(false);
                            return Err(Status::deadline_exceeded("Heartbeat timeout"));
                        }
                    }
                }
            }
        }
    }

    pub fn is_healthy(&self) -> bool {
        *self.is_healthy.borrow()
    }

    pub fn client_id(&self) -> &str {
        &self.client_id
    }
}
