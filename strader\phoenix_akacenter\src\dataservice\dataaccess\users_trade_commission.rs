use crate::dataservice::{
    dbsetup::DbConnection,
    entities::{
        prelude::{UsersTradeCommission, UsersTradeCommissionEntity},
        users_trade_commission,
    },
};
use anyhow::{anyhow, Result};
use sea_orm::{DbErr, EntityTrait};

impl users_trade_commission::Model {
    pub async fn find_all(db: &DbConnection) -> Result<Vec<UsersTradeCommission>> {
        let ret_data: Result<Vec<UsersTradeCommission>, DbErr> = UsersTradeCommissionEntity::find().all(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }
}
