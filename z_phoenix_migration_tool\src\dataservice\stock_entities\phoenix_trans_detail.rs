//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_trans_detail")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub p_account_target: i32,
    pub p_account_source: i32,
    pub p_op_flag: i32,
    #[sea_orm(column_type = "Decimal(Some((20, 4)))")]
    pub p_trans_value: Decimal,
    pub p_capital_type: i32,
    pub p_account_no: i32,
    pub p_datetime: i64,
    pub p_remark: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
