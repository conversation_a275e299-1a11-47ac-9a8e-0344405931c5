use crate::dataservice::{
    entities::{
        prelude::{SysCommodityExt, SysCommodityExtEntity},
        sys_commodity_ext,
    },
};
use dbconnection::DbConnection;
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, Condition, DbErr, EntityTrait, QueryFilter};

impl SysCommodityExt {
    pub async fn find_by_commodity_id(db: &DbConnection, commodity_id: i64) -> Result<SysCommodityExt> {
        let ret_data: Result<Option<SysCommodityExt>, DbErr> = SysCommodityExtEntity::find().filter(sys_commodity_ext::Column::CommodityId.eq(commodity_id)).one(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        let data = ret_data.unwrap();
        return match data {
            Some(v) => Ok(v),
            None => Err(anyhow!("SysCommodityExt数据不存在")),
        };
    }

    pub async fn _find_by_condition(db: &DbConnection, condition: Condition) -> Result<Vec<SysCommodityExt>> {
        let ret_data: Result<Vec<SysCommodityExt>, DbErr> = SysCommodityExtEntity::find().filter(condition).all(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }

    pub async fn _find_all(db: &DbConnection) -> Result<Vec<SysCommodityExt>> {
        let ret_data: Result<Vec<SysCommodityExt>, DbErr> = SysCommodityExtEntity::find().all(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }
}
