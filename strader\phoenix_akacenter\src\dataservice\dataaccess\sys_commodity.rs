use crate::dataservice::entities::prelude::{SysCommodityExtEntity, SysMarketEntity};
use crate::dataservice::entities::{
    prelude::{SysCommodity, SysCommodityEntity},
    sys_commodity,
};
use crate::dataservice::entities::{sys_commodity_ext, sys_market};
use anyhow::{anyhow, Result};
use dbconnection::DbConnection;
use rust_decimal::Decimal;
use sea_orm::{ColumnTrait, Condition, DbErr, EntityTrait, FromQueryResult, QueryFilter, QuerySelect};

#[derive(Debug, FromQueryResult)]
pub struct CommoditySelectRes {
    pub id: i64,
    pub code: String,
    pub name: String,
    pub trade_state: i8,
    pub r#type: i32,
    pub hands_num: i32,
    pub stock_type: i16,
    pub min_value: Decimal,
    pub max_value: Decimal,
    pub max_money: i64,
    pub market_id: i64,
    pub market_code: String,
    pub currency: String,
    pub bond_wc: Decimal,
}

#[derive(Debug, FromQueryResult)]
pub struct MarketSelectRes {
    pub id: i64,
    pub market_code: String,
}

impl sys_commodity::Model {
    #[allow(dead_code)]
    pub async fn find_all(db: &DbConnection) -> Result<Vec<SysCommodity>> {
        let ret_data: Result<Vec<SysCommodity>, DbErr> = SysCommodityEntity::find().filter(sys_commodity::Column::Status.eq(1)).all(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }

    pub async fn find_by_id(db: &DbConnection, id: i64) -> Result<Option<SysCommodity>> {
        let ret_data: Result<Option<SysCommodity>, DbErr> = SysCommodityEntity::find()
            .filter(sys_commodity::Column::Status.eq(1))
            .filter(sys_commodity::Column::Id.eq(id))
            .one(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        let data = ret_data.unwrap();
        Ok(data)
    }

    pub async fn find_by_condition(db: &DbConnection, condition: Condition) -> Result<Option<SysCommodity>> {
        let ret_data: Result<Option<SysCommodity>, DbErr> = SysCommodityEntity::find().filter(sys_commodity::Column::Status.eq(1)).filter(condition).one(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }

    pub async fn find_join_others(db: &DbConnection) -> Result<Vec<CommoditySelectRes>> {
        let ret_data = SysCommodityEntity::find()
            .inner_join(SysMarketEntity)
            .inner_join(SysCommodityExtEntity)
            .select_only()
            .column(sys_commodity::Column::Id)
            .column(sys_commodity::Column::Code)
            .column(sys_commodity::Column::Name)
            .column(sys_commodity::Column::TradeState)
            .column(sys_commodity::Column::Type)
            .column(sys_commodity::Column::HandsNum)
            .column(sys_commodity::Column::StockType)
            .column(sys_commodity_ext::Column::MinValue)
            .column(sys_commodity_ext::Column::MaxValue)
            .column(sys_commodity_ext::Column::MaxMoney)
            .column(sys_commodity::Column::MarketId)
            .column(sys_market::Column::MarketCode)
            .column(sys_commodity::Column::Currency)
            .column(sys_commodity_ext::Column::BondWc)
            .filter(sys_commodity::Column::Status.eq(1))
            .filter(sys_market::Column::DelState.eq(1))
            .group_by(sys_commodity_ext::Column::CommodityId)
            .into_model::<CommoditySelectRes>()
            .all(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }

    pub async fn query_market_code(db: &DbConnection, stock_code: String) -> Result<Option<MarketSelectRes>> {
        let ret_data = SysCommodityEntity::find()
            .inner_join(SysMarketEntity)
            .select_only()
            .column(sys_market::Column::MarketCode)
            .column(sys_market::Column::Id)
            .filter(sys_commodity::Column::Code.eq(stock_code))
            .into_model::<MarketSelectRes>()
            .one(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }
}
