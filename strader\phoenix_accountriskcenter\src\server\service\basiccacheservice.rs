use std::{collections::HashMap, sync::Arc};
use tracing::*;

use akaclient::akaclient::AkaClient;
use anyhow::{anyhow, Ok, Result};
use tokio::sync::RwLock;

pub struct BasicCacheService {
    stockcodes: Arc<RwLock<HashMap<String, i64>>>, //String:600000_XSHG, i64:stockid
}

impl BasicCacheService {
    pub fn new() -> Self {
        BasicCacheService {
            stockcodes: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    pub async fn get_stockid(&self, code: &str, akasvc: &AkaClient) -> Result<i64> {
        let stock_id: i64; // = 0;
        let mut flag = false;
        {
            let ret = self.stockcodes.read().await;
            if !ret.contains_key(code) {
                let vec_key: Vec<&str> = code.split("_").collect();
                if vec_key.len() != 2 {
                    return Err(anyhow!("code error: {}", &code));
                }
                let akaret = akasvc.query_stock_info_by_code(vec_key[0], vec_key[1]).await;
                if akaret.is_none() {
                    return Err(anyhow!("query from akaserver error"));
                }
                let stockinfo = akaret.unwrap();
                stock_id = stockinfo.stock_id;
                flag = true;
            } else {
                stock_id = ret.get(code).unwrap().to_owned();
            }
        }

        if flag {
            self.stockcodes.write().await.insert(code.to_string(), stock_id);
        }

        Ok(stock_id)
    }

    //计算用户的某个品种的最终的保证金比例
    // 1 保证金比例取值规则（实时/定时）
    // 1-1 若当前无融资合同，全部为100%
    // 1-2 若当前有融资合同，按如下规则
    // a 按“产品管理/产品基础配置/交易品种”设置的品种保证金比例（同一只股票只能有一个值）
    // b 按客户杠杆比例折算的保证金比例（合同比例）
    // c “人员管理/客户管理/客户保证金比例”设置，某一品种在某一客户下的比例（客户产品比例）
    // d 停牌增量值
    // 停牌增量值=min(100%, 25%*停牌日数)
    // e 最终取值
    // 取值规则：
    // （1）若已设置c
    // 如果股票未停牌，e = c
    // 如果股票已停牌，e = min(100%, c+d)
    // （2）若未设置c
    // 如果股票未停牌，e = max(a, b)
    // 如果股票已停牌，e = min(100%, max(a, b)+d)
    // 另，保证金比例，取值范围限定：[20%, 100%]
    pub async fn calculation_user_stock_rate(&self, akasvc: &AkaClient, stock_id: i64, user_id: i64, credit_multiple: f64) -> Result<f64> {
        //先查询此品种是否设置了品种保证金比例
        let mut ret_rate; // = 0.0;

        if credit_multiple == 0.0 {
            return Result::Ok(1.0);
        }

        let ret = akasvc.query_unit_stock_margin(user_id, stock_id).await;
        if let Err(err) = ret {
            error!("query_unit_stock_margin异常,{:?}", err);
            return Err(err);
        }
        let rate1 = ret.unwrap();
        // info!("calculation_user_stock_rate用户品种保证金,{},{},{}", user_id, stock_id, rate1);
        //查询此品种是否有停牌信息,并得到增量值
        let ret1 = akasvc.query_stock_suspension_info().await;
        if let Err(err) = ret1 {
            error!("query_stock_suspension_info异常,{:?}", err);
            return Err(err);
        }

        let ret_list = ret1.unwrap();
        let mut rate2 = 0.0;
        for item in ret_list {
            if stock_id == item.stock_id {
                rate2 = item.margin_rate_increment;
            }
        }

        // info!("calculation_user_stock_rate品种停牌增量,{},{},{}", user_id, stock_id, rate2);
        //计算得到用户的保证金
        let unit_rate = 1.0 / (credit_multiple + 1.0);
        // info!("calculation_user_stock_rate用户杠杠的保证金,{},{},{}", user_id, stock_id, unit_rate);
        //若设置了用户品种保证金
        if rate1 > 0.0 {
            //若停牌
            if rate2 > 0.0 {
                ret_rate = rate1 + rate2;
            } else {
                ret_rate = rate1;
            }
        } else {
            //查询品种的保证金比例
            let ret = akasvc.query_stock_info(stock_id).await;
            if let Err(err) = ret {
                error!("query_stock_info异常,{:?}", err);
                return Err(err);
            }
            let ret = ret.unwrap();
            if let None = ret {
                error!("query_stock_info 找不到信息,{:?}", stock_id);
                return Err(anyhow!(format!("query_stock_info 找不到信息,{:?}", stock_id)));
            }

            let rate3 = ret.unwrap().margin_rate;
            // info!("calculation_user_stock_rate品种的保证金,{},{},{}", user_id, stock_id, rate3);
            if rate3 > unit_rate {
                ret_rate = rate3
            } else {
                ret_rate = unit_rate;
            }
            ret_rate += rate2;
        }
        // info!("calculation_user_stock_rate最终保证金比例,{},{},{}", user_id, stock_id, ret_rate);
        if ret_rate > 1.0 {
            ret_rate = 1.0;
        }
        if ret_rate < 0.05 {
            ret_rate = 0.05;
        }

        Ok(ret_rate)
    }
}
