use crate::server::service::assetchange;
use crate::server::service::convertdata;
use crate::server::service::notificationmsg;
use crate::server::service::queryinfo;
use crate::{
    config::settings::Settings,
    server::service::{ordercache::<PERSON><PERSON><PERSON><PERSON>, tool},
};
use accountriskcenterclient::AccountRiskClient;
use akaclient::{
    akaclient::AkaClient,
    calcfee::{FeeDetail, FeeField},
};
use anyhow::Result;
use assetscenterclient::AssetsCenterClient;
use chrono::Timelike;
use common::{
    constant::{ChannelType, OrderDirection, OrderStatus},
    // logclient::LogClient,
    redisclient::redispool::RedisClient,
    uidservice::UidgenService,
};
use dbconnection::DbConnection;
// use messagecenter::notificationclient::NotificationClient;
use messagecenter::NotificationClientV2;
use protoes::phoenixordercenter::Riskinfo;
use protoes::phoenixordercenter::{CancelReq, OrderReq, OrderResp, ReplenishOrderReq};
use protoes::phoenixriskcenter::PhoenixRiskCheckInfo;
use riskcenterclient::RiskCenterClient;
// use protoes::phoenixorderrouter::router
use protoes::phoenixordermsg::{
    ExecMsg,
    ExecType,
    // ReplenishOrderResp
    RouterMsg,
};
use rust_decimal::prelude::*;
use rust_decimal_macros::dec;
use std::{ops::AddAssign, sync::Arc};
use tokio::sync::{broadcast, mpsc, RwLock};
use tracing::{error, info};

use crate::dataservice::entities::{
    prelude::PhoenixOrdCancel,
    // prelude::PhoenixOmsTradeconfig,
    prelude::PhoenixOrdPendSettle,
    prelude::PhoenixOrdStockdeal,
    prelude::PhoenixOrdStockorder,
    prelude::PhoenixOrdSuborder,
};
use crate::odcommon::common::OrderDetail;

#[non_exhaustive]
pub enum PersistData {
    StockDeal(Box<PhoenixOrdStockdeal>),
    PendSettle(Box<PhoenixOrdPendSettle>),
    StockOrder(Box<PhoenixOrdStockorder>),
    SubOrder(Box<Vec<PhoenixOrdSuborder>>),
    OrdCancel(Box<PhoenixOrdCancel>),
}

#[derive(Clone)]
pub struct OrderCenterController {
    #[allow(dead_code)]
    pub settings: Arc<Settings>,
    pub tx_persist: mpsc::Sender<PersistData>,
    pub tx_order: broadcast::Sender<RouterMsg>, //发订单消息 -> 报盘
    pub tx_filled: mpsc::Sender<ExecMsg>,       //撮合
    pub tx_cancel: mpsc::Sender<ExecMsg>,       //未报撤单
    pub tx_opponent: mpsc::Sender<OrderDetail>,
    pub db_client: Arc<DbConnection>,
    pub aka_client: Arc<AkaClient>,
    // pub riskcenter_client: Arc<RwLock<RiskCenterClient>>,
    // pub assetscenter_client: Arc<RwLock<AssetsCenterClient>>,
    // pub account_risk_client: Arc<RwLock<AccountRiskClient>>,
    pub assetscenter_client: AssetsCenterClient,
    pub riskcenter_client: RiskCenterClient,
    pub account_risk_client: AccountRiskClient,
    pub redis_client: Arc<RedisClient>,
    pub mq_client: Arc<RwLock<NotificationClientV2>>,
    pub uidgen: Arc<RwLock<UidgenService>>,
}
//增加订单redis缓存

impl OrderCenterController {
    pub async fn place_order(&self, req_order: &OrderReq) -> Result<OrderResp> {
        let now = std::time::Instant::now();
        info!("收到订单: {:?}", req_order);
        let mut order_detail = OrderDetail::place_order(&req_order).await;
        let mut id = self.uidgen.write().await;
        order_detail.order_id = id.get_uid();
        order_detail.msg_id = id.get_uid();
        drop(id);

        let mut resp = OrderResp {
            msg_id: order_detail.msg_id,
            order_id: order_detail.order_id,
            error_code: 0,
            error_msg: format!("委托成功"),
        };

        // 交易信息: 品种信息,交易时间
        if let Err(err) = self.aka_stock_trade_info(&mut order_detail).await {
            error!("memo: {:?}", err);
            resp.error_code = 1;
            resp.error_msg = format!("{:?}", err);
            return Ok(resp);
        }

        //风控拆单: 用户币种,保证金比例
        let ret = self.risk_check(&mut order_detail).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            resp.error_code = 1;
            resp.error_msg = ret.as_ref().err().unwrap().to_string();
            return Ok(resp);
        }
        let risk_ret = ret.unwrap();
        info!("风控用时: {}, {:?}", order_detail.order_id, now.elapsed());

        // 计算费用
        if let Err(err) = self.calc_fee_info(&mut order_detail).await {
            error!("{:?}", err);
            resp.error_code = 1;
            resp.error_msg = format!("{}", err);
            return Ok(resp);
        }
        order_detail.pre_fee = order_detail.fee_total;
        order_detail.capital = (Decimal::from(order_detail.order_amount) * order_detail.order_price * order_detail.margin_rate + order_detail.fee_total) * order_detail.rate;
        info!("订单明细: {:?}", &order_detail);

        //生成订单
        let ret = self.generate_order(&order_detail, &risk_ret).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            resp.error_code = 1;
            resp.error_msg = ret.as_ref().err().unwrap().to_string();
            return Ok(resp);
        }
        let (new_order, new_sub_orders) = ret.unwrap();

        //自撮合通道
        for sub_order in new_sub_orders.iter() {
            if sub_order.channel_type == ChannelType::INTERNAL as i32 {
                let new_opponent_order = OrderDetail::opponent_order(&order_detail, sub_order).await;
                let _ = self.tx_opponent.send(new_opponent_order).await;
            }
        }

        // 处理资产
        // let mut assetscenter_client = self.assetscenter_client.write().await;
        if let Err(err) = assetchange::place_order_asset_change(&order_detail, &self.assetscenter_client).await {
            error!("{:?}", err);
            tool::push_log(&err.to_string()).await;
            resp.error_code = 1;
            resp.error_msg = err.to_string();
            // drop(assetscenter_client);
            return Ok(resp);
        }
        // drop(assetscenter_client);

        //请求报盘
        let _ = self.place_order_to_bp(&order_detail, &new_sub_orders).await;
        let mqclient = self.mq_client.read().await;
        let _ = notificationmsg::notificationmsg(&order_detail, &new_order, &mqclient).await;
        info!("下单用时: {}, {:?}", order_detail.order_id, now.elapsed());
        Ok(resp)
    }

    pub async fn cancel_order(&self, req_cancel_order: &CancelReq) -> Result<OrderResp> {
        info!("收到撤单请求: {:?}", req_cancel_order);
        let mut cancel_order_detail = OrderDetail::cancel_order(&req_cancel_order).await;
        let mut id = self.uidgen.write().await;
        cancel_order_detail.msg_id = id.get_uid(); // 消息ID
        cancel_order_detail.cancel_id = id.get_uid();
        drop(id);

        let mut resp = OrderResp {
            msg_id: cancel_order_detail.msg_id,
            order_id: cancel_order_detail.order_id,
            error_code: 0,
            error_msg: "撤单委托成功".to_owned(),
        };

        let ret = self.check_cancel_order(&mut cancel_order_detail).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            resp.error_code = 1;
            resp.error_msg = ret.as_ref().err().unwrap().to_string();
            return Ok(resp);
        }
        let (order, mut sub_orders) = ret.unwrap();

        //设置子订单为待撤
        if let Err(err) = self.cancel_flag_update(cancel_order_detail.order_id, &mut sub_orders).await {
            error!("{:?}", err);
            resp.error_code = 1;
            resp.error_msg = err.to_string();
            return Ok(resp);
        }
        // cancel_order_detail
        cancel_order_detail.unit_id = order.unit_id;
        cancel_order_detail.user_id = order.user_id;
        cancel_order_detail.sys_date = order.sys_date;
        cancel_order_detail.order_price = order.order_price;
        cancel_order_detail.order_amount = order.order_amount;
        cancel_order_detail.deal_amount = order.deal_amount;
        cancel_order_detail.cancel_amount = order.cancel_amount;
        cancel_order_detail.cancel_status = 1;
        info!("撤单明细: {:?}", &cancel_order_detail);
        let cancel_order = convertdata::convert_to_ordcancel(&cancel_order_detail).await;
        // order.id = 0;
        // order.order_memo = format!("{}", order.order_no);//数据库怎么提现出该撤单委托对应那个订单?
        // order.order_no = cancel_order_detail.cancel_id;
        // info!("原委托:{}, 对应撤单委托:{}", cancel_order_detail.order_id, order.order_no);
        // let _ = self.tx_persist.send(PersistData::StockOrder(Box::new(order))).await;
        let _ = self.tx_persist.send(PersistData::OrdCancel(Box::new(cancel_order))).await;

        if let Err(err) = self.cancel_order_to_bp(&mut cancel_order_detail, &sub_orders).await {
            error!("{:?}", err);
            resp.error_code = 1;
            resp.error_msg = format!("{:?}", err);
            return Ok(resp);
        }
        // info!("cancel order end...");
        Ok(resp)
    }

    pub async fn replenishment_order(&self, req: &ReplenishOrderReq) -> Result<()> {
        info!("收到补订单: {:#?}", &req);
        let now = std::time::Instant::now();
        if req.order.is_none() {
            return Err(anyhow!(format!("Incomplete supplementary information")));
        }
        let order = req.to_owned().order.unwrap();

        let mut order_detail = OrderDetail::place_order(&order).await;
        let mut id = self.uidgen.write().await;
        order_detail.order_id = id.get_uid();
        order_detail.msg_id = id.get_uid();
        drop(id);

        if let Err(err) = self.aka_stock_trade_info(&mut order_detail).await {
            error!("memo: {:?}", err);
            return Err(anyhow!(err));
        }
        if !req.trade_time.is_empty() {
            let build_time = utility::timeutil::build_naive_date_time(&req.trade_time);
            order_detail.msg_time = format!("{:02}{:02}{:02}", build_time.hour(), build_time.minute(), build_time.second()).parse().unwrap_or_default();
            order_detail.create_time = utility::timeutil::get_local_timestamp_from_datetime(&build_time);
            // order_detail.create_time = build_time.timestamp() - 8 * 60 * 60;
            order_detail.deal_time = req.trade_time.to_owned();
            // order_detail.last_deal_time = format!("{}{:02}{:02}", build_time.hour(), build_time.minute(), build_time.second()).parse().unwrap_or_default();
        }

        if let Err(err) = self.deal_replenishment_order(&mut order_detail, &req.riskinfo.to_owned()).await {
            error!("{}", err);
            return Err(anyhow!(err));
        }
        info!("补单明细: {:?}", order_detail);

        //对手
        if req.to_unit_id != 0 {
            info!("补对方单....");
            let mut id = self.uidgen.write().await;
            order_detail.order_id = id.get_uid();
            order_detail.msg_id = id.get_uid();
            drop(id);
            order_detail.unit_id = req.to_unit_id;
            order_detail.user_id = req.to_user_id;

            if order.order_direction == OrderDirection::BUY as i32 {
                order_detail.order_direction = OrderDirection::SELL as i32
            } else {
                order_detail.order_direction = OrderDirection::BUY as i32
            }

            // let ret = self.account_risk_client.write().await.query_margin_ratio(order_detail.user_id, order_detail.stock_id).await;
            let ret = self.account_risk_client.query_margin_ratio(order_detail.user_id, order_detail.stock_id).await;
            if ret.as_ref().is_err() {
                error!("{}", ret.as_ref().err().unwrap());
                return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
            }
            order_detail.margin_rate = Decimal::from_f64(ret.unwrap().margin_ratio).unwrap_or_default();
            info!("保证金比例: stock: {}, 最终: {}", order_detail.stock_margin_rate, order_detail.margin_rate);

            if let Err(err) = self.deal_replenishment_order(&mut order_detail, &req.riskinfo.to_owned()).await {
                error!("{}", err);
                return Err(anyhow!(err));
            }
            info!("对方补单明细: {:?}", order_detail);
        }

        info!("补单/撮单用时: {:?}", now.elapsed());
        Ok(())
    }

    pub async fn aka_stock_trade_info(&self, order_detail: &mut OrderDetail) -> Result<()> {
        //取信息
        // let mut account_risk_client = self.account_risk_client.write().await;
        // if let Err(err) = queryinfo::get_stock_info(order_detail, &self.aka_client, &mut account_risk_client).await
        let mut account_risk_client = self.account_risk_client.clone();
        if let Err(err) = queryinfo::get_stock_info(order_detail, &self.aka_client, &mut account_risk_client).await {
            error!("memo: {:?}", err);
            // drop(account_risk_client);
            return Err(anyhow!(format!("query stock info failed")));
        }
        // drop(account_risk_client);

        //查询交易日
        let ret = queryinfo::query_trade_date(order_detail.exchange_id as i64, 0, 1, 0, &self.aka_client).await;
        if ret.as_ref().is_err() {
            error!("memo: {:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(format!("query trade date failed")));
        }
        order_detail.sys_date = ret.unwrap().target_date;
        Ok(())
    }

    pub async fn calc_fee_info(&self, detail: &mut OrderDetail) -> Result<()> {
        if let Err(err) = queryinfo::get_aka_rate(detail, &self.aka_client).await {
            error!("查询汇率出错: {:?}", err);
            return Err(anyhow!("查询汇率出错: {:?}", err));
        }
        let fee_field = FeeField {
            fee_type: detail.fee_type.clone(),
            exchange_id: detail.exchange_id as i64,
            unit_id: detail.unit_id,
            user_id: detail.user_id,
            channel_id: detail.channel_id as i64,
            stock_type: if detail.stock_type != 6 { 1 } else { detail.stock_type },
            amount: detail.order_amount,
            price: detail.order_price,
            currency_no: detail.currency_no.clone(),
            order_direction: detail.order_direction,
            rate: detail.rate,
        };
        info!("fee_calc: {:?}", fee_field);
        let ret = FeeDetail::calc_fee_info_v2(&fee_field, &self.aka_client).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let fee_detail = ret.unwrap();

        detail.fee_jy = fee_detail.fee_jy; //交易费
        detail.fee_yh = fee_detail.fee_yh; //印花税
        detail.fee_gh = fee_detail.fee_gh; //过户费
        detail.fee_yj = fee_detail.fee_yj; //佣金
        detail.fee_js = fee_detail.fee_js; //经手费
        detail.fee_zg = fee_detail.fee_zg; //证管费
        detail.fee_qt = fee_detail.fee_qt; //其他费用
        detail.fee_js2 = fee_detail.fee_js2; //结算费
        detail.fee_jg = fee_detail.fee_jg; //交割费
                                           // detail.fee_fx = fee_detail.fee_fx; //风险金
        detail.fee_total = fee_detail.fee_total;
        Ok(())
    }

    async fn risk_check(&self, order_detail: &mut OrderDetail) -> Result<Vec<PhoenixRiskCheckInfo>> {
        //风控拆单
        let risk_check_req = crate::odcommon::common::OrderDetail::convert_to_riskinfo(&order_detail).await;
        let ret = self.riskcenter_client.phoenix_risk_check(&risk_check_req).await;
        // let ret = self.riskcenter_client.write().await.phoenix_risk_check(&order_detail).await;
        if ret.as_ref().is_err() {
            error!("memo: {:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let risk_ret = ret.unwrap();
        //废单
        if risk_ret.retinfo.is_empty() {
            error!("memo: {:?}", risk_ret.ret_msg);
            order_detail.memo = risk_ret.ret_msg.to_owned();
            order_detail.order_status = OrderStatus::INVALID as i32;
            order_detail.capital = dec!(0.0);
            let failed_order = convertdata::convert_to_stockorder(&order_detail).await;
            let _ = self.tx_persist.send(PersistData::StockOrder(Box::new(failed_order))).await;
            return Err(anyhow!(risk_ret.ret_msg));
        }
        info!("拆单结果: {:?}", &risk_ret);

        order_detail.user_currency = risk_ret.retinfo.iter().last().unwrap().base_currency.to_owned();
        order_detail.margin_rate = Decimal::from_f64(risk_ret.retinfo.iter().last().unwrap().margin_ratio).unwrap_or_default();
        info!("最终保证金比例: {}", order_detail.margin_rate);
        Ok(risk_ret.retinfo)
    }

    async fn generate_order(&self, order_detail: &OrderDetail, risk_info: &Vec<PhoenixRiskCheckInfo>) -> Result<(PhoenixOrdStockorder, Vec<PhoenixOrdSuborder>)> {
        // 订单生成
        let mut order = convertdata::convert_to_stockorder(&order_detail).await;
        let mut sub_order = convertdata::convert_to_suborder(&order_detail).await;
        let mut id = self.uidgen.write().await;
        let sub_orders: Vec<PhoenixOrdSuborder> = risk_info
            .iter()
            .map(|v| {
                sub_order.sub_id = id.get_uid();
                // info!("=============={}", sub_order.sub_id);
                sub_order.channel_id = v.order_channel as i32;
                sub_order.channel_type = v.channel_type;
                sub_order.order_amount = v.order_amount;
                sub_order.relate_order = 0;
                order.trade_type = v.is_rq;
                if sub_order.channel_id == 0 {
                    sub_order.order_status = OrderStatus::INVALID as i32;
                    sub_order.remark = format!("channel未知");
                    order.order_status = OrderStatus::INVALID as i32;
                    order.order_memo = format!("channel未知");
                }
                if sub_order.channel_type == ChannelType::INTERNAL as i32 {
                    sub_order.relate_order = id.get_uid();
                }
                sub_order.clone()
            })
            .collect();
        drop(id);

        let _ = self.persist_and_update_cache(&sub_orders, &order).await;
        if sub_orders.iter().find(|x| x.channel_id == 0).is_some() {
            return Err(anyhow!(format!("channel未知")));
        }
        Ok((order, sub_orders))
    }

    pub async fn place_order_to_bp(&self, order_detail: &OrderDetail, sub_orders: &Vec<PhoenixOrdSuborder>) -> Result<()> {
        info!("place order to bp, sub_orders: {:?}", sub_orders);
        for sub_order in sub_orders.iter() {
            // if sub_order.channel_id != 0 {
            let router_msg = convertdata::convert_to_orderrouterinfo(sub_order, &order_detail).await;
            if let Err(err) = self.tx_order.send(router_msg) {
                error!("{:?}", err);
            }
            // }
        }
        Ok(())
    }

    pub async fn check_cancel_order(&self, detail: &mut OrderDetail) -> Result<(PhoenixOrdStockorder, Vec<PhoenixOrdSuborder>)> {
        let cache_key = CacheKey {
            order_id: detail.order_id,
            ..Default::default()
        };
        let ret = cache_key.query_order_info(&self.redis_client, &self.db_client).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let (sub_orders, order) = ret.unwrap();

        //判断主委托是否可以撤单[委托记录处于(1,2,3,4,6,a 未报、正报、 待报、正报、已报、部成、待撤)允许撤单]
        let order_status = vec![OrderStatus::INITED as i32, 2, 3, OrderStatus::SUBMITTED as i32, OrderStatus::PARTIALDEALED as i32];
        if order_status.iter().find(|&&v| v == order.order_status).is_none() {
            info!("订单{}状态{}不允许执行撤单", order.order_no, order.order_status);
            return Err(anyhow!("订单{}状态{}不允许执行撤单", order.order_no, order.order_status));
        }
        // for sub_order in sub_orders.iter() {
        //     if sub_order.cancel_flag == 1 {
        //         return Err(anyhow!("撤单中!"));
        //     }
        // }
        detail.stock_code = order.stock_code.to_owned(); // 证券代码
        detail.exchange_id = order.exchange_id.to_owned(); // 市场id
        detail.order_status = order.order_status;
        detail.order_direction = order.order_direction;
        return Ok((order, sub_orders));
    }

    pub async fn cancel_flag_update(&self, order_id: i64, sub_orders: &mut Vec<PhoenixOrdSuborder>) -> Result<()> {
        let cache_key = CacheKey { order_id, ..Default::default() };

        let order_status = vec![OrderStatus::INITED as i32, OrderStatus::SUBMITTED as i32, OrderStatus::PARTIALDEALED as i32];
        for sub_order in sub_orders.iter_mut() {
            if order_status.iter().find(|&&x| x == sub_order.order_status).is_some() {
                sub_order.cancel_flag = 1;
            }
        }

        let _ = self.tx_persist.send(PersistData::SubOrder(Box::new(sub_orders.to_owned()))).await;
        if let Err(err) = cache_key.update_sub_order_cache(&sub_orders, &self.redis_client).await {
            error!("{}", err);
            return Err(anyhow!(err));
        }
        Ok(())
    }

    pub async fn cancel_order_to_bp(&self, detail: &mut OrderDetail, sub_orders: &Vec<PhoenixOrdSuborder>) -> Result<()> {
        let order_status = vec![OrderStatus::INVALID as i32, OrderStatus::DEALED as i32, OrderStatus::PARTIALCANCELED as i32, OrderStatus::CANCELED as i32];
        for sub_order in sub_orders.iter() {
            info!("{:?}", sub_order);
            if sub_order.cancel_flag == 1 {
                //检查子订单状态(5：废单, 7：已成 8：部撤 9：已撤)  5789 表示最终状态, 无法撤单
                if order_status.iter().find(|&&v| v == sub_order.order_status).is_some() {
                    info!("订单{}状态{}不允许执行撤单", sub_order.sub_id, sub_order.order_status);
                    continue;
                }
                if sub_order.order_status == OrderStatus::INITED as i32 || detail.internal_cancel != 0 {
                    let mut exec_msg = ExecMsg::default();
                    if sub_order.order_status == OrderStatus::INITED as i32 {
                        info!("子订单{}未报 order_status, {}", sub_order.sub_id, sub_order.order_status);
                        exec_msg.memo = format!("INITED cancel order [Internal withdrawal]");
                    }
                    if detail.internal_cancel != 0 {
                        info!("内部撤单,子订单{} order_status, {}", sub_order.sub_id, sub_order.order_status);
                        exec_msg.memo = format!("internal cancel order [Internal withdrawal]");
                    }
                    info!("子订单{}未报 order_status, {}", sub_order.sub_id, sub_order.order_status);
                    exec_msg.order_id = detail.order_id;
                    exec_msg.channel_id = sub_order.channel_id as i64;
                    exec_msg.channel_type = sub_order.channel_type;
                    exec_msg.exec_qty = sub_order.order_amount - sub_order.deal_amount; //取消数量
                                                                                        // exec_msg.memo = format!("INITED cancel order [Internal withdrawal]");
                    let _ = self.tx_cancel.send(exec_msg).await;
                    continue;
                }
                detail.cancel_amount = sub_order.order_amount - sub_order.deal_amount; //取消数量
                let router_msg = convertdata::convert_to_orderrouterinfo(sub_order, &detail).await;
                if let Err(err) = self.tx_order.send(router_msg) {
                    error!("{:#?}", err);
                    return Err(anyhow!(err));
                }
            }
        }
        Ok(())
    }

    pub async fn generate_opponent_order(&self, detail: &mut OrderDetail) -> Result<()> {
        info!("收到反向单...{:?}", detail);
        let ret = self.aka_client.query_special_account(1).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        info!("对手账户: {:?}", ret.as_ref().unwrap());
        detail.unit_id = ret.unwrap().first().unwrap().unit_id;
        detail.user_id = detail.unit_id;
        // detail.unit_id = self.settings.system.oppaccount;
        // if detail.channel_id != 7 {//柜台账号
        //     detail.unit_id = self.settings.system.oppaccount;
        // }
        let mut id = self.uidgen.write().await;
        detail.sub_id = id.get_uid();
        drop(id);

        // let ret = self.account_risk_client.write().await.query_user_currency(detail.user_id, detail.unit_id).await;
        let ret = self.account_risk_client.query_user_currency(detail.user_id, detail.unit_id).await;
        if ret.as_ref().is_err() {
            error!("{}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        detail.user_currency = ret.unwrap();

        // 计算费用
        if let Err(err) = self.calc_fee_info(detail).await {
            error!("{:?}", err);
            //消息中心
        }
        detail.pre_fee = detail.fee_total;
        detail.capital = (Decimal::from(detail.order_amount) * detail.order_price * Decimal::from(1) + detail.fee_total) * detail.rate;
        info!("反向单明细: {:?}", detail);

        // let cache_key = CacheKey {order_id: detail.order_id, ..Default::default() };

        // 订单生成落库
        let opponent_order = convertdata::convert_to_stockorder(&detail).await;
        let opponent_sub_order = convertdata::convert_to_suborder(&detail).await;
        let _ = self.persist_and_update_cache(&vec![opponent_sub_order], &opponent_order).await;

        // if let Err(err) = PhoenixOrdStockorder::insert(&generate_order, &self.db_client).await {
        //     error!("{}", err);
        //     return Ok(())
        // }
        // if let Err(err) = cache_key.update_order_cache(&generate_order, &self.redis_client).await {
        //     error!("{}", err);
        // }

        // if let Err(err) = PhoenixOrdSuborder::insert(&sub_order, &self.db_client).await {
        //     error!("{}", err);
        //     return Ok(())
        // }
        // if let Err(err) = cache_key.update_sub_order_cache(&vec![sub_order.to_owned()], &self.redis_client).await {
        //     error!("{}", err);
        // }

        // 处理资产
        // let mut assetscenter_client = self.assetscenter_client.write().await;
        if let Err(err) = assetchange::place_order_asset_change(&detail, &self.assetscenter_client).await {
            error!("{:?}", err);
            tool::push_log(&err.to_string()).await;
        }
        // drop(assetscenter_client);

        detail.exec_type = ExecType::Confirm;
        let mqclient = self.mq_client.read().await;
        let _ = notificationmsg::notificationmsg(detail, &opponent_order, &mqclient).await;
        Ok(())
    }

    pub async fn deal_replenishment_order(&self, order_detail: &mut OrderDetail, riskinfo: &Vec<Riskinfo>) -> Result<()> {
        // let ret = self.account_risk_client.write().await.query_user_currency(order_detail.user_id, order_detail.unit_id).await;
        let ret = self.account_risk_client.query_user_currency(order_detail.user_id, order_detail.unit_id).await;
        if ret.as_ref().is_err() {
            error!("{}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        order_detail.user_currency = ret.unwrap();

        // 计算费用
        if let Err(err) = self.calc_fee_info(order_detail).await {
            error!("{:?}", err);
            return Err(anyhow!(format!("{}", err)));
        }
        order_detail.pre_fee = order_detail.fee_total;
        order_detail.capital = (Decimal::from(order_detail.order_amount) * order_detail.order_price * order_detail.margin_rate + order_detail.fee_total) * order_detail.rate;
        info!("订单明细: {:?}", &order_detail);

        //风控拆单
        let mut risk_check: Vec<PhoenixRiskCheckInfo> = Vec::new();
        if riskinfo.is_empty() {
            let ret = self.risk_check(order_detail).await;
            if ret.as_ref().is_err() {
                error!("{:?}", ret.as_ref().err().unwrap());
                return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
            }
            risk_check = ret.unwrap();
        }
        let mut risk = PhoenixRiskCheckInfo::default();
        for x in riskinfo.iter() {
            risk.order_amount = x.order_amount;
            risk.channel_type = x.channel_type;
            risk.order_channel = x.channel_id;
            risk.is_rq = 1;
            risk_check.push(risk.clone())
        }

        //生成订单
        let ret = self.generate_order(&order_detail, &risk_check).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let (replenish_order, replenish_sub_orders) = ret.unwrap();

        // 处理资产
        // let mut assetscenter_client = self.assetscenter_client.write().await;
        if let Err(err) = assetchange::place_order_asset_change(&order_detail, &self.assetscenter_client).await {
            error!("{:?}", err);
            tool::push_log(&err.to_string()).await;
        }
        // drop(assetscenter_client);

        order_detail.exec_type = ExecType::Confirm;
        let mqclient = self.mq_client.read().await;
        let _ = notificationmsg::send_order_info_to_mq(&order_detail, &replenish_order, &mqclient).await;
        let _ = notificationmsg::send_order_status_to_mq(&order_detail, &replenish_order, &mqclient).await;
        for sub_order in replenish_sub_orders.iter() {
            order_detail.order_amount = sub_order.order_amount;
            order_detail.channel_id = sub_order.channel_id;
            order_detail.channel_type = sub_order.channel_type;
            let _ = notificationmsg::send_order_exec_to_mq(&order_detail, &replenish_order, &mqclient).await;
        }

        //撮合成交
        let _ = self.convert_bp_from_order(&order_detail, &risk_check).await;
        Ok(())
    }

    pub async fn convert_bp_from_order(&self, detail: &OrderDetail, risk_infos: &Vec<PhoenixRiskCheckInfo>) {
        info!("撮合成交: {}", detail.order_id);
        let mut exec_msg = ExecMsg::default();
        exec_msg.exec_type = ExecType::Filled as i32;
        exec_msg.order_id = detail.order_id;
        exec_msg.exec_price = detail.order_price.to_f64().unwrap_or_default();
        exec_msg.exec_id = detail.exec_id.to_owned();
        exec_msg.exec_time = detail.deal_time.to_owned();
        exec_msg.order_direction = detail.order_direction;
        exec_msg.brk_order_id = detail.confirm_no.to_owned();
        exec_msg.exec_qty = detail.order_amount;
        exec_msg.channel_id = detail.channel_id as i64;
        exec_msg.channel_type = detail.channel_type;
        exec_msg.memo = detail.memo.to_owned();

        for risk in risk_infos.iter() {
            exec_msg.exec_qty = risk.order_amount;
            exec_msg.channel_type = risk.channel_type;
            exec_msg.channel_id = risk.order_channel;
            let mut id = self.uidgen.write().await;
            exec_msg.exec_id = id.get_uid().to_string();
            drop(id);

            if let Err(err) = self.tx_filled.send(exec_msg.clone()).await {
                error!("{:?}", err);
            }
        }
    }

    pub async fn confirm_order_receipt(&self, exec_msg: &ExecMsg) -> Result<()> {
        info!("开始处理委托确认: {:?}", exec_msg);
        let now = std::time::Instant::now();
        let mut detail = OrderDetail::receipt_msg(&exec_msg).await;
        let mut id = self.uidgen.write().await;
        detail.msg_id = id.get_uid();
        drop(id);

        let cache_key = CacheKey {
            order_id: detail.order_id,
            ..Default::default()
        };
        let ret = cache_key.query_order_info(&self.redis_client, &self.db_client).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let (mut sub_orders, mut order) = ret.unwrap();
        if sub_orders.is_empty() {
            error!("{}确认失败,order_status: {}, sub_orders len: {}", order.order_no, order.order_status, sub_orders.len());
            return Ok(());
        }

        let ret = sub_orders
            .iter_mut()
            .find(|x| x.order_no == detail.order_id && x.channel_id == detail.channel_id && x.channel_type == detail.channel_type);
        if ret.is_none() {
            error!("找不到当前子订单: order_id: {}, channel_id: {}, channel_type: {}", detail.order_id, detail.channel_id, detail.channel_type);
        } else {
            let sub_order = ret.unwrap();
            if sub_order.order_status == OrderStatus::INITED as i32 {
                //先成交, 后确认
                sub_order.order_status = OrderStatus::SUBMITTED as i32; //已报
                sub_order.confirm_no = detail.confirm_no.to_owned();
                sub_order.modify_time = utility::timeutil::current_timestamp();
                if order.order_no == detail.order_id && order.order_status == OrderStatus::INITED as i32 {
                    order.order_status = OrderStatus::SUBMITTED as i32;
                    order.modify_time = utility::timeutil::current_timestamp();
                }
            } else {
                info!("订单{} 状态: {}", order.order_no, order.order_status);
            }
            detail.order_amount = sub_order.order_amount;
        }

        let _ = self.persist_and_update_cache(&sub_orders, &order).await;

        info!("订单 {} 更新后: {:?}", detail.order_id, order);
        info!("委托确认处理成功: {}", detail.order_id);
        let mqclient = self.mq_client.read().await;
        let _ = notificationmsg::notificationmsg(&detail, &order, &mqclient).await;
        info!("订单{}确认用时: {:?}", order.order_no, now.elapsed());
        Ok(())
    }

    pub async fn filled_order_receipt(&self, exec_msg: &ExecMsg) -> Result<()> {
        info!("开始处理成交: {:?}", exec_msg);
        let mut detail = OrderDetail::receipt_msg(&exec_msg).await;
        let mut id = self.uidgen.write().await;
        detail.msg_id = id.get_uid();
        detail.deal_id = id.get_uid();
        detail.settle_id = id.get_uid();
        drop(id);
        //往日志中心发送
        common::logclient::log_info(&format!("成交：{:?}", exec_msg)).await;

        info!("成交明细: {:?}", &detail);
        if let Err(err) = self.begin_order_deal(&mut detail).await {
            error!("{}", err);
            return Err(err);
        }

        // 反向单处理
        if detail.relate_id > 0 && detail.channel_type == 2 {
            let mut id = self.uidgen.write().await;
            detail.order_id = detail.relate_id;
            detail.relate_id = 0;
            detail.deal_id = id.get_uid();
            detail.settle_id = id.get_uid();
            detail.exec_id = id.get_uid().to_string();
            drop(id);
            info!("反向单成交:{}, deal_id: {}, settle_id: {}, exec_id: {}", detail.order_id, detail.deal_id, detail.settle_id, detail.exec_id);
            if let Err(err) = self.begin_order_deal(&mut detail).await {
                error!("{}", err);
                return Err(err);
            }
        }
        Ok(())
    }

    pub async fn begin_order_deal(&self, detail: &mut OrderDetail) -> Result<()> {
        let now = std::time::Instant::now();
        let cache_key = CacheKey {
            order_id: detail.order_id,
            exec_no: detail.exec_id.clone(),
            ..Default::default()
        };
        if let Err(err) = cache_key.cheak_order_deal(&self.redis_client).await {
            error!("{}", err);
            return Err(err);
        }

        let ret = cache_key.query_order_info(&self.redis_client, &self.db_client).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let (mut sub_orders, mut order) = ret.unwrap();

        detail.sys_date = order.sys_date;
        detail.order_id = order.order_no;
        detail.unit_id = order.unit_id;
        detail.user_id = order.user_id;
        detail.user_currency = order.user_currency.to_owned();
        detail.order_direction = order.order_direction;
        detail.pre_fee = order.pre_fee;
        detail.order_type = order.order_type;
        detail.stock_id = order.stock_id;
        detail.stock_code = order.stock_code.to_owned();
        detail.exchange_id = order.exchange_id;
        detail.trade_type = order.trade_type;
        detail.un_deal_amount = order.order_amount - order.deal_amount - order.cancel_amount;
        detail.deal_value = Decimal::from(detail.deal_amount) * detail.deal_price;
        if order.algorithm_id.is_some() {
            detail.algorithm_id = order.algorithm_id.unwrap();
        } else {
            detail.algorithm_id = 0;
        }
        // detail.face_value = dec!(1.0);

        let ret = sub_orders
            .iter()
            .find(|sub_order| sub_order.order_no == detail.order_id && sub_order.channel_id == detail.channel_id && sub_order.channel_type == detail.channel_type); //.map(|x| x);
        if ret.is_none() {
            return Err(anyhow!("找不到子委托: order_id: {}, channel_id: {}, channel_type: {}", detail.order_id, detail.channel_id, detail.channel_type));
        }
        let sub_order = ret.unwrap();
        detail.relate_id = sub_order.relate_order;

        if order.order_amount <= 0
            || order.order_amount < order.cancel_amount + order.deal_amount + detail.deal_amount
            || sub_order.order_amount <= 0
            || sub_order.order_amount < sub_order.deal_amount + detail.deal_amount
        {
            error!(
                "order amount: {}, cancel_amount: {}, deal_amount: {}",
                order.order_amount,
                order.cancel_amount,
                order.deal_amount + detail.deal_amount
            );
            error!("sub order amount: {}, deal amount: {}", sub_order.order_amount, sub_order.deal_amount + detail.deal_amount);
            return Err(anyhow!("订单{}成交处理失败, 订单数量不符...", order.order_no));
        }

        if order.order_direction == OrderDirection::BUY as i32 || order.order_direction == OrderDirection::SELL as i32 {
            detail.channel_id = sub_order.channel_id;
            detail.channel_type = sub_order.channel_type;
            if sub_order.channel_type == ChannelType::INTERNAL as i32
                && (sub_order.order_status == OrderStatus::INVALID as i32 || sub_order.order_status == OrderStatus::PARTIALCANCELED as i32 || sub_order.order_status == OrderStatus::CANCELED as i32)
            {
                info!(
                    "子委托已撤单: order_id: {}, channel_id: {}, channel_type: {}, order_status: {}",
                    sub_order.order_no, sub_order.channel_id, sub_order.channel_type, sub_order.order_status
                );
                return Err(anyhow!(
                    "子委托已撤单: order_id: {}, channel_id: {}, channel_type: {}, order_status: {}",
                    sub_order.order_no,
                    sub_order.channel_id,
                    sub_order.channel_type,
                    sub_order.order_status
                ));
            }

            // let mut account_risk_client = self.account_risk_client.write().await;
            let mut account_risk_client = self.account_risk_client.clone();
            if let Err(err) = queryinfo::get_stock_info(detail, &self.aka_client, &mut account_risk_client).await {
                error!("查询证券信息出错: {:?}", err);
                // drop(account_risk_client);
                return Err(anyhow!("查询证券信息出错: {:?}", err));
            }
            // drop(account_risk_client);

            detail.order_amount = detail.deal_amount;
            detail.order_price = detail.deal_price;
            if let Err(err) = self.calc_fee_info(detail).await {
                error!("计算费用出错: {:?}", err);
                return Err(anyhow!("计算费用出错: {:?}", err));
            }
        }
        detail.order_amount = order.order_amount;

        // if detail.relate_id > 0 {
        //     //  取关联委托号
        //     match PhoenixOrdSuborder::query_sub_order_by_subid(detail.relate_id, &self.db_client).await {
        //         Ok(relate_order) => {
        //             detail.relate_id = relate_order.order_no;
        //         },
        //         Err(err) => {
        //             error!("{:?}", err);
        //             detail.relate_id = 0;
        //         }
        //     }
        // }
        info!("订单: {}, 关联订单: {}", detail.order_id, detail.relate_id);

        // // 计算卖收益================> 由资产算
        // if detail.order_direction == OrderDirection::SELL as i32 {
        //     if let Err(err) = self.cal_refer_profit(detail).await {
        //         error!("{:?}", err);
        //         return Err(err);
        //     }
        // }

        // 成交落库/缓存
        let stockdeal = convertdata::convert_to_stockdeal(&detail).await;
        let _ = self.tx_persist.send(PersistData::StockDeal(Box::new(stockdeal.clone()))).await;
        if let Err(err) = cache_key.insert_deal_cache(&stockdeal, &self.redis_client).await {
            error!("{:?}", err);
        }
        // if let Err(err) = PhoenixOrdStockdeal::insert(&stockdeal, &self.db_client).await {
        //     error!("{:?}", err);
        // }

        detail.capital = (detail.deal_value * detail.margin_rate + detail.fee_total) * detail.rate;
        detail.un_deal_amount = detail.un_deal_amount - detail.deal_amount;
        info!("订单未成交部分: {}", detail.un_deal_amount);
        if detail.un_deal_amount <= 0 {
            // 完全成交
            // 最后一笔将剩余的委托时的冻结都逆掉 ===>资产
            info!("订单{}委托最后一笔进入", detail.order_id);
            detail.capital = order.pre_capital;
        }

        //交收数据处理
        if let Err(err) = self.pendsettle_process(detail).await {
            error!("{}", err);
            return Err(err);
        }

        //资产调整
        // let mut assetscenter_client = self.assetscenter_client.write().await;
        if let Err(err) = assetchange::deal_order_asset_change(&detail, &self.assetscenter_client).await {
            error!("{}", err);
            tool::push_log(&err.to_string()).await;
        }
        // drop(assetscenter_client);

        if let Err(err) = self.deal_update(&detail, &mut order, &mut sub_orders).await {
            error!("{:?}", err);
            return Err(err);
        }
        let mqclient = self.mq_client.read().await;
        let _ = notificationmsg::notificationmsg(&detail, &order, &mqclient).await;
        info!("成交处理成功: {}", order.order_no);
        info!("订单{}成交处理用时: {:?}", order.order_no, now.elapsed());
        Ok(())
    }

    pub async fn pendsettle_process(&self, detail: &mut OrderDetail) -> Result<()> {
        //生成交收数据
        if let Err(err) = self.init_pend_settle_info(detail).await {
            error!("{}", err);
            return Err(err);
        }

        if let Err(err) = self.pendsettle_update_or_insert(&detail).await {
            error!("{}", err);
            return Err(err);
        }
        Ok(())
    }

    pub async fn init_pend_settle_info(&self, detail: &mut OrderDetail) -> Result<()> {
        // if detail.order_direction == OrderDirection::SELL as i32 {
        //     // 卖时需多扣费用[盈亏+费用]
        //     detail.settle_capital = detail.deal_value - detail.fee_total;
        // } else {
        //     // 买时需多收费用[费用]
        //     // 买时只会扣费用
        //     detail.settle_capital = detail.deal_value + detail.fee_total;
        // }

        // 准备结算需要的数据
        info!("根据成交回报数据{}生成对应的待交割处理数据", detail.exec_id);
        // let ret = PhoenixOmsTradeconfig::query_trade_config(detail, &self.db_client).await;
        // if ret.as_ref().is_err() {
        //     error!("{:?}", ret.as_ref().err().unwrap().to_string());
        //     return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        // }
        // let trade_config = ret.unwrap();
        // if let Err(err) = PhoenixOmsTradeconfig::isvalid_trade_config(&trade_config).await{
        //     error!("{:?}", err);
        //     return Err(err);
        // }
        // detail.clear_speed = trade_config.clear_speed.parse().unwrap_or_default();
        let mut date_offset = 1;
        if detail.exchange_id == 101 || detail.exchange_id == 102 {
            detail.clear_speed = 1;
        } else if detail.exchange_id == 103 {
            detail.clear_speed = 2; //市场103
            date_offset = 2;
        } else {
            detail.clear_speed = 2; //其他
            date_offset = 2;
        }
        // let _ = queryinfo::get_settle_date(detail, &self.redis_client).await;
        let ret = queryinfo::query_trade_date(detail.exchange_id as i64, 0, 2, date_offset, &self.aka_client).await;
        if ret.as_ref().is_err() {
            error!("交收日查询出错: {:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(format!("查询交收日出错")));
        }
        detail.settle_date = ret.unwrap().target_date;
        //计算日期信息redis?
        // 资产处理日期标识计算资产处理日期
        // if trade_config.asset_settle_date.eq(&0) {//交易日处理资产
        //     detail.asset_settle_date = detail.sys_date;
        // }  else if trade_config.asset_settle_date.eq(&1) {//交割日处理资产
        //     detail.asset_settle_date = detail.settle_date;
        // }
        detail.asset_settle_date = detail.sys_date;
        info!("交易日: {}, 交割日: {}, 资产处理: {}", detail.sys_date, detail.settle_date, detail.asset_settle_date);
        Ok(())
    }

    pub async fn pendsettle_update_or_insert(&self, detail: &OrderDetail) -> Result<()> {
        info!(
            "pendsettle order_id:{}, channel_id:{}, unit_id:{}, stock_id:{}",
            detail.order_id, detail.channel_id, detail.unit_id, detail.stock_id
        );
        let cache_key = CacheKey {
            order_id: detail.order_id,
            settle_id: detail.settle_id,
            channel_id: detail.channel_id,
            unit_id: detail.unit_id,
            ..Default::default()
        };
        let ret = cache_key.query_pendsettle_info(&self.redis_client, &self.db_client).await;
        if ret.is_none() {
            info!("未找到订单 {} 的交收信息, 将生成新的交收数据", detail.order_id);
            let model = convertdata::convert_to_pendsettle(detail).await;
            info!("生成交收数据: {:?}", &model);
            let _ = self.tx_persist.send(PersistData::PendSettle(Box::new(model.clone()))).await;
            if let Err(err) = cache_key.update_pendsettle_cache(&model, &self.redis_client).await {
                error!("update_pendsettle_cache err: {}", err);
            }
            return Ok(());
        }

        let mut model = ret.unwrap();
        info!("结算数量: {}, 成交数量: {}", model.settle_amount, detail.deal_amount);
        if detail.channel_type == ChannelType::INTERNAL as i32 {
            model.settle_internal_amount.add_assign(detail.deal_amount);
        }
        model.settle_amount.add_assign(detail.deal_amount);
        // model.settle_balance.add_assign(detail.settle_capital);
        model.settle_balance.add_assign(detail.deal_value);
        model.net_balance.add_assign(detail.deal_value);
        if !Decimal::from(model.settle_amount).is_zero() {
            model.deal_avg_price = model.net_balance / Decimal::from(model.settle_amount);
            //注意结算数量为0
        }

        model.fee_total.add_assign(detail.fee_total);
        model.fee_jy.add_assign(detail.fee_jy); //交易费
        model.fee_yh.add_assign(detail.fee_yh); //印花税
        model.fee_gh.add_assign(detail.fee_gh); //过户费
        model.fee_yj.add_assign(detail.fee_yj); //佣金
        model.fee_js.add_assign(detail.fee_js); //经手费
        model.fee_zg.add_assign(detail.fee_zg); //证管费
        model.fee_other.add_assign(detail.fee_qt); //其他费用
        if model.fee_js2.is_none() {
            //结算费
            model.fee_js2 = Some(detail.fee_js2);
        } else {
            let fee_js2 = model.fee_js2.unwrap();
            model.fee_js2 = Some(fee_js2 + detail.fee_js2);
        }
        if model.fee_jg.is_none() {
            //交割费
            model.fee_jg = Some(detail.fee_jg);
        } else {
            let fee_js2 = model.fee_jg.unwrap();
            model.fee_jg = Some(fee_js2 + detail.fee_jg);
        }
        info!("更新交收数据: {:?}", &model);
        let _ = self.tx_persist.send(PersistData::PendSettle(Box::new(model.clone()))).await;
        if let Err(err) = cache_key.update_pendsettle_cache(&model, &self.redis_client).await {
            error!("update_pendsettle_cache err: {}", err);
        }
        Ok(())
    }

    pub async fn cancel_order_receipt(&self, exec_msg: &ExecMsg) -> Result<()> {
        info!("开始处理撤单: {:?}", exec_msg);
        if exec_msg.channel_id == 0 {
            error!("撤单回报有误: {:?}", exec_msg);
            return Err(anyhow!("撤单回报有误: {:?}", exec_msg));
        }

        let mut cancel_detail = OrderDetail::receipt_msg(&exec_msg).await;
        {
            cancel_detail.msg_id = self.uidgen.write().await.get_uid();
        }

        if let Err(err) = self.begin_cancel_order(&mut cancel_detail).await {
            error!("{}", err);
            return Err(err);
        }

        if cancel_detail.relate_id > 0 {
            cancel_detail.order_id = cancel_detail.relate_id;
            cancel_detail.relate_id = 0;
            info!("撤反向单: {}", cancel_detail.order_id);
            if let Err(err) = self.begin_cancel_order(&mut cancel_detail).await {
                error!("{}", err);
                return Err(err);
            }
        }
        Ok(())
    }

    pub async fn begin_cancel_order(&self, cancel_detail: &mut OrderDetail) -> Result<()> {
        let now = std::time::Instant::now();
        let cache_key = CacheKey {
            order_id: cancel_detail.order_id,
            ..Default::default()
        };
        let ret = cache_key.query_order_info(&self.redis_client, &self.db_client).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let (mut sub_orders, mut cancel_order) = ret.unwrap();
        // if cancel_order.order_amount < cancel_order.deal_amount + cancel_order.cancel_amount + cancel_detail.cancel_amount {
        //     error!("order_amount: {}, deal_amount: {}, cancel_amount: {}, curr_cancel_amount: {}",
        //         cancel_order.order_amount, cancel_order.deal_amount, cancel_order.cancel_amount, cancel_detail.cancel_amount);
        //     return Err(anyhow!("订单撤单失败"));
        // }

        if cancel_detail.cancel_amount == 0 {
            let ret = sub_orders
                .iter()
                .find(|sub_order| sub_order.order_no == cancel_detail.order_id && sub_order.channel_id == cancel_detail.channel_id && sub_order.channel_type == cancel_detail.channel_type);
            // .map(|x| x);
            if ret.is_some() {
                let sub_order = ret.unwrap();
                cancel_detail.cancel_amount = sub_order.order_amount - sub_order.deal_amount;
                cancel_detail.relate_id = sub_order.relate_order;
                cancel_detail.order_amount = sub_order.order_amount;
                info!(
                    "子订单: order_id: {}, channel_id: {}, channel_type: {}, order_amount: {}, deal_amount: {}, cancel_amount: {}",
                    cancel_detail.order_id, cancel_detail.channel_id, cancel_detail.channel_type, sub_order.order_amount, sub_order.deal_amount, cancel_detail.cancel_amount
                );
            } else {
                error!(
                    "找不到子订单: order_id: {}, channel_id: {}, channel_type: {}",
                    cancel_detail.order_id, cancel_detail.channel_id, cancel_detail.channel_type
                );
            }
        }

        if cancel_detail.channel_type == ChannelType::EXTERNAL as i32 {
            // 内盘待测数量
            let ret = sub_orders
                .iter()
                .find(|sub_order| sub_order.order_no == cancel_detail.order_id && sub_order.channel_id == cancel_detail.channel_id && sub_order.channel_type == ChannelType::INTERNAL as i32);
            // .map(|x| x);
            if ret.is_some() {
                let sub_order = ret.unwrap();
                cancel_detail.cancel_amount += sub_order.order_amount - sub_order.deal_amount;
                info!(
                    "子订单: order_id: {}, channel_id: {}, channel_type: {}, order_amount: {}, deal_amount: {}, cancel_amount: {}",
                    cancel_detail.order_id, cancel_detail.channel_id, cancel_detail.channel_type, sub_order.order_amount, sub_order.deal_amount, cancel_detail.cancel_amount
                );
            } else {
                error!("找不到子订单: order_id: {}, channel_id: {}, channel_type: {}", cancel_detail.order_id, cancel_detail.channel_id, 2);
            }
        }

        if cancel_detail.cancel_amount <= 0 {
            info!("没有可撤数量: {}", cancel_detail.order_id);
            return Ok(());
        }

        //判断是否可以撤单
        let order_status = vec![OrderStatus::INVALID as i32, OrderStatus::DEALED as i32, OrderStatus::CANCELED as i32, OrderStatus::PARTIALCANCELED as i32];
        if order_status.iter().find(|&&v| v == cancel_order.order_status).is_some() {
            //5：废单 7：已成 8：部撤 9：已撤
            info!("订单{}状态{}[5：废单 7：已成 8：部撤 9：已撤]不允许执行撤单", cancel_order.order_no, cancel_order.order_status);
            return Err(anyhow!("订单{}状态{}[5：废单 7：已成 8：部撤 9：已撤]不允许执行撤单", cancel_order.order_no, cancel_order.order_status));
        }
        info!("待撤订单: {:#?}", &cancel_order);
        cancel_detail.unit_id = cancel_order.unit_id;
        cancel_detail.user_id = cancel_order.user_id;
        cancel_detail.user_currency = cancel_order.user_currency.to_owned();
        cancel_detail.stock_id = cancel_order.stock_id;
        cancel_detail.order_direction = cancel_order.order_direction;
        // cancel_detail.stock_code = cancel_order.stock_code.to_owned();
        // cancel_detail.exchange_id = cancel_order.exchange_id;
        cancel_detail.order_amount = cancel_order.order_amount;
        cancel_detail.un_deal_amount = cancel_order.order_amount - cancel_order.deal_amount;
        if cancel_detail.order_amount == cancel_order.cancel_amount {
            error!("订单已撤: {}", cancel_detail.order_id);
            return Err(anyhow!("订单已撤: {}", cancel_detail.order_id));
        }
        info!("委托状态: {}", cancel_order.order_status);

        // let mut account_risk_client = self.account_risk_client.write().await;
        let mut account_risk_client = self.account_risk_client.clone();
        if let Err(err) = queryinfo::get_stock_info(cancel_detail, &self.aka_client, &mut account_risk_client).await {
            error!("查询证券信息出错: {:?}", err);
            // drop(account_risk_client);
            return Err(anyhow!("查询证券信息出错: {:?}", err));
        }
        // drop(account_risk_client);

        cancel_detail.order_amount = cancel_detail.cancel_amount;
        cancel_detail.order_price = cancel_order.order_price;
        if let Err(err) = self.calc_fee_info(cancel_detail).await {
            error!("计算费用出错: {:?}", err);
            return Err(anyhow!("计算费用出错: {:?}", err));
        }
        cancel_detail.order_amount = cancel_order.order_amount;
        info!("订单: {}, 关联订单: {}", cancel_detail.order_id, cancel_detail.relate_id);

        // 计算撤单金额
        // cancel_detail.capital = (cancel_order.order_price * Decimal::from(cancel_detail.cancel_amount) + cancel_detail.fee_total) * cancel_detail.rate;
        cancel_detail.capital = (cancel_order.order_price * Decimal::from(cancel_detail.cancel_amount) * cancel_detail.margin_rate + cancel_detail.fee_total) * cancel_detail.rate;
        info!("撤单金额: {}", cancel_detail.capital);
        info!("委托数量: {}, 委托金额: {}", cancel_detail.cancel_amount, cancel_detail.capital);

        // 判断是否为最后一笔
        if cancel_detail.un_deal_amount <= cancel_detail.cancel_amount {
            cancel_detail.cancel_amount = cancel_detail.un_deal_amount;
            cancel_detail.capital = cancel_order.pre_capital;
            info!("撤单数量: {}, 撤单金额: {}", cancel_detail.cancel_amount, cancel_detail.capital);
        }

        info!("cancel_detail: {:?}", &cancel_detail);

        // 进行证券或资金冻结(通知资产调整)
        // let mut assetscenter_client = self.assetscenter_client.write().await;
        if let Err(err) = assetchange::cancel_order_asset_change(cancel_detail, &self.assetscenter_client).await {
            error!("{:?}", err);
            tool::push_log(&err.to_string()).await;
            // return Err(anyhow!(err));
        }
        // drop(assetscenter_client);

        if let Err(err) = self.cancel_update(cancel_detail, &mut cancel_order, &mut sub_orders).await {
            error!("{:?}", err);
            return Err(err);
        }
        let mqclient = self.mq_client.read().await;
        let _ = notificationmsg::notificationmsg(&cancel_detail, &cancel_order, &mqclient).await;
        info!("撤单处理成功: {}", cancel_order.order_no);
        info!("订单{}撤单用时: {:?}", cancel_order.order_no, now.elapsed());
        Ok(())
    }

    pub async fn rejected_order_receipt(&self, exec_msg: &ExecMsg) -> Result<()> {
        if exec_msg.cancel_id == 0 {
            info!("开始处理废单: {:?}", exec_msg);
            if let Err(err) = self.cancel_order_receipt(exec_msg).await {
                return Err(err);
            }
        } else {
            info!("开始处理拒绝撤单: {:?}", exec_msg);
            let mut rejected_detail = OrderDetail::receipt_msg(&exec_msg).await;
            if let Err(err) = self.begin_rejected_order(&mut rejected_detail).await {
                error!("{}", err);
                return Err(err);
            }
        }
        Ok(())
    }

    pub async fn begin_rejected_order(&self, detail: &mut OrderDetail) -> Result<()> {
        let now = std::time::Instant::now();
        let cache_key = CacheKey {
            order_id: detail.order_id,
            ..Default::default()
        };
        let ret = cache_key.query_order_info(&self.redis_client, &self.db_client).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let (mut sub_orders, mut order) = ret.unwrap();

        let ret = sub_orders
            .iter_mut()
            .find(|sub_order| sub_order.order_no == detail.order_id && sub_order.channel_id == detail.channel_id && sub_order.channel_type == detail.channel_type); //.map(|x| x);
        if ret.is_none() {
            return Err(anyhow!("找不到子委托: order_id: {}, channel_id: {}, channel_type: {}", detail.order_id, detail.channel_id, detail.channel_type));
        }
        let sub_order = ret.unwrap();

        order.order_memo = detail.memo.to_owned();
        let order_status = vec![OrderStatus::INITED as i32, OrderStatus::SUBMITTED as i32, OrderStatus::PARTIALDEALED as i32];
        if order_status.iter().find(|&&x| x == sub_order.order_status).is_some() {
            sub_order.cancel_flag = 0;
            sub_order.remark = detail.memo.to_owned();
        }

        let _ = self.tx_persist.send(PersistData::SubOrder(Box::new(sub_orders.to_owned()))).await;
        if let Err(err) = cache_key.update_sub_order_cache(&sub_orders, &self.redis_client).await {
            error!("{}", err);
            return Err(anyhow!(err));
        }

        info!("订单{}拒绝撤单用时: {:?}", detail.order_id, now.elapsed());
        Ok(())
    }

    pub async fn deal_update(&self, detail: &OrderDetail, order: &mut PhoenixOrdStockorder, sub_orders: &mut Vec<PhoenixOrdSuborder>) -> Result<()> {
        let ret = sub_orders
            .iter_mut()
            .find(|sub_order| sub_order.order_no == detail.order_id && sub_order.channel_id == detail.channel_id && sub_order.channel_type == detail.channel_type);
        // .map(|x| x);
        if ret.is_none() {
            return Err(anyhow!("找不到子订单: order_id: {}, channel_id: {}, channel_type: {}", detail.order_id, detail.channel_id, detail.channel_type));
        }
        let sub_order = ret.unwrap();

        //主
        if order.deal_amount + detail.deal_amount >= order.order_amount {
            info!("订单已成: {}, 状态(前): {}", order.order_no, order.order_status);
            order.order_status = OrderStatus::DEALED as i32; //已成
        } else if order.order_status == OrderStatus::PARTIALCANCELED as i32 || order.order_status == OrderStatus::CANCELED as i32 {
            info!("订单部撤: {}, 状态(前): {}", order.order_no, order.order_status);
            order.order_status = OrderStatus::PARTIALCANCELED as i32
        } else if order.deal_amount + order.cancel_amount + detail.deal_amount == order.order_amount && order.cancel_amount > 0 {
            info!("订单部撤: {}, 状态(前): {}", order.order_no, order.order_status);
            order.order_status = OrderStatus::PARTIALCANCELED as i32; //部撤
        } else if order.order_amount - order.deal_amount - detail.deal_amount == order.order_amount {
            info!("订单已报: {}, 状态(前): {}", order.order_no, order.order_status);
            order.order_status = OrderStatus::SUBMITTED as i32; //已报
        } else {
            info!("订单部成: {}, 状态(前):{}", order.order_no, order.order_status);
            order.order_status = OrderStatus::PARTIALDEALED as i32; //部成
        }
        order.deal_amount += detail.deal_amount;
        order.deal_value += detail.deal_value;
        // order.deal_fee += detail.deal_fee;
        // order.pre_capital.sub_assign(detail.capital);
        order.pre_capital -= detail.capital;
        order.last_deal_time = detail.last_deal_time;
        order.deal_fee += detail.fee_total;
        order.modify_time = utility::timeutil::current_timestamp();

        //子
        if sub_order.deal_amount + detail.deal_amount >= sub_order.order_amount {
            info!(
                "子订单已成 order_no: {} channel_id: {}, channel_type: {}, 状态(前): {}",
                sub_order.order_no, detail.channel_id, detail.channel_type, sub_order.order_status
            );
            sub_order.order_status = OrderStatus::DEALED as i32; //已成
        } else if sub_order.order_status == OrderStatus::PARTIALCANCELED as i32 || sub_order.order_status == OrderStatus::CANCELED as i32 {
            info!(
                "子订单部撤 order_no: {} channel_id: {}, channel_type: {}, 状态(前): {}",
                sub_order.order_no, detail.channel_id, detail.channel_type, sub_order.order_status
            );
            sub_order.order_status = OrderStatus::PARTIALCANCELED as i32; //部撤
        } else if sub_order.order_amount - sub_order.deal_amount - detail.deal_amount == sub_order.order_amount {
            info!(
                "子订单已报 order_no: {} channel_id: {}, channel_type: {}, 状态(前): {}",
                sub_order.order_no, detail.channel_id, detail.channel_type, sub_order.order_status
            );
            sub_order.order_status = OrderStatus::SUBMITTED as i32; //已报
        } else {
            info!(
                "子订单部成 order_no: {} channel_id: {}, channel_type: {}, 状态(前): {}",
                sub_order.order_no, detail.channel_id, detail.channel_type, sub_order.order_status
            );
            sub_order.order_status = OrderStatus::PARTIALDEALED as i32; //已报
        }
        sub_order.deal_amount += detail.deal_amount;
        sub_order.deal_value += detail.deal_value;
        if sub_order.confirm_no.is_empty() {
            sub_order.confirm_no = detail.confirm_no.to_owned();
        }
        sub_order.modify_time = utility::timeutil::current_timestamp();

        let _ = self.persist_and_update_cache(&sub_orders, &order).await;
        info!("订单 {} 成交, 更新后: {:?}", detail.order_id, order);
        Ok(())
    }

    pub async fn cancel_update(&self, detail: &OrderDetail, order: &mut PhoenixOrdStockorder, sub_orders: &mut Vec<PhoenixOrdSuborder>) -> Result<()> {
        let ret = sub_orders
            .iter_mut()
            .find(|sub_order| sub_order.order_no == detail.order_id && sub_order.channel_id == detail.channel_id && sub_order.channel_type == detail.channel_type);
        // .map(|x| x);
        if ret.is_none() {
            return Err(anyhow!("没有子订单: order_id: {}, channel_id: {}, channel_type: {}", detail.order_id, detail.channel_id, detail.channel_type));
        }
        let sub_order = ret.unwrap();
        //主
        if order.cancel_amount + detail.cancel_amount == order.order_amount {
            info!("订单已撤: {}, 状态(前): {}", order.order_no, order.order_status);
            order.order_status = OrderStatus::CANCELED as i32; //已撤
        } else if order.cancel_amount + order.deal_amount + detail.cancel_amount == order.order_amount {
            info!("订单部撤: {}, 状态(前): {}", order.order_no, order.order_status);
            order.order_status = OrderStatus::PARTIALCANCELED as i32; //部撤
        } else {
            info!("订单: {}, 状态(前): {}", order.order_no, order.order_status);
        }
        order.cancel_amount += detail.cancel_amount;
        order.order_memo = detail.memo.to_owned();
        order.pre_capital -= detail.capital;
        order.modify_time = utility::timeutil::current_timestamp();

        //子
        if sub_order.deal_amount == 0 {
            info!("子订单已撤: {}, 状态(前): {}", sub_order.order_no, sub_order.order_status);
            sub_order.order_status = OrderStatus::CANCELED as i32;
        } else {
            info!("子订单部撤: {}, 状态(前): {}", sub_order.order_no, sub_order.order_status);
            sub_order.order_status = OrderStatus::PARTIALCANCELED as i32; //部撤
        }
        // if sub_order.deal_amount + detail.cancel_amount == sub_order.order_amount {
        //     sub_order.cancel_flag = 0;
        // }
        if detail.exec_type == ExecType::Rejected {
            if order.order_amount == sub_order.order_amount {
                order.order_status = OrderStatus::INVALID as i32; //废单
            }
            sub_order.order_status = OrderStatus::INVALID as i32; //废单
        }
        if sub_order.confirm_no.is_empty() {
            sub_order.confirm_no = detail.confirm_no.to_owned();
        }
        sub_order.modify_time = utility::timeutil::current_timestamp();
        sub_order.remark = detail.memo.to_owned();

        let _ = self.persist_and_update_cache(&sub_orders, &order).await;
        info!("订单 {} 撤单/废单, 更新后: {:?}", detail.order_id, order);
        Ok(())
    }

    pub async fn persist_and_update_cache(&self, sub_orders: &Vec<PhoenixOrdSuborder>, order: &PhoenixOrdStockorder) {
        // info!("persist_and_update_cache......");
        if let Err(err) = self.tx_persist.send(PersistData::StockOrder(Box::new(order.to_owned()))).await {
            info!("send order persist err: {}", err);
        }
        if let Err(err) = self.tx_persist.send(PersistData::SubOrder(Box::new(sub_orders.to_owned()))).await {
            info!("send sub order persist err: {}", err);
        }

        let cache_key = CacheKey {
            order_id: order.order_no,
            ..Default::default()
        };
        if let Err(err) = cache_key.update_order_cache(&order, &self.redis_client).await {
            error!("{}", err);
        }
        if let Err(err) = cache_key.update_sub_order_cache(&sub_orders, &self.redis_client).await {
            error!("{}", err);
        }
    }

    pub async fn persist_data(&self, persist_data: &PersistData) -> Result<()> {
        let now = std::time::Instant::now();
        match persist_data {
            PersistData::StockOrder(data) => {
                info!("persist_data 订单: {:?}", &data);
                if let Err(err) = PhoenixOrdStockorder::order_update_or_insert(&data, &self.db_client).await {
                    error!("{:?}", err);
                }
            }
            PersistData::SubOrder(data) => {
                info!("persist_data 子订单: {:?}", &data);
                if let Err(err) = PhoenixOrdSuborder::sub_order_updata_insert_many(&data, &self.db_client).await {
                    error!("{:?}", err);
                }
            }
            PersistData::StockDeal(data) => {
                info!("persist_data 成交: {:?}", &data);
                if let Err(err) = PhoenixOrdStockdeal::insert(&data, &self.db_client).await {
                    error!("{:?}", err);
                }
            }
            PersistData::PendSettle(data) => {
                info!("persist_data 交收: {:?}", &data);
                if let Err(err) = PhoenixOrdPendSettle::save(&data, &self.db_client).await {
                    error!("{:?}", err);
                }
            }
            PersistData::OrdCancel(data) => {
                info!("persist_data 撤单委托: {:?}", &data);
                if let Err(err) = PhoenixOrdCancel::save(&data, &self.db_client).await {
                    error!("{:?}", err);
                }
            }
        }
        info!("persist completed, elapsed: {:?}", now.elapsed());
        Ok(())
    }

    // pub async fn cal_refer_profit(&self, detail: &mut OrderDetail) -> Result<()>{
    //     // let ret = PhoenixAstStockposition::query_position(&detail, &self.db_client).await;
    //     // if ret.as_ref().is_err() {
    //     //     error!("{:?}", ret.as_ref().err().unwrap().to_string());
    //     //     return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    //     // }
    //     // let position = ret.unwrap();

    //     let ret = assetchange::query_postion_info(detail.unit_id, detail.stock_id, &self.assetscenter_client).await;
    //     if ret.as_ref().is_err() {
    //         error!("{:?}", ret.as_ref().err().unwrap().to_string());
    //         return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    //     }
    //     let position = ret.unwrap();
    //     let mut avg_price = dec!(0.0);

    //     if !position.current_amount.is_zero() {
    //         avg_price = Decimal::from_f64(position.total_value / position.current_amount as f64).unwrap_or_default();
    //     }
    //     // (成交价 - 均价) * 数量
    //     detail.profit = Decimal::from(detail.deal_amount) * (detail.deal_price - avg_price);
    //     info!("盈亏: {}", detail.profit);
    //     Ok(())
    // }

    // pub async fn calc_fee_info(&self, detail: &mut OrderDetail) -> Result<()>{
    //     let stock_type = if detail.stock_type != 6 {
    //         1
    //     } else {
    //         detail.stock_type
    //     };
    //     info!("query_fee_setting: fee_type: {}, exchange_id: {}, order_direction: {}, unit_id: {}, channel_id: {}, stock_type: {}", detail.fee_type, detail.exchange_id, detail.order_direction, detail.unit_id, detail.channel_id, stock_type);
    //     let ret = self.aka_client
    //     .query_fee_setting(detail.fee_type.clone(), detail.exchange_id as i64, detail.order_direction, detail.unit_id, detail.channel_id as i64, stock_type).await;
    //     if ret.as_ref().is_err() {
    //         return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    //     }

    //     let fee_setting = ret.unwrap();
    //     info!("fee_setting: {:?}", fee_setting);
    //     let mut fee_detail = FeeDetail::new().await;
    //     fee_detail.amount = detail.order_amount;
    //     fee_detail.price = detail.order_price;
    //     fee_detail.order_direction = detail.order_direction;
    //     fee_detail.currency_no = detail.currency_no.to_owned();
    //     fee_detail.rate = detail.rate;

    //     if let Err(err) = FeeDetail::calc_fee_info(&mut fee_detail, &fee_setting, &self.redis_client).await {
    //         return Err(err);
    //     }
    //     detail.fee_jy = fee_detail.fee_jy; //交易费
    //     detail.fee_yh = fee_detail.fee_yh; //印花税
    //     detail.fee_gh = fee_detail.fee_gh; //过户费
    //     detail.fee_yj = fee_detail.fee_yj; //佣金
    //     detail.fee_js = fee_detail.fee_js; //经手费
    //     detail.fee_zg = fee_detail.fee_zg; //证管费
    //     detail.fee_qt = fee_detail.fee_qt; //其他费用
    //     detail.fee_js2 = fee_detail.fee_js2; //结算费
    //     detail.fee_jg = fee_detail.fee_jg; //交割费
    //     // detail.fee_fx = fee_detail.fee_fx; //风险金
    //     detail.fee_total = fee_detail.fee_total;
    //     Ok(())
    // }
}
