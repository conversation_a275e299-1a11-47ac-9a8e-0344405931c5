//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_ast_frozendetail_his")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub did: i64,
    pub sys_date: i32,
    pub unit_id: i64,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub deal_capital: Decimal,
    pub deal_date: i32,
    pub cancel_date: i32,
    pub status: i32,
    pub remarks: String,
    pub currency_no: String,
    pub busin_flag: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
