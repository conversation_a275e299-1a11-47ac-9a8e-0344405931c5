//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_wallet_cash_flow_detail")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub cash_flow_id: i64,
    pub create_date: i64,
    pub handle_name: String,
    pub data: String,
    pub step_sort: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
