use anyhow::Result;
use common::logclient::log_error;
use protoes::assetscenter::{phoenixassetscenter_client::PhoenixassetscenterClient, PhoenixassetscenterQueryRequest, PhoenixassetscenterRequest, PhoenixassetscenterResponse};
use tonic::transport::Channel;
use tracing::{error, info};
#[derive(<PERSON><PERSON>, Debug)]
pub struct AssetsCenterClient {
    pub client: Option<PhoenixassetscenterClient<Channel>>,
    pub uri: String,
}

impl AssetsCenterClient {
    pub async fn new(uri: &str) -> Self {
        let mut as_client = Self { client: None, uri: uri.to_string() };
        match PhoenixassetscenterClient::connect(uri.to_string()).await {
            Ok(client) => {
                info!("资产中心连接成功");
                as_client.client = Some(client)
            }
            Err(err) => {
                error!("connect to AssetsCenter failed: {}", err);
                log_error("connect to AssetsCenter failed").await;
            }
        }
        as_client
    }

    pub async fn init_client(&mut self) -> Result<PhoenixassetscenterClient<Channel>> {
        if let Some(client) = &self.client {
            return Ok(client.clone());
        }
        match PhoenixassetscenterClient::connect(self.uri.to_owned()).await {
            Ok(c) => {
                info!("资产中心连接成功....");
                self.client = Some(c.clone());
                Ok(c)
            }
            Err(err) => {
                error!("资产中心连接失败: {:?}", err);
                log_error(format!("connect to AssetsCenter failed: {:?}", self.uri).as_str()).await;
                Err(anyhow!("connect to AssetsCenter failed"))
            }
        }
    }

    pub async fn phoenix_assets_change(&mut self, request: &PhoenixassetscenterRequest) -> Result<PhoenixassetscenterResponse> {
        let mut client = match self.init_client().await {
            Ok(client) => client,
            Err(err) => return Err(anyhow!(err.to_string())),
        };

        match client.phoenix_assets_change(request.to_owned()).await {
            Ok(val) => Ok(val.into_inner()),
            Err(status) => {
                self.client = None;
                error!("{:?}", status);
                log_error(format!("assets center err code: {:?} message: {:?}", status.code(), status.message()).as_str()).await;
                return Err(anyhow!("assets center error"));
            }
        }
    }

    #[allow(dead_code)]
    pub async fn phoenix_assets_query(&mut self, request: &PhoenixassetscenterQueryRequest) -> Result<PhoenixassetscenterResponse> {
        let mut client = match self.init_client().await {
            Ok(client) => client,
            Err(err) => return Err(anyhow!(err.to_string())),
        };

        match client.phoenix_assets_query(request.to_owned()).await {
            Ok(val) => Ok(val.into_inner()),
            Err(status) => {
                self.client = None;
                error!("{:?}", status);
                log_error(format!("assets center err code: {:?} message: {:?}", status.code(), status.message()).as_str()).await;
                return Err(anyhow!("assets center error"));
            }
        }
    }

    // pub async fn get_conn(&self) -> PhoenixassetscenterClient<Channel>{
    //     self.client.clone()
    // }
}
