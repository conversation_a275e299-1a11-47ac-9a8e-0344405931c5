//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_stockposition_channel")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub p_date: i32,
    pub p_account_unit_id: i32,
    pub p_stock_id: i32,
    pub p_stock_code: String,
    pub p_exchange_id: i32,
    pub p_prebuy_amount: i32,
    pub p_current_amount: i32,
    pub p_frozen_amount: i32,
    pub p_frozen_amount_temp: i32,
    #[sea_orm(column_type = "Decimal(Some((16, 8)))")]
    pub p_avg_price: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 8)))")]
    pub p_avg_price_hkd: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 4)))")]
    pub p_total_value: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 4)))")]
    pub p_total_value_hkd: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 4)))")]
    pub p_last_total_value: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 4)))")]
    pub p_last_total_value_hkd: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub p_last_price: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 8)))")]
    pub p_fee_hkd: Decimal,
    pub p_channel_id: i32,
    pub p_stock_type: i32,
    #[sea_orm(column_type = "Decimal(Some((28, 6)))")]
    pub p_currency_rate: Decimal,
    #[sea_orm(column_type = "Decimal(Some((10, 4)))")]
    pub p_leverage: Decimal,
    pub p_qfii_state: i32,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
