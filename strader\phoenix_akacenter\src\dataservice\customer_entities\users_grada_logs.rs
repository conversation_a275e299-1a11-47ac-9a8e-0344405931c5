//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_grada_logs")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub user_id: i64,
    pub create_time: i64,
    pub grada_type: i8,
    #[sea_orm(column_type = "Text")]
    pub rval_data: String,
    pub grada_risk_level: i8,
    pub sar_state: i8,
    pub hit_label: String,
    pub pep_state: i8,
    pub grada_complate: i8,
    pub mark: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
