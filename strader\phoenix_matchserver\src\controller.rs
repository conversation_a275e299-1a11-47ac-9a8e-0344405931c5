use common::logclient::*;
use std::collections::HashMap;
use std::sync::Arc;
use tracing::*;

use crate::config::settings::Settings;
use anyhow::Result;
// use common::uidservice::UidgenService;
use tokio::sync::RwLock;
// use rust_decimal::{prelude::*, Decimal};
use crate::client::OrderCenterClient;
use akaclient::akaclient::AkaClient;
use chrono::{Datelike, Timelike};
use common::uidservice;
use protoes::phoenixakacenter::SpecialAccount;
use protoes::phoenixnotification::NotificationPosition;
// use utility::{constant, errors, errors::ErrorCode};
use crate::server::StockTemp;
use accountriskcenterclient::AccountRiskClient;
use protoes::phoenixaccountriskcenter::{PhoenixUserPositions, UserPositionReq};
use protoes::phoenixmatchserver::{ManualMatchReq, MatchResp};
use protoes::phoenixordercenter::{OrderReq, ReplenishOrderReq, Riskinfo};
// #[derive(Clone)]
pub struct ServerController {
    pub settings: Arc<RwLock<Settings>>,
    // pub uidsvc: Arc<RwLock<UidgenService>>,
    pub account_risk_client: AccountRiskClient,
    pub akacenterconn: AkaClient,
    pub order_client: OrderCenterClient,
    pub account_list: Vec<SpecialAccount>,
    pub in_channel_id: i64,
    pub position: Arc<RwLock<HashMap<i64, StockTemp>>>,
}

//处理业务逻辑
impl ServerController {
    #[allow(dead_code)]
    pub async fn update_configurations(&self, settings: &Settings) -> Result<()> {
        let mut wr = self.settings.write().await;
        *wr = settings.to_owned();
        Ok(())
    }

    #[allow(dead_code)]
    pub async fn print_configurations(&self) -> Result<()> {
        info!("new configurations:{:#?}", &self.settings.read().await);
        Ok(())
    }

    pub async fn add(&self, mes: &NotificationPosition) -> Result<()> {
        if self.account_list.iter().any(|f| f.unit_id == mes.unit_id) {
            let time_now = utility::timeutil::current_timestamp();

            // 尝试获取持仓信息
            let mut position_map = self.position.write().await;

            if let Some(val) = position_map.get_mut(&mes.unit_id) {
                // 更新持仓信息
                val.stock_info = mes.clone();
            } else {
                // 检查持仓数量是否小于或等于冻结数量
                if mes.current_amount > mes.temp_frozen_amount {
                    let new_stock_temp = StockTemp { timed: time_now, stock_info: mes.clone() };
                    position_map.insert(mes.unit_id, new_stock_temp);
                    // info!("添加成功");
                } else {
                    info!("持仓数量:{}小于或等于冻结数量:{},无需添加", mes.current_amount, mes.temp_frozen_amount);
                }
            }
        } else {
            warn!("未找到对应的对手账户，无法添加持仓信息");
            //log_error(&format!("未找到对应的对手账户，无法添加持仓信息:{:?}", mes)).await;
        }
        Ok(())
    }

    #[tracing::instrument(name = "内部撮合", skip(self))]
    pub async fn auto_match(&self) -> Result<()> {
        if self.position.read().await.is_empty() {
            // info!("暂时没有数据");
            return Ok(());
        }
        //跟对手账户比对
        let mut key_list = Vec::new();
        for (k, mes) in self.position.write().await.iter() {
            let time_now = utility::timeutil::current_timestamp();
            if time_now - mes.timed <= 1 {
                continue;
            }
            if mes.stock_info.current_amount < 100 {
                info!("持仓数量小于100!:{:?}", mes.stock_info.current_amount);
                continue;
            }

            info!("match account :{:?}", mes.stock_info);
            let mut user_position_req = UserPositionReq::default();
            user_position_req.unit_id = common::constant::VALUE_ALL;
            user_position_req.stock_id = mes.stock_info.stock_id;
            let price = mes.stock_info.total_value / mes.stock_info.current_amount as f64;
            //查询融券账户持仓
            // let mut acc_c = self.account_risk_client.clone();
            let account_list = self.account_risk_client.query_user_positions(&user_position_req).await;
            //查询负头寸
            if let Ok(acc_list) = account_list {
                let posit = acc_list.positions;
                let list: Vec<PhoenixUserPositions> = posit.into_iter().filter(|item| item.amount < 0).collect();
                let mut order_list: Vec<ReplenishOrderReq> = vec![];
                //没有负头寸
                let account_all = self.akacenterconn.query_special_account(protoes::phoenixakacenter::SpecialAccountType::All as i32).await;
                if account_all.as_ref().is_err() {
                    error!("查找所有特殊账号信息错误:{:?}", account_all.as_ref().unwrap_err().to_string());
                    log_error(&format!("查找所有特殊账号信息错误:{:?}", account_all.as_ref().unwrap_err().to_string())).await;
                    return Ok(());
                }
                let acc_all = account_all.as_ref().unwrap(); // 特殊账户写在redis里,一般情况下unit_id跟user_id相等
                if list.len() == 0 {
                    info!("没有查询到负头寸账户");
                } else {
                    //有负头寸
                    //优先级排序
                    info!("{:?}", list);
                    let mut unit_ids = "".to_string();
                    for val in &list {
                        unit_ids = format!("{};{}", unit_ids, val.unit_id.to_string());
                    }
                    let ids = &unit_ids[1..unit_ids.len()].to_string();
                    info!("融券账户:{:?}", &ids);
                    let mut level_list = Vec::new();
                    let mut level_map: HashMap<i32, Vec<PhoenixUserPositions>> = HashMap::new();
                    let req = self.akacenterconn.query_securities_borrow_level(ids.clone()).await;
                    if let Ok(val) = req {
                        info!("优先级数组:{:?}", val);
                        let mut total = mes.stock_info.current_amount - mes.stock_info.temp_frozen_amount;
                        let val_copy = val.clone();
                        let temp = list.clone();
                        for id in val_copy.iter() {
                            if id.status == 2 {
                                info!("该用户禁止撮合:{:?}", id);
                                continue;
                            }
                            if total <= 0 {
                                info!("剩余票数不足:{:?}", total);
                                break;
                            }
                            if !acc_all.iter().find(|x| x.unit_id == id.user_id).is_none() {
                                info!("该账号是特殊账号:{:?}", id.user_id);
                                continue;
                            }
                            if !level_list.contains(&id.level) {
                                level_list.push(id.level);
                            }
                            let req = &temp.iter().find(|x| x.unit_id == id.user_id);
                            if let Some(acc_val) = req {
                                if acc_val.amount.abs() - acc_val.prebuy_amount <= 0 {
                                    continue;
                                }
                                match level_map.get_mut(&id.level) {
                                    Some(val) => {
                                        val.push(acc_val.to_owned().to_owned());
                                        val.sort_by(|a, b| b.amount.abs().partial_cmp(&a.amount.abs()).unwrap())
                                    }
                                    None => {
                                        let vec_list = vec![acc_val.to_owned().to_owned()];
                                        let _ = level_map.insert(id.level, vec_list);
                                    }
                                }
                            }
                            info!("排序完的账户数组:{:?}", level_map);
                        }
                        //优先级
                        for key in level_list.iter() {
                            match level_map.get_mut(key) {
                                Some(val) => {
                                    for pos in val.iter() {
                                        if total < 100 {
                                            info!("当前对手账户持有:{:?}", total);
                                            break;
                                        }
                                        if pos.amount < 0 || (pos.amount.abs() < total as i64) {
                                            if pos.amount.abs() < 100 {
                                                info!("出现碎股融券账号持有:{:?}", pos.amount); //
                                                break;
                                            }
                                            let mut snow = uidservice::UidgenService::new(1, 1);
                                            let uid = snow.get_uid();
                                            let mut req = self.create_req(uid, mes.stock_info.stock_id, pos.amount.abs() as i32, price, mes.stock_info.unit_id, mes.stock_info.user_id);
                                            req.to_unit_id = pos.unit_id;
                                            req.to_user_id = pos.unit_id;
                                            if total < pos.amount.abs() as i32 {
                                                info!("当前对手账户持有:{:?},融券账号持有:{:?}", total, pos.amount); // break;
                                                if total == 0 {
                                                    break;
                                                }
                                                if total < 100 || pos.amount.abs() < 100 {
                                                    info!("出现散股,当前对手账户持有:{:?},融券账号持有:{:?}", total, pos.amount); // break;
                                                    break;
                                                } else {
                                                    let modulo_total = total % 100; //取到模数
                                                    let last_num = total - modulo_total;
                                                    let temp = req.order.as_ref().unwrap();
                                                    req.order = Some(OrderReq { order_qty: last_num, ..*temp });
                                                    req.riskinfo[0].order_amount = last_num;
                                                    total = modulo_total;
                                                    info!("当前对手账户持有:{:?}", total);
                                                    info!("生成的订单:{:#?}", req);
                                                }
                                            } else {
                                                let modulo_total = pos.amount.abs() % 100;
                                                let last_num = pos.amount.abs() - modulo_total;
                                                let temp = req.order.as_ref().unwrap();
                                                req.order = Some(OrderReq { order_qty: last_num as i32, ..*temp });
                                                req.riskinfo[0].order_amount = last_num as i32;
                                                total -= last_num as i32;
                                                info!("当前对手账户持有:{:?}", total);
                                                info!("生成的订单:{:#?}", req);
                                                // }
                                            }
                                            let naive_time = utility::timeutil::current_naive_time();
                                            let current_time = format!(
                                                "{:02}-{:02}-{:02} {:02}:{:02}:{:02}",
                                                naive_time.year(),
                                                naive_time.month(),
                                                naive_time.day(),
                                                naive_time.hour(),
                                                naive_time.minute(),
                                                naive_time.second()
                                            );
                                            req.trade_time = current_time;
                                            order_list.push(req);
                                        }
                                    }
                                }
                                None => {
                                    info!("没有找到该优先级的账户数组")
                                }
                            }
                        }

                        if order_list.len() > 0 {
                            info!("准备下单:{:?}", order_list);
                            let _ = self.post_order(order_list).await;
                            info!("撮合完毕!");
                        }
                    }
                }
            }
            key_list.push(k.to_owned());
        }
        if !key_list.is_empty() {
            for k in key_list.iter() {
                if let Some(e) = self.position.write().await.remove(k) {
                    info!("{:?}", e);
                }
            }
        }
        Ok(())
    }

    #[tracing::instrument(name = "手动撮合", skip(self))]
    pub async fn manual_match(&self, mes: &ManualMatchReq) -> Result<MatchResp> {
        let mut user_position_req = UserPositionReq::default();
        user_position_req.unit_id = mes.unit_id;
        user_position_req.stock_id = mes.stock_id;
        info!("{:#?}", mes);
        let a_c = self.account_risk_client.clone();
        let account_list = a_c.query_user_positions(&user_position_req).await;
        info!("account_list: {:?}", account_list);
        if let Ok(acc_list) = account_list {
            info!("acc_list has data");
            if acc_list.positions.is_empty() {
                info!("没有查询到持仓");
                return Ok(MatchResp {
                    msg_id: 0,
                    error_code: 0,
                    error_msg: "没有查询到持仓".to_string(),
                });
            }
            info!("查询到该用户的持仓:用户号:{:?},股票号:{:?}", mes.unit_id, mes.stock_id);
            let position = acc_list.positions.first().unwrap().to_owned();
            if position.amount <= 0 || position.amount < (mes.order_qty) as i64 {
                info!("手动撮合失败,用户持仓:{:?},对手方持仓:{:?}", position.amount, mes.order_qty);
                return Ok(MatchResp {
                    msg_id: 0,
                    error_code: 0,
                    error_msg: "数量超出持仓量或用户持仓量为0".to_string(),
                });
            }
            let mut snow = uidservice::UidgenService::new(1, 1);
            let uid = snow.get_uid();
            let mut req = self.create_req(uid, mes.stock_id, mes.order_qty, mes.order_price, mes.unit_id, mes.user_id);
            req.to_unit_id = mes.to_unit_id;
            req.to_user_id = mes.to_unit_id;

            let mut list: Vec<ReplenishOrderReq> = vec![];
            list.push(req);
            let _ = self.post_order(list).await;
            info!("撮合完毕!");
            return Ok(MatchResp::default());
        } else {
            info!("没有查询到该用户的持仓:用户号:{:?},股票号:{:?}", mes.unit_id, mes.stock_id);
        }
        info!("end");
        return Ok(MatchResp::default());
    }

    pub async fn post_order(&self, mut order_list: Vec<ReplenishOrderReq>) {
        for val in order_list.iter_mut() {
            info!("{:#?}", val);
            let mut oc = self.order_client.clone();
            let naive_time = utility::timeutil::current_naive_time();
            let current_time = format!(
                "{:02}-{:02}-{:02} {:02}:{:02}:{:02}",
                naive_time.year(),
                naive_time.month(),
                naive_time.day(),
                naive_time.hour(),
                naive_time.minute(),
                naive_time.second()
            );
            val.trade_time = current_time;
            let _ = oc.replenishment_order(&val).await;
            tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
        }
    }

    fn create_req(&self, uid: i64, stock_id: i64, order_qty: i32, price: f64, unit_id: i64, user_id: i64) -> ReplenishOrderReq {
        let mut req = ReplenishOrderReq::default();
        req.order = Some(OrderReq {
            msg_id: uid,
            unit_id: unit_id,
            stock_id: stock_id,
            order_direction: common::constant::OrderDirection::SELL as i32,
            order_qty: order_qty,
            price_type: Constant::PriceType as i32,
            order_price: price,
            operator_no: unit_id,
            order_type: Constant::OrderType as i32,
            trade_mode: Constant::TradeMode as i32,
            agent_account: unit_id,
            algorithm_id: 0,
            user_id: user_id,
        });
        req.riskinfo = vec![Riskinfo {
            channel_id: self.in_channel_id,
            channel_type: 2,
            order_amount: order_qty,
        }];
        return req;
    }
}

pub enum Constant {
    PriceType = 2,
    OrderType = 6,
    TradeMode = 1,
}
