//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_exam_checkrecord1")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub exam_id: i64,
    pub check_user_id: i64,
    pub check_date: i64,
    pub check_mark: String,
    pub check_name: String,
    pub check_state: i8,
    pub eid: i64,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
