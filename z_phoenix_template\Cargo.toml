[package]
name = "phoenix_template"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
utility = { workspace = true }
common = { workspace = true }
messagecenter = { workspace = true }
akaclient = { workspace = true }

config = { workspace = true }

serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
futures = { workspace = true }
lapin = { workspace = true, default-features = false, features = ["rustls"] }
tonic = { workspace = true }
prost = { workspace = true }
notify = { workspace = true }
rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }

anyhow = { workspace = true }
thiserror = { workspace = true }

log = { workspace = true }

[build-dependencies]
# tonic-build = { workspace = true, features = ["prost"] }
tonic-build.workspace = true
