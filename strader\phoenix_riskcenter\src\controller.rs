use crate::basicdata::mintradeunit::Mintradeunit;
use crate::client::HqCenterClient;
use crate::config::settings;
use crate::service::ordercontroller::OrderController;
use accountriskcenterclient::AccountRiskClient;
use akaclient::akaclient::{AkaCacheOption, AkaClient};
use anyhow::Result;
use common::logclient::*;
use common::*;
use protoes::hqcenter::LastPriceMsgReq;
use protoes::phoenixaccountriskcenter::{MarginRatioReq, UserAssetsReq};
use protoes::phoenixriskcenter::{PhoenixRiskCheckInfo, PhoenixRiskCheckResponse, PhoenixRiskRequest, PhoenixRiskResponse};
use protoes::{phoenixakacenter::Currency, *};
use tokio::try_join;
use tracing::*;

use rust_decimal::{prelude::*, Decimal};
use tonic::{self, Status};
// use tracing::*;
use utility::{errors, errors::ErrorCode};

// #[derive(Clone)]
pub struct RiskCenterController {
    pub order_controller: OrderController,
    // pub close_time_cache: CloseTimeCached,
    // pub settings: Settings,
    pub account_risk_client: AccountRiskClient,
    pub akacenterconn: AkaClient,
    pub hq_center_client: HqCenterClient,
}
impl RiskCenterController {
    pub async fn new(settings: &settings::Settings) -> Self {
        let mut akacache = AkaCacheOption::default();
        akacache = AkaCacheOption {
            use_cache: settings.system.cacheflag,
            mq_uri: format!("{}{}", &settings.mq.amqpaddr, &settings.notification.vhost),
            exchange: settings.notification.notification_exchanger.to_string(),
            routing_keys: settings.notification.riskcenter_routing_key.to_string(),
            ..akacache.clone()
        };
        info!("缓存开关:{:?}", &akacache);
        let hq_center_client = HqCenterClient::new(&settings.servers.hqcenterserver).await;
        let akacenterconn = AkaClient::init(settings.servers.akacenterserver.to_string(), &akacache).await;
        let account_risk_client = AccountRiskClient::init(settings.servers.accountriskserver.clone()).await;
        let order_controller = OrderController::new(&akacenterconn).await;
        Self {
            order_controller,
            account_risk_client,
            akacenterconn,
            hq_center_client,
        }
    }

    pub async fn phoenix_risk_check(&mut self, order: &mut PhoenixRiskCheckInfo) -> Result<PhoenixRiskCheckResponse, Status> {
        // info!("收到订单信息:{:?}", order);
        log_debug(&format!("收到订单信息,userid:{},direction:{},amount:{}", &order.user_id, &order.order_direction, &order.order_amount)).await;
        let check_result: Result<PhoenixRiskCheckResponse, Status>;
        let mut resp = PhoenixRiskCheckResponse {
            ret_code: errors::get_error_code(ErrorCode::CodeOk).0,
            ret_msg: errors::get_error_code(ErrorCode::CodeOk).1,
            retinfo: Vec::new(),
        };

        //先判断该券交易时间
        let tradetimecheck = AkaClient::trade_time_check(&self.akacenterconn, order.stock_id, order.order_direction).await;
        if !tradetimecheck {
            error!("品种【{}】不在交易时间段内", order.stock_id);
            log_error(&format!("品种【{}】不在交易时间段内", order.stock_id)).await;
            resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityNottradable).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityNottradable).1;
            return Ok(resp);
        }

        let user_asset_req = UserAssetsReq {
            unit_id: vec![order.unit_id],
            ..Default::default()
        };

        let mut counter_party = false;
        let accounttype = phoenixakacenter::SpecialAccountType::Counterparty as i32;

        let (marketinfo, account_info_resp, agentaccount, counter_partys, stockquota, goodsinfo) = match try_join!(
            AkaClient::query_market_info(&self.akacenterconn, order.market_id),
            self.account_risk_client.query_user_assets(&user_asset_req),
            AkaClient::query_account_info(&self.akacenterconn, order.user_id),
            AkaClient::query_special_account(&self.akacenterconn, accounttype),
            AkaClient::query_unit_stock_quota(&self.akacenterconn, order.user_id, order.stock_id),
            AkaClient::query_stock_info(&self.akacenterconn, order.stock_id),
        ) {
            Ok(res) => res,
            Err(e) => {
                error!("error: {:?}", e);
                return Err(Status::internal(format!("Internal error: {}", e)));
            }
        };

        if marketinfo.market_id != order.market_id {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeSystemErrRequest).0;
            resp.ret_msg = format!("{},市场ID{}不存在", errors::get_error_code(ErrorCode::CodeSystemErrRequest).1, order.market_id);
            log_error(&format!("{},市场ID{}不存在", errors::get_error_code(ErrorCode::CodeSystemErrRequest).1, order.market_id)).await;
            return Ok(resp);
        }
        if marketinfo.date_type == 0 {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeMarketClosed).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeMarketClosed).1;
            error!("当前市场非交易日{}", order.market_id);
            // log_error(&format!("当前市场非交易日{}", order.market_id)).await;
            return Ok(resp);
        }

        let act_ret = account_info_resp.assets.into_iter().find(|f| f.unit_id == order.unit_id);
        if act_ret.is_none() {
            error!("query_user_asset is none");
            log_error(&format!("没有找到编号为【{}】的用户资产信息", order.unit_id)).await;
            resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountNotexist).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeAccountNotexist).1;
            error!("没有找到编号为【{}】的用户资产信息", order.unit_id);
            return Ok(resp);
        }
        let account_info = act_ret.unwrap();
        if account_info.trade_state == (constant::AccountStatus::AccountNotReady as i32)
            || account_info.trade_state == (constant::AccountStatus::AccountFrozed as i32)
            || account_info.trade_state == (constant::AccountStatus::AccountOrderClosed as i32)
        {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountNottradable).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeAccountNottradable).1;
            return Ok(resp);
        }
        info!("账户信息:{:?}", &account_info);
        //验证托管账户是否正确
        if order.trade_mode == constant::TradeMode::AGENT as i32 {
            info!("验证托管账户是否正确,order.agent_account:{} == agentaccount.agent_account:{}", order.agent_account, agentaccount.agent_account);
            if order.agent_account != agentaccount.agent_account {
                resp.ret_code = errors::get_error_code(ErrorCode::CodeAgentAccountError).0;
                resp.ret_msg = errors::get_error_code(ErrorCode::CodeAgentAccountError).1;
                error!("代理账户不匹配,权限不足不能下单:{}", agentaccount.user_id);
                log_error(&format!("代理账户不匹配,权限不足不能下单:{}", agentaccount.user_id)).await;
                return Ok(resp);
            }
        }
        if goodsinfo.is_none() {
            error!("没有找到stockid为:{}的商品信息", order.stock_id);
            log_error(&format!("没有找到stockid为:{}的商品信息", order.stock_id)).await;
            resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityNotExist).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityNotExist).1;
            return Ok(resp);
        }

        let goods_info = goodsinfo.unwrap();
        info!("商品信息:{:?}", &goods_info);
        if goods_info.trade_state == constant::CommidityStatus::Closed as i32 {
            error!("商品【{}】禁止交易", order.stock_id);
            log_error(&format!("商品【{}】禁止交易", order.stock_id)).await;
            resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).1;
            return Ok(resp);
        }
        if goods_info.stock_id == 0 {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityNotExist).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityNotExist).1;
            error!("没有找到【{}】的商品信息", order.stock_id);
            log_error(&format!("没有找到【{}】的商品信息", order.stock_id)).await;
            return Ok(resp); //不能找到该商品
        }
        //对手账户信息
        if counter_partys.len() == 0 {
            error!("没有找到交易对手方账号信息");
            resp.ret_code = errors::get_error_code(ErrorCode::CodeSystemErrRequest).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeSystemErrRequest).1;
            log_error(&format!("交易对手方为空")).await;
            return Ok(resp);
        }
        let counterparty = counter_partys.iter().find(|&f| f.unit_id == order.unit_id);
        if counterparty.is_some() {
            info!("unitid是交易对手方账号:{}", order.unit_id);
            counter_party = true;
        } else {
            info!("unitid不是交易对手方账号:{}", order.unit_id);
        }

        let c = &counter_partys[0];

        let pricereq = LastPriceMsgReq {
            stock_code: goods_info.stock_code.to_owned(),
            exchange_id: order.market_id as i32,
        };
        // let margin_ratio_req = MarginRatioReq {
        //     user_id: order.user_id,
        //     stock_id: order.stock_id,
        // };
        let mut currency = String::default();
        if goods_info.trade_currency == Currency::Undef as i32 {
            currency = "Undef".to_string();
        } else if goods_info.trade_currency == Currency::Hkd as i32 {
            currency = "Hkd".to_string();
        } else if goods_info.trade_currency == Currency::Cny as i32 {
            currency = "Cny".to_string();
        } else if goods_info.trade_currency == Currency::Usd as i32 {
            currency = "Usd".to_string();
        }

        let (counter_stockquota, lastprice, rateinfo, margin) = match try_join!(
            AkaClient::query_unit_stock_quota(&self.akacenterconn, c.unit_id, order.stock_id),
            self.hq_center_client.get_last_price(&pricereq),
            AkaClient::query_exchange_rate(&self.akacenterconn, &currency, &account_info.currency),
            // self.account_risk_client.query_margin_ratio(&margin_ratio_req),
            self.account_risk_client.query_margin_ratio(order.user_id, order.stock_id),
        ) {
            Ok(res) => res,
            Err(e) => {
                error!("error: {:?}", e);
                return Err(Status::internal(format!("Internal error: {}", e)));
            }
        };
        info!("柜台持仓上下限信息:{:?}", counter_stockquota);

        let mut rate = Decimal::default();
        if order.order_direction == constant::OrderDirection::BUY as i32 {
            rate = Decimal::from_f64(rateinfo.buy_rate).unwrap_or_default();
        } else if order.order_direction == constant::OrderDirection::SELL as i32 {
            rate = Decimal::from_f64(rateinfo.sell_rate).unwrap_or_default();
        }
        let currentorder: f64 = order.order_amount as f64 * order.order_price * rate.to_f64().unwrap_or_default();
        info!("当前下单金额:{}", currentorder);

        info!("当前保证金比例:{:?}", margin);
        if order.order_direction == constant::OrderDirection::BUY as i32 {
            if (currentorder * margin.margin_ratio) > account_info.available_cash {
                // if currentorder > (account_info.real_cash - account_info.real_margin) || (currentorder * margin.margin_ratio) > (account_info.real_cash - account_info.real_margin) {
                resp.ret_code = errors::get_error_code(ErrorCode::CodeRealCashError).0;
                resp.ret_msg = errors::get_error_code(ErrorCode::CodeRealCashError).1;
                error!("可用资金不足{}", currentorder);
                common::logclient::log_error(&format!("可用资金不足{}", currentorder)).await;
                // LogClient::get().push_error(&format!("可用资金不足{}", currentorder)).await;
                return Ok(resp);
            }

            //验证账户创业板限制 (创业板维持保证金(持仓)+开仓保证金(下单))/当前本金(净资产) < 创业板上限 可下单
            if goods_info.stock_type == common::constant::StockType::HSCY as i32 || goods_info.stock_type == common::constant::StockType::HSKC as i32 {
                if account_info.total_asset != 0.0 {
                    let gem_limit = ((account_info.gem_margin_frozen_capital + account_info.gem_trade_frozen_capital) + (currentorder * margin.margin_ratio)) / account_info.total_asset;
                    info!("此次下单的创业板比例:{}", gem_limit);
                    if gem_limit > agentaccount.gem_limit {
                        resp.ret_code = errors::get_error_code(ErrorCode::CodeGemPositionError).0;
                        resp.ret_msg = errors::get_error_code(ErrorCode::CodeGemPositionError).1;
                        error!("本次下单已达创业板上限{}", agentaccount.user_id);
                        common::logclient::log_error(&format!("本次下单已达创业板上限{}", agentaccount.user_id)).await;
                        // if let Ok(log_client) = LogClient::get() {
                        //   log_client.push_error(&format!("本次下单已达创业板上限{}", agentaccount.unit_id)).await;
                        // }
                        return Ok(resp);
                    }
                }
            }
            //杠杆倍数不为0时判断风险率和预警线，账户风险率=下单金额*保证金比例+原有的维持保证金/当前本金
            if account_info.level_num != 0.00 {
                let risk_rate = (currentorder * margin.margin_ratio + account_info.real_margin) / account_info.total_asset;
                info!("此次下单账户风险率 {:?}", risk_rate);
                if risk_rate != 0.00 {
                    if risk_rate >= agentaccount.warning_line {
                        //0和0相等情况不限制
                        resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountRiskRateHigh).0;
                        resp.ret_msg = errors::get_error_code(ErrorCode::CodeAccountRiskRateHigh).1;
                        error!("账户风险率已达到预警线{}", risk_rate);

                        common::logclient::log_error(&format!("账户风险率已达到预警线{}", risk_rate)).await;
                        // if let Ok(log_client) = LogClient::get() {
                        //   log_client.push_error(&format!("账户风险率已达到预警线{}", account_info.risk_rate)).await;
                        // }
                        return Ok(resp);
                    }
                }
            }
        }
        //最新价
        let priceinfo = lastprice.data;
        if priceinfo.is_none() {
            error!("未取到最新价");
            log_error(&format!("未取到最新价")).await;
        }
        let price = priceinfo.unwrap_or_default();
        let is_tradeunit = Mintradeunit::is_correspond_min_trade_unit(price.last_price, order.order_price, goods_info.stock_type);
        if !is_tradeunit {
            //下单价最小变动单位错误
            resp.ret_code = errors::get_error_code(ErrorCode::CodeMinTradeUnitError).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeMinTradeUnitError).1;
            error!("下单价{:?}最小变动单位错误:{:?},价格无效", order.order_price, price.last_price);
            log_error(&format!("下单价{:?}最小变动单位错误:{:?},价格无效", order.order_price, price.last_price)).await;
            return Ok(resp);
        }

        //单笔最小数量
        let min_number = match goods_info.min_value {
            0 => {
                if goods_info.stock_type == constant::StockType::HSKC as i32 || goods_info.stock_type == constant::StockType::HSCY as i32 {
                    200
                } else if goods_info.stock_type == constant::StockType::GANGGU as i32 {
                    //港股
                    100
                } else if goods_info.stock_type == constant::StockType::MEIGU as i32 {
                    1
                } else {
                    100
                }
            }
            _ => goods_info.min_value,
        };

        //单笔最大数量
        let max_number = match goods_info.max_value {
            0 => {
                if goods_info.stock_type == constant::StockType::HSKC as i32 || goods_info.stock_type == constant::StockType::HSCY as i32 {
                    100000
                } else if goods_info.stock_type == constant::StockType::GANGGU as i32 {
                    2000000
                } else if goods_info.stock_type == constant::StockType::MEIGU as i32 {
                    10000000
                } else {
                    300000
                }
            }
            _ => goods_info.max_value,
        };

        let max_money: Decimal; // = Decimal::from(0);

        //单笔最大金额
        if goods_info.max_single_money == 0.0 {
            if goods_info.stock_type == constant::StockType::HSKC as i32 || goods_info.stock_type == constant::StockType::HSCY as i32 {
                max_money = Decimal::from(2000000 as i64);
            } else if goods_info.stock_type == constant::StockType::GANGGU as i32 {
                max_money = Decimal::from(20000000 as i64);
            } else if goods_info.stock_type == constant::StockType::MEIGU as i32 {
                max_money = Decimal::from(10000000 as i64);
            } else {
                max_money = Decimal::from(3000000 as i64);
            }
        } else {
            max_money = Decimal::from(goods_info.max_single_money as i64);
        }

        if let Err(e) = &goods_info.hands_num.parse::<i32>() {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityNotExist).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityNotExist).1;
            error!("商品信息【{}】数据错误，每手数量不是有效得整数:{:?}", order.stock_id, &e);
            log_error(&format!("商品信息【{}】数据错误，每手数量不是有效得整数:{:?}", order.stock_id, &e)).await;
            // if let Ok(log_client) = LogClient::get() {
            //     log_client.push_error(&format!("商品信息【{}】数据错误，每手数量不是有效得整数:{:?}", order.stock_id, &e)).await;
            // }
            return Ok(resp); //不能找到该商品
        }

        //非管理员平仓，需要判断最大量和最大金额，不管买卖
        if order.order_type != constant::OrderType::ADMINCLEAR2 as i32 && order.order_type != constant::OrderType::ADMINCLEAR as i32 {
            if order.order_amount > max_number {
                resp.ret_code = errors::get_error_code(ErrorCode::CodeOrderExceedMaxNumber).0;
                resp.ret_msg = format!("{},最大数量是:{}", errors::get_error_code(ErrorCode::CodeOrderExceedMaxNumber).1, max_number);
                // error!("没有找到【{}】的商品信息", order.stock_id);
                return Ok(resp); //不能找到该商品
            }

            let total_money = rate * Decimal::from_f64(order.order_price).unwrap_or_default() * Decimal::from_i32(order.order_amount).unwrap_or_default();
            if total_money > max_money {
                resp.ret_code = errors::get_error_code(ErrorCode::CodeOrderExceedMaxMoney).0;
                resp.ret_msg = format!("{},最大金额是:{}", errors::get_error_code(ErrorCode::CodeOrderExceedMaxMoney).1, max_money);
                // error!("没有找到【{}】的商品信息", order.stock_id);
                return Ok(resp); //不能找到该商品
            }
        }

        // info!("开始处理根据买卖方向，分配订单得通道信息......");
        if order.order_direction == constant::OrderDirection::BUY as i32 {
            if order.order_amount < min_number {
                resp.ret_code = errors::get_error_code(ErrorCode::CodeOrderExceedMinNumber).0;
                resp.ret_msg = format!("{},最小数量是:{}", errors::get_error_code(ErrorCode::CodeOrderExceedMinNumber).1, min_number);
                return Ok(resp); //不能找到该商品
            }

            //处理买单
            if account_info.trade_state == (constant::AccountStatus::AccountBuyClosed as i32) {
                resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountBuyClosed).0;
                resp.ret_msg = errors::get_error_code(ErrorCode::CodeAccountBuyClosed).1;
                error!("账户【{}】禁止开仓", order.unit_id);
                log_error(&format!("账户【{}】禁止开仓", order.unit_id)).await;
                return Ok(resp);
            }

            if goods_info.trade_state == constant::CommidityStatus::SellOnly as i32 {
                error!("商品[{}]:[{}]只能平仓,不能下买单", order.stock_id, &goods_info.stock_name);
                log_error(&format!("商品[{}]:[{}]只能平仓,不能下买单", order.stock_id, &goods_info.stock_name)).await;
                resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).0;
                resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).1;
                // if let Ok(log_client) = LogClient::get() {
                //     log_client.push_error(&format!("商品[{}]:[{}]只能平仓,不能下买单", order.stock_id, &goods_info.stock_name)).await;
                // }
                return Ok(resp);
            }
            check_result = self
                .order_controller
                .phoenix_handle_buy_order(
                    order,
                    &account_info,
                    &marketinfo,
                    &self.akacenterconn,
                    &counter_partys,
                    &mut self.account_risk_client,
                    &stockquota,
                    &counter_stockquota,
                    margin.margin_ratio,
                    &agentaccount,
                )
                .await;
        } else {
            //处理卖单
            check_result = self
                .order_controller
                .phoenix_handle_sell_order(
                    order,
                    &account_info,
                    &marketinfo,
                    &self.akacenterconn,
                    &goods_info,
                    &mut self.account_risk_client,
                    counter_party,
                    &counter_partys,
                    &stockquota,
                    &counter_stockquota,
                    margin.margin_ratio,
                )
                .await;
        }

        log_debug(&format!("风控处理完成，交易通道:{:?}", &check_result)).await;
        info!("====处理获取通道的请求完成,结果:{:?}", &check_result);

        check_result
    }

    pub async fn _phoenix_risk_check_back(&mut self, order: &mut PhoenixRiskCheckInfo) -> Result<PhoenixRiskCheckResponse, Status> {
        info!("收到订单信息:{:?}", order);
        log_debug(&format!("收到订单信息:{:?}", order)).await;
        let ret: Result<PhoenixRiskCheckResponse, Status>;
        let mut resp = PhoenixRiskCheckResponse {
            ret_code: errors::get_error_code(ErrorCode::CodeOk).0,
            ret_msg: errors::get_error_code(ErrorCode::CodeOk).1,
            retinfo: Vec::new(),
        };
        //根据市场ID，获取市场信息
        let marketinfo_ret = AkaClient::query_market_info(&self.akacenterconn, order.market_id).await;
        if marketinfo_ret.as_ref().is_err() {
            error!("获取市场信息信息error:{:?}", marketinfo_ret.as_ref().err().unwrap().to_string());
            log_error(&format!("获取市场信息信息error:{:?}", marketinfo_ret.as_ref().err().unwrap().to_string())).await;

            resp.ret_code = errors::get_error_code(ErrorCode::CodeSystemErrRequest).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeSystemErrRequest).1;
            return Ok(resp);
        }
        let marketinfo = marketinfo_ret.unwrap();
        // info!("市场信息:{:?}", &marketinfo);
        if marketinfo.market_id != order.market_id {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeSystemErrRequest).0;
            resp.ret_msg = format!("{},市场ID{}不存在", errors::get_error_code(ErrorCode::CodeSystemErrRequest).1, order.market_id);
            log_error(&format!("{},市场ID{}不存在", errors::get_error_code(ErrorCode::CodeSystemErrRequest).1, order.market_id)).await;
            return Ok(resp);
        }
        if marketinfo.date_type == 0 {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeMarketClosed).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeMarketClosed).1;
            error!("当前市场非交易日{}", order.market_id);
            // log_error(&format!("当前市场非交易日{}", order.market_id)).await;
            return Ok(resp);
        }

        // order: &mut PhoenixRiskCheckInfo
        /* 1. 如果用户状态无效，则返回*/
        //帐号状态 '0:待配置, 1: 正常交易 ，2:禁止开仓， 3:禁止交易(只读)，4:账号冻结(禁用)'
        // let unitids: Vec<i64> = vec![order.unit_id];
        // unitids.push(order.unit_id);
        // let mut user_asset_req = UserAssetsReq::default();
        // user_asset_req.unit_id = vec![order.unit_id];

        let user_asset_req = UserAssetsReq {
            unit_id: vec![order.unit_id],
            ..Default::default()
        };

        let account_info = match self.account_risk_client.query_user_assets(&user_asset_req).await {
            Ok(account_info) => {
                let ret = account_info.assets.into_iter().find(|f| f.unit_id == order.unit_id);
                if ret.is_none() {
                    error!("query_user_asset is none");
                    log_error(&format!("没有找到编号为【{}】的用户资产信息", order.unit_id)).await;
                    resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountNotexist).0;
                    resp.ret_msg = errors::get_error_code(ErrorCode::CodeAccountNotexist).1;
                    error!("没有找到编号为【{}】的用户资产信息", order.unit_id);
                    return Ok(resp);
                }
                let account = ret.unwrap();
                if account.trade_state == (constant::AccountStatus::AccountNotReady as i32)
                    || account.trade_state == (constant::AccountStatus::AccountFrozed as i32)
                    || account.trade_state == (constant::AccountStatus::AccountOrderClosed as i32)
                {
                    resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountNottradable).0;
                    resp.ret_msg = errors::get_error_code(ErrorCode::CodeAccountNottradable).1;
                    return Ok(resp);
                }
                account
            }
            Err(err) => {
                error!("query_user_asset error: {}", &err);
                resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountNotexist).0;
                resp.ret_msg = errors::get_error_code(ErrorCode::CodeAccountNotexist).1;
                error!("没有找到编号为【{}】的用户资产信息，错误信息:{}", order.unit_id, &err);
                return Ok(resp);
            }
        };

        info!("接口返回的账户风控信息:{:?}", &account_info);
        let agentret = AkaClient::query_account_info(&self.akacenterconn, order.user_id).await;
        if agentret.as_ref().is_err() {
            error!("查找托管账户信息错误:{:?}", agentret.as_ref().err().unwrap());
            resp.ret_code = errors::get_error_code(ErrorCode::CodeAgentAccountError).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeAgentAccountError).1;
            log_error(&format!("代理账户不匹配,查找托管账户信息错误:{:?}", agentret.as_ref().err().unwrap())).await;
            return Ok(resp);
        }
        let agentaccount = agentret.unwrap();
        info!("接口返回的账户信息:{:?}", &agentaccount);
        //验证托管账户是否正确
        if order.trade_mode == constant::TradeMode::AGENT as i32 {
            info!("验证托管账户是否正确,order.agent_account:{} == agentaccount.agent_account:{}", order.agent_account, agentaccount.agent_account);
            if order.agent_account != agentaccount.agent_account {
                resp.ret_code = errors::get_error_code(ErrorCode::CodeAgentAccountError).0;
                resp.ret_msg = errors::get_error_code(ErrorCode::CodeAgentAccountError).1;
                error!("代理账户不匹配,权限不足不能下单:{}", agentaccount.user_id);
                log_error(&format!("代理账户不匹配,权限不足不能下单:{}", agentaccount.user_id)).await;
                return Ok(resp);
            }
        }

        //验证unitid是否是交易对手方账号
        let mut counter_party = false;
        let accounttype = phoenixakacenter::SpecialAccountType::Counterparty as i32;
        let counter_partys_ret = AkaClient::query_special_account(&self.akacenterconn, accounttype).await;
        if counter_partys_ret.as_ref().is_err() {
            error!("查找交易对手方信息错误:{:?}", counter_partys_ret.as_ref().err().unwrap());
            resp.ret_code = errors::get_error_code(ErrorCode::CodeSystemErrRequest).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeSystemErrRequest).1;
            log_error(&format!("查找交易对手方信息错误:{:?}", counter_partys_ret.as_ref().err().unwrap())).await;
            return Ok(resp);
        }
        let counter_partys = counter_partys_ret.as_ref().unwrap();
        if counter_partys.len() == 0 {
            error!("没有找到交易对手方账号信息");
            resp.ret_code = errors::get_error_code(ErrorCode::CodeSystemErrRequest).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeSystemErrRequest).1;
            log_error(&format!("交易对手方为空")).await;
            return Ok(resp);
        }
        let counterparty = counter_partys.iter().find(|&f| f.unit_id == order.unit_id);
        if counterparty.is_some() {
            info!("unitid是交易对手方账号:{}", order.unit_id);
            counter_party = true;
        } else {
            info!("unitid不是交易对手方账号:{}", order.unit_id);
        }

        let c = &counter_partys[0];
        //查询柜台持仓限制
        let counter_stockquotaret = AkaClient::query_unit_stock_quota(&self.akacenterconn, c.unit_id, order.stock_id).await;
        if counter_stockquotaret.as_ref().is_err() {
            error!("查找柜台持仓上下限错误:{:?}", counter_stockquotaret.as_ref().err().unwrap());
            resp.ret_code = errors::get_error_code(ErrorCode::CodeNotFound).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeNotFound).1;
            log_error(&format!("查找柜台持仓上下限错误:{},{}", order.unit_id, counter_stockquotaret.as_ref().unwrap_err())).await;
            return Ok(resp);
        }
        let counter_stockquota = counter_stockquotaret.unwrap();
        info!("接口返回的柜台持仓上下限信息:{:?}", counter_stockquota);

        //查询用户持仓限制
        let stockquotaret = AkaClient::query_unit_stock_quota(&self.akacenterconn, order.user_id, order.stock_id).await;
        if stockquotaret.as_ref().is_err() {
            error!("查找账户持仓上下限错误:{:?}", stockquotaret.as_ref().err().unwrap());
            resp.ret_code = errors::get_error_code(ErrorCode::CodeNotFound).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeNotFound).1;
            if let Ok(log_client) = LogClient::get() {
                log_client.push_error(&format!("查找账户持仓上下限错误:{}", order.unit_id)).await;
            }
            return Ok(resp);
        }
        let stockquota = stockquotaret.unwrap();
        info!("接口返回的账户持仓上下限信息:{:?}", stockquota);
        /* 2. 如果该商品禁止交易，则返回错误*/
        // let mut commidity_req = StockInfoReq::default();
        // commidity_req.stock_id = order.stock_id;
        let goods_info_ret = AkaClient::query_stock_info(&self.akacenterconn, order.stock_id).await;
        if goods_info_ret.as_ref().is_err() {
            error!("没有找到商品信息:{:?}", goods_info_ret.as_ref().err().unwrap());
            resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityNotExist).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityNotExist).1;
            log_error(&format!("没有找到商品信息:{:?}", goods_info_ret.as_ref().err().unwrap())).await;
            return Ok(resp);
        }
        let goodsinfo = goods_info_ret.as_ref().unwrap();
        if goodsinfo.is_none() {
            error!("没有找到商品信息:{:?}", goods_info_ret.as_ref().err().unwrap());
            log_error(&format!("没有找到商品信息:{:?}", goods_info_ret.as_ref().err().unwrap())).await;
            resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityNotExist).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityNotExist).1;
            return Ok(resp);
        }
        let goods_info = goodsinfo.clone().unwrap();
        if goods_info.trade_state == constant::CommidityStatus::Closed as i32 {
            error!("商品【{}】禁止交易", order.stock_id);
            log_error(&format!("商品【{}】禁止交易", order.stock_id)).await;
            resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).1;
            return Ok(resp);
        }
        if goods_info.stock_id == 0 {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityNotExist).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityNotExist).1;
            error!("没有找到【{}】的商品信息", order.stock_id);
            log_error(&format!("没有找到【{}】的商品信息", order.stock_id)).await;
            return Ok(resp); //不能找到该商品
        }
        info!("接口返回的商品信息:{:?}", &goods_info);
        let mut currency = String::default();
        if goods_info.trade_currency == Currency::Undef as i32 {
            currency = "Undef".to_string();
        } else if goods_info.trade_currency == Currency::Hkd as i32 {
            currency = "Hkd".to_string();
        } else if goods_info.trade_currency == Currency::Cny as i32 {
            currency = "Cny".to_string();
        } else if goods_info.trade_currency == Currency::Usd as i32 {
            currency = "Usd".to_string();
        }
        let rate_info = AkaClient::query_exchange_rate(&self.akacenterconn, &currency, &account_info.currency).await;
        if rate_info.as_ref().is_err() {
            error!("查找汇率错误:{:?}", rate_info.as_ref().err().unwrap());
            log_error(&format!("查找汇率错误:{:?}", rate_info.as_ref().err().unwrap())).await;
            resp.ret_code = errors::get_error_code(ErrorCode::CodeOrderExceedMaxMoney).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeOrderExceedMaxMoney).1;
            // if let Ok(log_client) = LogClient::get() {
            //     log_client.push_error(&format!("查找汇率错误:{:?}", currency)).await;
            // }
            return Ok(resp);
        }
        let rateinfo = rate_info.unwrap();
        let mut rate = Decimal::default();
        if order.order_direction == constant::OrderDirection::BUY as i32 {
            rate = Decimal::from_f64(rateinfo.buy_rate).unwrap_or_default();
        } else if order.order_direction == constant::OrderDirection::SELL as i32 {
            rate = Decimal::from_f64(rateinfo.sell_rate).unwrap_or_default();
        }
        info!("当前汇率:{:?}", rate);
        //查询保证金比例
        let currentorder: f64 = order.order_amount as f64 * order.order_price * rate.to_f64().unwrap_or_default();
        info!("当前下单金额:{}", currentorder);
        let mut margin_ratio_req = MarginRatioReq::default();
        margin_ratio_req.user_id = order.user_id;
        margin_ratio_req.stock_id = order.stock_id;
        let margin_ratioret = self.account_risk_client.query_margin_ratio(order.user_id, order.stock_id).await;
        let margin = margin_ratioret.unwrap();
        info!("当前保证金比例:{:?}", margin);
        if order.order_direction == constant::OrderDirection::BUY as i32 {
            if (currentorder * margin.margin_ratio) > account_info.available_cash {
                // if currentorder > (account_info.real_cash - account_info.real_margin) || (currentorder * margin.margin_ratio) > (account_info.real_cash - account_info.real_margin) {
                resp.ret_code = errors::get_error_code(ErrorCode::CodeRealCashError).0;
                resp.ret_msg = errors::get_error_code(ErrorCode::CodeRealCashError).1;
                error!("可用资金不足{}", currentorder);
                common::logclient::log_error(&format!("可用资金不足{}", currentorder)).await;
                // LogClient::get().push_error(&format!("可用资金不足{}", currentorder)).await;
                return Ok(resp);
            }

            //验证账户创业板限制 (创业板维持保证金(持仓)+开仓保证金(下单))/当前本金(净资产) < 创业板上限 可下单
            if goods_info.stock_type == common::constant::StockType::HSCY as i32 || goods_info.stock_type == common::constant::StockType::HSKC as i32 {
                if account_info.total_asset != 0.0 {
                    let gem_limit = ((account_info.gem_margin_frozen_capital + account_info.gem_trade_frozen_capital) + (currentorder * margin.margin_ratio)) / account_info.total_asset;
                    info!("此次下单的创业板比例:{}", gem_limit);
                    if gem_limit > agentaccount.gem_limit {
                        resp.ret_code = errors::get_error_code(ErrorCode::CodeGemPositionError).0;
                        resp.ret_msg = errors::get_error_code(ErrorCode::CodeGemPositionError).1;
                        error!("本次下单已达创业板上限{}", agentaccount.user_id);
                        common::logclient::log_error(&format!("本次下单已达创业板上限{}", agentaccount.user_id)).await;
                        // if let Ok(log_client) = LogClient::get() {
                        //   log_client.push_error(&format!("本次下单已达创业板上限{}", agentaccount.unit_id)).await;
                        // }
                        return Ok(resp);
                    }
                }
            }
            //杠杆倍数不为0时判断风险率和预警线，账户风险率=下单金额*保证金比例+原有的维持保证金/当前本金
            if account_info.level_num != 0.00 {
                let risk_rate = (currentorder * margin.margin_ratio + account_info.real_margin) / account_info.total_asset;
                info!("此次下单账户风险率{:?}", risk_rate);
                if risk_rate != 0.00 {
                    if risk_rate >= agentaccount.warning_line {
                        //0和0相等情况不限制
                        resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountRiskRateHigh).0;
                        resp.ret_msg = errors::get_error_code(ErrorCode::CodeAccountRiskRateHigh).1;
                        error!("账户风险率已达到预警线{}", risk_rate);

                        common::logclient::log_error(&format!("账户风险率已达到预警线{}", risk_rate)).await;
                        // if let Ok(log_client) = LogClient::get() {
                        //   log_client.push_error(&format!("账户风险率已达到预警线{}", account_info.risk_rate)).await;
                        // }
                        return Ok(resp);
                    }
                }
            }
        }

        //判断该券交易时间
        let tradetimecheck = AkaClient::trade_time_check(&self.akacenterconn, order.stock_id, order.order_direction).await;
        if !tradetimecheck {
            error!("品种【{}】不在交易时间段内", order.stock_id);
            log_error(&format!("品种【{}】不在交易时间段内", order.stock_id)).await;
            resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityNottradable).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityNottradable).1;
            // if let Ok(log_client) = LogClient::get() {
            //     log_client.push_error(&format!("品种【{}】不在交易时间段内", order.stock_id)).await;
            // }
            return Ok(resp);
        }

        //取最新价判断下单加最小变动单位
        let mut pricereq = LastPriceMsgReq::default();
        pricereq.stock_code = goods_info.stock_code.to_owned();
        pricereq.exchange_id = order.market_id as i32;
        let lastprice = self.hq_center_client.get_last_price(&pricereq).await;
        if lastprice.as_ref().is_err() {
            error!("获取最新价错误:{}", lastprice.as_ref().err().unwrap());
            log_error(&format!("获取最新价错误:{}", lastprice.as_ref().err().unwrap())).await;

            resp.ret_code = errors::get_error_code(ErrorCode::CodeMinTradeUnitError).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeMinTradeUnitError).1;
            return Ok(resp);
        }
        let priceinfo = lastprice.unwrap().data;
        if priceinfo.is_none() {
            error!("未取到最新价");
            log_error(&format!("未取到最新价")).await;
        }
        let price = priceinfo.unwrap_or_default();
        let is_tradeunit = Mintradeunit::is_correspond_min_trade_unit(price.last_price, order.order_price, goods_info.stock_type);
        if !is_tradeunit {
            //下单价最小变动单位错误
            resp.ret_code = errors::get_error_code(ErrorCode::CodeMinTradeUnitError).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeMinTradeUnitError).1;
            error!("下单价{:?}最小变动单位错误:{:?},价格无效", order.order_price, price.last_price);
            log_error(&format!("下单价{:?}最小变动单位错误:{:?},价格无效", order.order_price, price.last_price)).await;
            return Ok(resp);
        }

        //单笔最小数量
        let min_number = match goods_info.min_value {
            0 => {
                if goods_info.stock_type == constant::StockType::HSKC as i32 || goods_info.stock_type == constant::StockType::HSCY as i32 {
                    200
                } else if goods_info.stock_type == constant::StockType::GANGGU as i32 {
                    //港股
                    100
                } else if goods_info.stock_type == constant::StockType::MEIGU as i32 {
                    1
                } else {
                    100
                }
            }
            _ => goods_info.min_value,
        };

        //单笔最大数量
        let max_number = match goods_info.max_value {
            0 => {
                if goods_info.stock_type == constant::StockType::HSKC as i32 || goods_info.stock_type == constant::StockType::HSCY as i32 {
                    100000
                } else if goods_info.stock_type == constant::StockType::GANGGU as i32 {
                    2000000
                } else if goods_info.stock_type == constant::StockType::MEIGU as i32 {
                    10000000
                } else {
                    300000
                }
            }
            _ => goods_info.max_value,
        };

        let max_money: Decimal; // = Decimal::from(0);

        //单笔最大金额
        if goods_info.max_single_money == 0.0 {
            if goods_info.stock_type == constant::StockType::HSKC as i32 || goods_info.stock_type == constant::StockType::HSCY as i32 {
                max_money = Decimal::from(2000000 as i64);
            } else if goods_info.stock_type == constant::StockType::GANGGU as i32 {
                max_money = Decimal::from(20000000 as i64);
            } else if goods_info.stock_type == constant::StockType::MEIGU as i32 {
                max_money = Decimal::from(10000000 as i64);
            } else {
                max_money = Decimal::from(3000000 as i64);
            }
        } else {
            max_money = Decimal::from(goods_info.max_single_money as i64);
        }

        if let Err(e) = &goods_info.hands_num.parse::<i32>() {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityNotExist).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityNotExist).1;
            error!("商品信息【{}】数据错误，每手数量不是有效得整数:{:?}", order.stock_id, &e);
            log_error(&format!("商品信息【{}】数据错误，每手数量不是有效得整数:{:?}", order.stock_id, &e)).await;
            // if let Ok(log_client) = LogClient::get() {
            //     log_client.push_error(&format!("商品信息【{}】数据错误，每手数量不是有效得整数:{:?}", order.stock_id, &e)).await;
            // }
            return Ok(resp); //不能找到该商品
        }

        //非管理员平仓，需要判断最大量和最大金额，不管买卖
        if order.order_type != constant::OrderType::ADMINCLEAR2 as i32 && order.order_type != constant::OrderType::ADMINCLEAR as i32 {
            if order.order_amount > max_number {
                resp.ret_code = errors::get_error_code(ErrorCode::CodeOrderExceedMaxNumber).0;
                resp.ret_msg = format!("{},最大数量是:{}", errors::get_error_code(ErrorCode::CodeOrderExceedMaxNumber).1, max_number);
                // error!("没有找到【{}】的商品信息", order.stock_id);
                return Ok(resp); //不能找到该商品
            }

            let total_money = rate * Decimal::from_f64(order.order_price).unwrap_or_default() * Decimal::from_i32(order.order_amount).unwrap_or_default();
            if total_money > max_money {
                resp.ret_code = errors::get_error_code(ErrorCode::CodeOrderExceedMaxMoney).0;
                resp.ret_msg = format!("{},最大金额是:{}", errors::get_error_code(ErrorCode::CodeOrderExceedMaxMoney).1, max_money);
                // error!("没有找到【{}】的商品信息", order.stock_id);
                return Ok(resp); //不能找到该商品
            }
        }

        if order.order_direction == constant::OrderDirection::BUY as i32 {
            if order.order_amount < min_number {
                resp.ret_code = errors::get_error_code(ErrorCode::CodeOrderExceedMinNumber).0;
                resp.ret_msg = format!("{},最小数量是:{}", errors::get_error_code(ErrorCode::CodeOrderExceedMinNumber).1, min_number);
                return Ok(resp); //不能找到该商品
            }

            //处理买单
            if account_info.trade_state == (constant::AccountStatus::AccountBuyClosed as i32) {
                resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountBuyClosed).0;
                resp.ret_msg = errors::get_error_code(ErrorCode::CodeAccountBuyClosed).1;
                error!("账户【{}】禁止开仓", order.unit_id);
                log_error(&format!("账户【{}】禁止开仓", order.unit_id)).await;
                return Ok(resp);
            }

            if goods_info.trade_state == constant::CommidityStatus::SellOnly as i32 {
                error!("商品[{}]:[{}]只能平仓,不能下买单", order.stock_id, &goods_info.stock_name);
                log_error(&format!("商品[{}]:[{}]只能平仓,不能下买单", order.stock_id, &goods_info.stock_name)).await;
                resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).0;
                resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).1;
                // if let Ok(log_client) = LogClient::get() {
                //     log_client.push_error(&format!("商品[{}]:[{}]只能平仓,不能下买单", order.stock_id, &goods_info.stock_name)).await;
                // }
                return Ok(resp);
            }
            ret = self
                .order_controller
                .phoenix_handle_buy_order(
                    order,
                    &account_info,
                    &marketinfo,
                    &self.akacenterconn,
                    counter_partys,
                    &mut self.account_risk_client,
                    &stockquota,
                    &counter_stockquota,
                    margin.margin_ratio,
                    &agentaccount,
                )
                .await;
        } else {
            //处理卖单
            ret = self
                .order_controller
                .phoenix_handle_sell_order(
                    order,
                    &account_info,
                    &marketinfo,
                    &self.akacenterconn,
                    &goods_info,
                    &mut self.account_risk_client,
                    counter_party,
                    counter_partys,
                    &stockquota,
                    &counter_stockquota,
                    margin.margin_ratio,
                )
                .await;
        }

        log_debug(&format!("处理获取通道的请求完成:{:?}", &ret)).await;
        info!("====处理获取通道的请求完成,结果:{:?}", &ret);

        ret
    }

    pub async fn phoenix_risk_test(&mut self, _req: PhoenixRiskRequest) -> Result<PhoenixRiskResponse, Status> {
        let result = PhoenixRiskResponse {
            ret_code: 1111,
            ret_msg: String::from("aaa"),
        };
        info!("收到一个下单请求");
        std::thread::sleep(std::time::Duration::from_secs(2));

        Ok(result)
    }
}
