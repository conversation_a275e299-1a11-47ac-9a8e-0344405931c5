//! HQ Center Client with Graceful Shutdown Support
//!
//! This client provides connection management for the Phoenix HQ (Market Data) Center service
//! with built-in heartbeat monitoring and graceful shutdown capabilities.
//!
//! # Example Usage
//!
//! ```no_run
//! use hqcenterclient::HqCenterClient;
//! use std::time::Duration;
//!
//! #[tokio::main]
//! async fn main() -> anyhow::Result<()> {
//!     // Initialize the client
//!     let client = HqCenterClient::init(
//!         "http://localhost:50053",  // endpoint
//!         "my_client_id",            // client_id
//!         "my_service_name"          // service_name
//!     ).await;
//!     
//!     // Use the client for operations
//!     let tick_data = client.post_tick_hq(&"AAPL".to_string()).await?;
//!     let last_price = client.get_last_price("AAPL".to_string(), 1).await?;
//!     
//!     // Gracefully shutdown when done
//!     client.shutdown().await?;
//!     
//!     Ok(())
//! }
//! ```
//!
//! # Graceful Shutdown
//!
//! The client supports graceful shutdown through the `shutdown()` method which:
//! - Stops the background connection management task
//! - Clears the active client connection
//! - Ensures proper cleanup of resources
//!
//! The background task will respond to shutdown signals quickly (within 1 second)
//! even during heartbeat monitoring loops.

use anyhow::{Result, anyhow};
use common::logclient::log_error;
use heartbeatclient::HeartbeatClient;

use protoes::{
    hqcenter::{KLineHqInfo, KLineHqReq, LastPriceMsgReq, PreDealNumReq, TickHqReq, svr_post_subscribe_hq_msg_client::SvrPostSubscribeHqMsgClient},
    hqmsg::YsHqInfo,
};
use std::{sync::Arc, time::Duration};
use tokio::sync::{RwLock, broadcast};
use tonic::transport::Channel;
use tracing::*;

#[derive(Clone)]
pub struct HqCenterClient {
    client: Arc<RwLock<Option<SvrPostSubscribeHqMsgClient<Channel>>>>,
    heartbeat: HeartbeatClient,
    shutdown_tx: broadcast::Sender<()>,
    // uri: String,
}

impl HqCenterClient {
    pub async fn init(endpoint: &str, client_id: &str, service_name: &str) -> Self {
        let heartbeat = HeartbeatClient::new(endpoint, client_id.to_string(), service_name.to_string(), true).await;
        let client = Arc::new(RwLock::new(None));

        // Create shutdown channel
        let (shutdown_tx, _shutdown_rx) = broadcast::channel(1);

        if let Ok(channel) = tonic::transport::Channel::from_shared(endpoint.to_string()) {
            if let Ok(channel) = channel.connect().await {
                let proto_client = SvrPostSubscribeHqMsgClient::new(channel);
                let mut wr = client.write().await;
                *wr = Some(proto_client);
            } else {
                log_error(&format!("connect to HqCenterClient failed: {:?}", endpoint)).await;
                error!("connect to HqCenterClient failed: {:?}", endpoint);
            }
        }

        let client_clone = client.clone();

        let endpoint_owned = endpoint.to_string();
        let service_name_owned = service_name.to_string();
        let heartbeat_clone = heartbeat.clone();
        let shutdown_tx_clone = shutdown_tx.clone();

        // 啟動後台任務管理業務客戶端連接
        tokio::spawn(async move {
            let mut shutdown_rx = shutdown_tx_clone.subscribe();
            loop {
                tokio::select! {
                    _ = shutdown_rx.recv() => {
                        info!(service = %service_name_owned, "Received shutdown signal, stopping connection management task");
                        break;
                    }
                    _ = async {
                        info!(service = %service_name_owned, endpoint = %endpoint_owned, "Attempting to connect Service client");
                        match tonic::transport::Channel::from_shared(endpoint_owned.clone()) {
                            Ok(channel) => match channel.connect().await {
                                Ok(channel) => {
                                    {
                                        if client_clone.read().await.is_none() {
                                            let proto_client = SvrPostSubscribeHqMsgClient::new(channel);
                                            let mut wr = client_clone.write().await;
                                            *wr = Some(proto_client);
                                            // *client_clone.write().await.unwrap() = Some(proto_client);
                                            info!(service = %service_name_owned, "Service1 client connected");
                                        }
                                    }
                                    // 等待 HeartbeatClient 報告不健康，然後重試
                                    let mut heartbeat_shutdown_rx = shutdown_tx_clone.subscribe();
                                    loop {
                                        tokio::select! {
                                            _ = heartbeat_shutdown_rx.recv() => {
                                                info!(service = %service_name_owned, "Received shutdown signal during heartbeat monitoring");
                                                return;
                                            }
                                            _ = tokio::time::sleep(Duration::from_secs(1)) => {
                                                if !heartbeat_clone.is_healthy() {
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                    {
                                        let mut wr = client_clone.write().await;
                                        *wr = None::<SvrPostSubscribeHqMsgClient<tonic::transport::Channel>>;
                                        error!(service = %service_name_owned, "Service1 client disconnected due to unhealthy state");
                                    }
                                }
                                Err(e) => {
                                    error!(service = %service_name_owned, "Service1 client connection failed: {}", e);
                                }
                            },
                            Err(e) => {
                                error!(service = %service_name_owned, "Invalid endpoint: {}", e);
                            }
                        }
                        warn!(service = %service_name_owned, "Retrying Service1 client connection in 2 seconds");
                        tokio::time::sleep(Duration::from_secs(2)).await;
                    } => {}
                }
            }
            info!(service = %service_name_owned, "Connection management task terminated");
        });

        Self {
            client,
            heartbeat,
            shutdown_tx,
            // uri: endpoint.to_string(),
        }
    }

    /// Helper method to get a healthy client connection
    /// Returns Ok(client) if healthy and connected, Err otherwise
    ///
    /// # Performance optimizations:
    /// - Fast health check (no async ops)
    /// - Functional chain with lazy error creation
    /// - Automatic lock release (no explicit drop needed)
    async fn get_healthy_client(&self) -> Result<SvrPostSubscribeHqMsgClient<Channel>> {
        // Fast health check first (synchronous)
        if !self.heartbeat.is_healthy() {
            return Err(anyhow!("HQ中心服务不健康，请稍后重试"));
        }

        // Functional chain: read lock -> clone if Some -> error if None
        self.client.read().await.as_ref().cloned().ok_or_else(|| anyhow!("HQ中心客户端未连接"))
    }

    /// Helper method to handle client errors and reset connection
    async fn handle_client_error(&self, error: tonic::Status, operation: &str) -> anyhow::Error {
        // Reset client connection on error
        let mut write_guard = self.client.write().await;
        *write_guard = None;
        drop(write_guard);

        // Log error asynchronously
        let error_msg = format!("HQ中心 {} 操作失败: {:?}", operation, error);
        log_error(&error_msg).await;

        anyhow!("HQ中心操作失败")
    }

    /// Gracefully shutdown the HqCenterClient
    /// This will stop the background connection management task and cleanup resources
    pub async fn shutdown(&self) -> Result<()> {
        info!("Initiating graceful shutdown of HqCenterClient");

        // Send shutdown signal to background task
        if let Err(e) = self.shutdown_tx.send(()) {
            warn!("Failed to send shutdown signal: {:?}", e);
        }

        // Clear the client connection
        {
            let mut write_guard = self.client.write().await;
            *write_guard = None;
        }

        info!("HqCenterClient shutdown completed");
        Ok(())
    }

    pub async fn get_pre_deal_amount(&self, stock_code: &String, exchange_id: i32, time: &String) -> Result<i32> {
        // Get healthy client (combines health check and client acquisition)
        let mut client = self.get_healthy_client().await?;

        match client
            .get_pre_deal_amount(PreDealNumReq {
                stock_code: stock_code.clone(),
                exchange_id,
                time: time.clone(),
            })
            .await
        {
            Ok(val) => Ok(val.into_inner().prev_period_amount),
            Err(err) => Err(self.handle_client_error(err, "get_pre_deal_amount").await),
        }
    }

    pub async fn post_tick_hq(&self, stock_code: &String) -> Result<YsHqInfo> {
        // Get healthy client (combines health check and client acquisition)
        let mut client = self.get_healthy_client().await?;

        match client
            .post_tick_hq(TickHqReq {
                strcontractno: stock_code.clone(),
                iticktype: 1,
                ticktime: 0,
                realtime: 1,
            })
            .await
        {
            Ok(val) => Ok(val.into_inner().tickhqinfo.first().ok_or(anyhow!("找不到数据"))?.clone()),
            Err(err) => Err(self.handle_client_error(err, "post_tick_hq").await),
        }
    }

    pub async fn post_history_k_line_hq(&self, stock_code: String, end_time: &String) -> anyhow::Result<Vec<KLineHqInfo>> {
        // Get healthy client (combines health check and client acquisition)
        let mut client = self.get_healthy_client().await?;

        info!("请求 post_history_k_line_hq stock_code: {} end_time: {}", &stock_code, end_time);
        match client
            .post_history_k_line_hq(KLineHqReq {
                strcontractno: stock_code,
                strklinetype: "24".to_string(),
                strendtime: format!("{}000000", end_time),
                limit: 0,
                realtime: 1,
            })
            .await
        {
            Ok(val) => Ok(val.into_inner().klineinfo),
            Err(err) => Err(self.handle_client_error(err, "post_history_k_line_hq").await),
        }
    }

    pub async fn get_last_price(&self, stock_code: String, exchange_id: i32) -> anyhow::Result<f64> {
        // Get healthy client (combines health check and client acquisition)
        let mut client = self.get_healthy_client().await?;

        match client.get_last_price(LastPriceMsgReq { stock_code, exchange_id }).await {
            Ok(val) => {
                let last_price = val.into_inner();
                if last_price.data.is_none() {
                    return Err(anyhow!("last price are not found."));
                }
                Ok(last_price.data.unwrap().last_price)
            }
            Err(err) => Err(self.handle_client_error(err, "get_last_price").await),
        }
    }
}
