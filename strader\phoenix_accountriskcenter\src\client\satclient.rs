use anyhow::{anyhow, Result};
use common::constant::AsyncLock;
use common::logclient::*;
use protoes::phoenixsatcenter::sat_center_client::SatCenterClient;
use protoes::phoenixsatcenter::{ModifyRqReq, ModifyRqReqList};
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use tonic::transport::Channel;
use tracing::*;
#[derive(Debug, Clone)]
pub struct SatClient {
    sat_client: AsyncLock<Option<SatCenterClient<Channel>>>,
}

impl SatClient {
    pub async fn init(url: String) -> Self {
        let sat_client = Arc::new(RwLock::new(match SatCenterClient::connect(url.clone()).await {
            Ok(client) => Some(client),
            Err(err) => {
                error!("connect to SatCenter failed: {:?}", &err);
                log_error(&format!("Connected to satcenter failed:{:?}", &err)).await;
                None
            }
        }));

        let recon_client = sat_client.clone();
        tokio::spawn(async move {
            loop {
                if recon_client.read().await.is_none() {
                    *recon_client.write().await = match SatCenterClient::connect(url.clone()).await {
                        Ok(client) => Some(client),
                        Err(err) => {
                            error!("connect to SatCenter failed: {:?}", &err);
                            log_error(&format!("Connected to satcenter failed:{:?}", &err)).await;
                            None
                        }
                    };
                } else {
                    break;
                }
                tokio::time::sleep(Duration::from_secs(2)).await;
            }
        });

        Self { sat_client }
    }

    /// 更新已用
    pub async fn modify_used(&self, stock_id: i64, user_id: i64, use_num: i32) -> Result<()> {
        info!("开始更新融券的已用,stockid:{},userid:{},usenum:{}", stock_id, user_id, use_num);
        if self.sat_client.read().await.is_none() {
            log_error(&format!("satcenter is not connected")).await;
            return Err(anyhow!("SatCenter is not connected"));
        }
        let response = self
            .sat_client
            .clone()
            .write()
            .await
            .as_mut()
            .unwrap()
            .modify_rq(ModifyRqReq {
                rqlist: vec![ModifyRqReqList {
                    stock_id,
                    user_id,
                    use_num,
                    credit_num: 0,
                    mark: "".to_string(),
                    exec: 3,
                    rq_id: 0,
                }],
            })
            .await;
        if response.as_ref().is_err() {
            let e = response.unwrap_err();
            error!("failed:{:?}", e);
            log_error(&format!("modify_used failed: {:?}", &e)).await;
            // if let Ok(log_client) = LogClient::get() {
            //     log_client.push(LogLevel::Error, format!("modify_used failed: {:?}", e).as_str()).await;
            // }
            return Err(anyhow!("failed"));
        }

        Ok(())
    }

    /// 融券召回
    pub async fn recall_rq(&self, stock_id: i64) -> Result<()> {
        if self.sat_client.read().await.is_none() {
            // if let Ok(log_client) = LogClient::get() {
            //     log_client.push(LogLevel::Error, "SatCenter is not connected").await;
            // }
            log_error(&format!("satcenter is not connected")).await;
            return Err(anyhow!("SatCenter is not connected"));
        }

        let response = self
            .sat_client
            .write()
            .await
            .clone()
            .unwrap()
            .modify_rq(ModifyRqReq {
                rqlist: vec![ModifyRqReqList {
                    stock_id,
                    user_id: 0,
                    use_num: 0,
                    credit_num: 0,
                    mark: "".to_string(),
                    exec: 2,
                    rq_id: 0,
                }],
            })
            .await;
        if response.as_ref().is_err() {
            let e = response.unwrap_err();
            error!("failed:{:?}", e);
            log_error(&format!("failed:{:?}", &e)).await;
            // if let Ok(log_client) = LogClient::get() {
            //     log_client.push(LogLevel::Error, format!("recall_rq failed: {:?}", e).as_str()).await;
            // }
            return Err(anyhow!("failed"));
        }

        Ok(())
    }
}
