mod accountriskcenterclient;
pub use accountriskcenterclient::*;

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;
    use tokio::time::timeout;

    #[tokio::test]
    async fn test_graceful_shutdown() {
        // Test that graceful shutdown works properly
        // Note: This test doesn't require an actual server connection
        // It tests the shutdown mechanism itself

        let client = AccountRiskClient::init(
            "http://localhost:50052", // This endpoint doesn't need to be real for this test
            "test_client",
            "test_service",
        )
        .await;

        // Give it a moment to initialize
        tokio::time::sleep(Duration::from_millis(100)).await;

        // Test shutdown with timeout to ensure it doesn't hang
        let shutdown_result = timeout(Duration::from_secs(5), client.shutdown()).await;

        assert!(shutdown_result.is_ok(), "Shutdown should complete within timeout");
    }

    #[tokio::test]
    async fn test_multiple_shutdowns() {
        // Test that multiple shutdown calls don't cause issues
        let client = AccountRiskClient::init("http://localhost:50052", "test_client_multi", "test_service_multi").await;

        // Give it a moment to initialize
        tokio::time::sleep(Duration::from_millis(100)).await;

        // First shutdown
        client.shutdown().await;

        // Second shutdown should also succeed (idempotent)
        client.shutdown().await;
    }

    #[tokio::test]
    async fn test_sync_shutdown() {
        // Test the synchronous shutdown method
        let client = AccountRiskClient::init("http://localhost:50052", "test_sync_client", "test_sync_service").await;

        // Give it a moment to initialize
        tokio::time::sleep(Duration::from_millis(100)).await;

        // Test sync shutdown - should not panic or hang
        let _ = client.shutdown().await;

        // Verify we can call it multiple times
        let _ = client.shutdown().await;
    }

    #[tokio::test]
    async fn test_external_shutdown_coordination() {
        // Test external shutdown coordination
        let client = AccountRiskClient::init("http://localhost:50052", "test_external_client", "test_external_service").await;

        // Give it a moment to initialize
        tokio::time::sleep(Duration::from_millis(100)).await;

        // Get shutdown sender
        let shutdown_sender = client.get_shutdown_sender();

        // Test external shutdown signal
        let send_result = shutdown_sender.send(());
        assert!(send_result.is_ok(), "Should be able to send shutdown signal");

        // Give a moment for shutdown to process
        tokio::time::sleep(Duration::from_millis(200)).await;

        // Normal shutdown should still work
        client.shutdown().await;
    }
}
