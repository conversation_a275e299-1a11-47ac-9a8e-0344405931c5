//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_questionnaire")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub score: i32,
    pub risk_level: i8,
    pub question_type: i8,
    pub create_time: i64,
    pub create_name: String,
    pub user_id: i64,
    pub last_modify_time: i64,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
