use crate::dataservice::{
    dbsetup::DbConnection,
    old_stock_entities::{prelude::*, tstockposition},
};
use anyhow::{anyhow, Result};
use rust_decimal::Decimal;
use sea_orm::{ColumnTrait, Condition, DbErr, EntityTrait, FromQueryResult, QueryFilter, QuerySelect};
// use serde_json::json;

#[derive(Clone, Debug, PartialEq, FromQueryResult)]
pub struct TStockPositionResult {
    pub l_date: Decimal,
    pub l_position_no: i64,
    pub l_account_id: Decimal,
    pub l_unit_id: Decimal,
    pub vc_stock_code: Option<String>,
    pub l_exchange_id: i32,
    pub c_position_flag: String,
    pub l_begin_amount: Decimal,
    pub l_current_amount: Decimal,
    pub l_frozen_amount: Decimal,
    pub l_buy_amount: Decimal,
    pub l_sale_amount: Decimal,
    pub l_prebuy_amount: Decimal,
    pub l_presale_amount: Decimal,
    pub en_buy_fee: Decimal,
    pub en_sale_fee: Decimal,
    pub en_current_cost: Option<Decimal>,
    pub en_total_value: Option<Decimal>,
    pub en_real_buy_fee: Option<Decimal>,
    pub en_real_sale_fee: Option<Decimal>,
    pub l_temp_frozen_amount: Option<Decimal>,
    pub en_today_profit: Option<Decimal>,
    pub l_stock_id: Option<i32>,
    pub en_last_price: Option<Decimal>,
    pub l_buy_in_transit: Option<Decimal>,
    pub l_sale_in_transit: Option<Decimal>,
    pub l_channel_id: Option<i32>,
    pub l_stock_type: i32,
    pub en_total_value_hkd: Decimal,
    pub en_begin_value_hkd: Decimal,
    pub en_today_total_value_hkd: Decimal,
}

impl tstockposition::Model {
    pub async fn find_all_by_condition(db: &DbConnection, condition: Condition) -> Result<Vec<Tstockposition>> {
        let ret_data: Result<Vec<Tstockposition>, DbErr> = TstockpositionEntity::find().filter(condition).all(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(format!("Tstockposition: {:?}", ret_data.err())));
        }

        Ok(ret_data.unwrap())
    }

    pub async fn query_many(unit_id: i64, db: &DbConnection) -> Result<Vec<TStockPositionResult>> {
        let ret = match unit_id {
            0 => {
                TstockpositionEntity::find()
                    .column_as(tstockposition::Column::LBeginAmount.sum(), "l_begin_amount")
                    .column_as(tstockposition::Column::LCurrentAmount.sum(), "l_current_amount")
                    .column_as(tstockposition::Column::LFrozenAmount.sum(), "l_frozen_amount")
                    .column_as(tstockposition::Column::LBuyAmount.sum(), "l_buy_amount")
                    .column_as(tstockposition::Column::LSaleAmount.sum(), "l_sale_amount")
                    .column_as(tstockposition::Column::LPrebuyAmount.sum(), "l_prebuy_amount")
                    .column_as(tstockposition::Column::LPresaleAmount.sum(), "l_presale_amount")
                    .column_as(tstockposition::Column::EnBuyFee.sum(), "en_buy_fee")
                    .column_as(tstockposition::Column::EnSaleFee.sum(), "en_sale_fee")
                    .column_as(tstockposition::Column::EnCurrentCost.sum(), "en_current_cost")
                    .column_as(tstockposition::Column::EnTotalValue.sum(), "en_total_value")
                    .column_as(tstockposition::Column::EnTradeCapital.sum(), "en_trade_capital")
                    .column_as(tstockposition::Column::EnRealBuyFee.sum(), "en_real_buy_fee")
                    .column_as(tstockposition::Column::EnRealSaleFee.sum(), "en_real_sale_fee")
                    .column_as(tstockposition::Column::LTempFrozenAmount.sum(), "l_temp_frozen_amount")
                    .column_as(tstockposition::Column::EnTodayProfit.sum(), "en_today_profit")
                    .column_as(tstockposition::Column::LBuyInTransit.sum(), "l_buy_in_transit")
                    .column_as(tstockposition::Column::LSaleInTransit.sum(), "l_sale_in_transit")
                    .column_as(tstockposition::Column::EnTotalValueHkd.sum(), "en_total_value_hkd")
                    .column_as(tstockposition::Column::EnBeginValueHkd.sum(), "en_begin_value_hkd")
                    .column_as(tstockposition::Column::EnTodayTotalValueHkd.sum(), "en_today_total_value_hkd")
                    .group_by(tstockposition::Column::LUnitId)
                    .group_by(tstockposition::Column::LStockId)
                    .into_model::<TStockPositionResult>()
                    .all(db.get_connection())
                    .await
            }
            _ => {
                TstockpositionEntity::find()
                    .filter(tstockposition::Column::LUnitId.eq(unit_id))
                    .column_as(tstockposition::Column::LBeginAmount.sum(), "l_begin_amount")
                    .column_as(tstockposition::Column::LCurrentAmount.sum(), "l_current_amount")
                    .column_as(tstockposition::Column::LFrozenAmount.sum(), "l_frozen_amount")
                    .column_as(tstockposition::Column::LBuyAmount.sum(), "l_buy_amount")
                    .column_as(tstockposition::Column::LSaleAmount.sum(), "l_sale_amount")
                    .column_as(tstockposition::Column::LPrebuyAmount.sum(), "l_prebuy_amount")
                    .column_as(tstockposition::Column::LPresaleAmount.sum(), "l_presale_amount")
                    .column_as(tstockposition::Column::EnBuyFee.sum(), "en_buy_fee")
                    .column_as(tstockposition::Column::EnSaleFee.sum(), "en_sale_fee")
                    .column_as(tstockposition::Column::EnCurrentCost.sum(), "en_current_cost")
                    .column_as(tstockposition::Column::EnTotalValue.sum(), "en_total_value")
                    .column_as(tstockposition::Column::EnRealBuyFee.sum(), "en_real_buy_fee")
                    .column_as(tstockposition::Column::EnRealSaleFee.sum(), "en_real_sale_fee")
                    .column_as(tstockposition::Column::LTempFrozenAmount.sum(), "l_temp_frozen_amount")
                    .column_as(tstockposition::Column::EnTodayProfit.sum(), "en_today_profit")
                    .column_as(tstockposition::Column::LBuyInTransit.sum(), "l_buy_in_transit")
                    .column_as(tstockposition::Column::LSaleInTransit.sum(), "l_sale_in_transit")
                    .column_as(tstockposition::Column::EnTotalValueHkd.sum(), "en_total_value_hkd")
                    .column_as(tstockposition::Column::EnBeginValueHkd.sum(), "en_begin_value_hkd")
                    .column_as(tstockposition::Column::EnTodayTotalValueHkd.sum(), "en_today_total_value_hkd")
                    .group_by(tstockposition::Column::LUnitId)
                    .group_by(tstockposition::Column::LStockId)
                    .into_model::<TStockPositionResult>()
                    .all(db.get_connection())
                    .await
            }
        };
        if ret.as_ref().is_err() {
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let d = ret.unwrap();
        return Ok(d);
    }
}
