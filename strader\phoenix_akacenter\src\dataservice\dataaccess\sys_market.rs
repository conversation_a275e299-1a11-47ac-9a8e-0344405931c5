use crate::dataservice::{
    entities::{
        prelude::{SysMarket, SysMarketEntity},
        sys_market,
    },
};
use dbconnection::DbConnection;
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, DbErr, EntityTrait, QueryFilter};

impl SysMarket {
    pub async fn find_all(db: &DbConnection) -> Result<Vec<SysMarket>> {
        let ret_data: Result<Vec<SysMarket>, DbErr> = SysMarketEntity::find()
            .filter(sys_market::Column::DelState.eq(1))
            .filter(sys_market::Column::Currency.ne(""))
            .all(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }

    pub async fn find_by_id(db: &DbConnection, market_id: i64) -> Result<SysMarket> {
        let ret_data: Result<Option<SysMarket>, DbErr> = SysMarketEntity::find_by_id(market_id).filter(sys_market::Column::DelState.eq(1)).one(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        let data = ret_data.unwrap();
        return match data {
            Some(v) => Ok(v),
            None => Err(anyhow!("SysMarket数据不存在")),
        };
    }
}
