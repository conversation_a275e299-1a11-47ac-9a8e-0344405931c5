use crate::dataservice::{
    entities::{
        prelude::{SysTradeConfigChannel, SysTradeConfigChannelEntity},
        sys_trade_config_channel,
    },
};
use dbconnection::DbConnection;
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, Condition, DbErr, EntityTrait, QueryFilter};

impl SysTradeConfigChannel {
    pub async fn _find_by_trade_config_id(db: &DbConnection, trade_config_id: i64) -> Result<Vec<SysTradeConfigChannel>> {
        let ret_data: Result<Vec<SysTradeConfigChannel>, DbErr> = SysTradeConfigChannelEntity::find()
            .filter(sys_trade_config_channel::Column::TradeConfigId.eq(trade_config_id))
            .filter(sys_trade_config_channel::Column::Direction.eq(1))
            .all(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }

    pub async fn _find_by_condition(db: &DbConnection, condition: Condition) -> Result<Vec<SysTradeConfigChannel>> {
        let ret_data: Result<Vec<SysTradeConfigChannel>, DbErr> = SysTradeConfigChannelEntity::find().filter(condition).all(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(format!("SysTradeConfigChannel: {}", ret_data.err().unwrap())));
        }

        Ok(ret_data.unwrap())
    }
}
