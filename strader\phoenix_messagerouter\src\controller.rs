use std::sync::Arc;

use crate::config::settings::Settings;
// use crate::protofiles::phoenixmessagerouter::*;
use anyhow::{anyhow, Result};
// use common::uidservice::UidgenService;
use tokio::sync::RwLock;
// use rust_decimal::{prelude::*, Decimal};
use crate::client::OrderCenterClient;
// use crate::client::OrderRouterClient;
// use tonic::{self};
// use utility::{constant, errors, errors::ErrorCode};
use async_channel::Sender;
use protoes::phoenixordercenter::{ReplenishOrderReq, ReplenishOrderResp};
use protoes::{
    phoenixordercenter::{OrderReq, OrderResp},
    phoenixordermsg::{MsgContent, MsgType, ReqResp, RouterMsg},
};

// #[derive(Clone)]
#[allow(dead_code)]
pub struct ServerController {
    pub settings: Arc<RwLock<Settings>>,
    //pub uidsvc: Arc<RwLock<UidgenService>>,
    pub order_center_client: OrderCenterClient,
    //  pub order_router_client :OrderRouterClient,
    pub router_sender: Sender<RouterMsg>,
}

//处理业务逻辑
impl ServerController {
    pub async fn order_message_transfer(&self, req: &OrderReq) -> Result<OrderResp> {
        let client = self.order_center_client.clone();
        let ret = client.place_order(req).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!("请求失败:{}", ret.as_ref().err().unwrap().to_string()));
        }
        Ok(OrderResp {
            msg_id: req.msg_id,
            order_id: 0,
            error_code: 0,
            error_msg: "".to_string(),
        })
    }

    pub async fn exec_order_message_transfer(&self, req: &RouterMsg) -> Result<RouterMsg> {
        //let mut client = self.order_router_client.clone();
        //let req =  client.order_transfer(req.to_owned()).await;
        let ret = self.router_sender.send(req.to_owned()).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!("请求失败:{}", ret.as_ref().err().unwrap().to_string()));
        }
        Ok(RouterMsg {
            msg_type: MsgType::Response as i32,
            msg_content: Some(MsgContent {
                register_req: None,
                order_msg: None,
                exec_msg: None,
                resp: Some(ReqResp {
                    msg_id: req.msg_id,
                    error_code: 0,
                    error_msg: "".to_string(),
                }),
            }),
            msg_id: 0,
            msg_time: 0,
        })
    }
    pub async fn replenishment_order_transfer(&self, req: &ReplenishOrderReq) -> Result<ReplenishOrderResp> {
        let client = self.order_center_client.clone();
        let ret = client.replenishment_order(req).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!("请求失败:{}", ret.as_ref().err().unwrap().to_string()));
        }
        Ok(ReplenishOrderResp { err_msg: "".to_string(), err_code: 0 })
        //todo!()
    }
}
