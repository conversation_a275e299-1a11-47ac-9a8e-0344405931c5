//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_apply_record")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub user_id: i64,
    pub business_type: i8,
    pub apply_op_type: i8,
    pub apply_time: i64,
    pub account_no: String,
    pub lever: Option<i32>,
    pub old_lever: Option<i32>,
    pub check_data: String,
    pub t_id: i64,
    pub show_state: i8,
    pub status: i8,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
