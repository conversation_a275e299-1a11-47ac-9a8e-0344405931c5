//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_questionnaire_answer")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub questionnaire_id: i64,
    pub topic_type_desc: String,
    pub topic_title: String,
    pub supplement_text: String,
    pub answer_score: i32,
    pub create_time: i64,
    pub answer_desc: String,
    pub sort: i32,
    pub topic_id: i64,
    pub topic_answer_id: i64,
    pub topic_type: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
