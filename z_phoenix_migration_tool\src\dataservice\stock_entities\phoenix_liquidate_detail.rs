//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_liquidate_detail")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub market_type: i32,
    pub liquidate_trigger_time: String,
    pub current_trade_date: i32,
    pub liq_prepare: i32,
    pub archive_state: i32,
    pub archive_time: i64,
    pub initialize_state: i32,
    pub initialize_time: i64,
    pub settle_state: i32,
    pub settle_time: i64,
    pub dividend_trigger_time: String,
    pub dividend_state: i32,
    pub dividend_exec_time: i64,
    pub memo: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
