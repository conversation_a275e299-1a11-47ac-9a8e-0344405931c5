mod hqcenterclient;
pub use hqcenterclient::*;

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;
    use tokio::time::timeout;

    #[tokio::test]
    async fn test_graceful_shutdown() {
        // Test that graceful shutdown works properly
        // Note: This test doesn't require an actual server connection
        // It tests the shutdown mechanism itself

        let client = HqCenterClient::init(
            "http://localhost:50053", // This endpoint doesn't need to be real for this test
            "test_client",
            "test_service",
        )
        .await;

        // Give it a moment to initialize
        tokio::time::sleep(Duration::from_millis(100)).await;

        // Test shutdown with timeout to ensure it doesn't hang
        let shutdown_result = timeout(Duration::from_secs(5), client.shutdown()).await;

        assert!(shutdown_result.is_ok(), "Shutdown should complete within timeout");
        assert!(shutdown_result.unwrap().is_ok(), "Shutdown should succeed");
    }

    #[tokio::test]
    async fn test_multiple_shutdowns() {
        // Test that multiple shutdown calls don't cause issues
        let client = HqCenterClient::init("http://localhost:50053", "test_client_multi", "test_service_multi").await;

        // Give it a moment to initialize
        tokio::time::sleep(Duration::from_millis(100)).await;

        // First shutdown
        let result1 = client.shutdown().await;
        assert!(result1.is_ok(), "First shutdown should succeed");

        // Second shutdown should also succeed (idempotent)
        let result2 = client.shutdown().await;
        assert!(result2.is_ok(), "Second shutdown should also succeed");
    }

    #[tokio::test]
    async fn test_client_initialization() {
        // Test that client initializes correctly without panicking
        let client = HqCenterClient::init("http://localhost:50053", "test_init_client", "test_init_service").await;

        // Client should be created successfully even if connection fails
        // (it will retry in background)
        assert!(true, "Client initialization should complete");

        // Clean shutdown
        let _ = client.shutdown().await;
    }

    #[tokio::test]
    async fn test_client_with_invalid_endpoint() {
        // Test client behavior with invalid endpoint
        let client = HqCenterClient::init(
            "http://invalid-endpoint:99999", // Invalid endpoint to ensure connection failures
            "test_invalid_client",
            "test_invalid_service",
        )
        .await;

        // Client should be created successfully even if connection fails
        // (it will retry in background)
        assert!(true, "Client initialization should complete even with invalid endpoint");

        // Clean shutdown
        let _ = client.shutdown().await;
    }
}
