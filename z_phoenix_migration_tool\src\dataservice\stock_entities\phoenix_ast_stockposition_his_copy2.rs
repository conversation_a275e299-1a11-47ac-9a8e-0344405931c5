//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_ast_stockposition_his_copy2")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub did: i64,
    pub sys_date: i32,
    pub unit_id: i64,
    pub stock_code: String,
    pub stock_id: i64,
    pub exchange_id: i32,
    pub position_flag: i32,
    pub begin_amount: i32,
    pub current_amount: i32,
    pub frozen_amount: i32,
    pub temp_frozen_amount: i32,
    pub buy_amount: i32,
    pub sale_amount: i32,
    pub prebuy_amount: i32,
    pub presale_amount: i32,
    #[sea_orm(column_type = "Decimal(Some((12, 2)))")]
    pub buy_fee: Decimal,
    #[sea_orm(column_type = "Decimal(Some((12, 2)))")]
    pub sale_fee: Decimal,
    #[sea_orm(column_type = "Decimal(Some((12, 2)))")]
    pub real_buy_fee: Decimal,
    #[sea_orm(column_type = "Decimal(Some((12, 2)))")]
    pub real_sale_fee: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub current_cost: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 8)))")]
    pub avg_price: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 8)))")]
    pub total_value: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 4)))")]
    pub total_value_hkd: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 4)))")]
    pub begin_value_hkd: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 4)))")]
    pub today_total_value_hkd: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub last_price: Decimal,
    pub buy_in_transit: i32,
    pub sale_in_transit: i32,
    pub qf_amount: i32,
    pub stock_type: i32,
    #[sea_orm(column_type = "Decimal(Some((10, 4)))")]
    pub margin_rate: Decimal,
    pub position_no: i64,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
