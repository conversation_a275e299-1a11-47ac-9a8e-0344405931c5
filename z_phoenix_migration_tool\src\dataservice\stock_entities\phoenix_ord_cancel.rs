//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_ord_cancel")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    #[sea_orm(unique)]
    pub cancel_no: i64,
    pub sys_date: i32,
    pub unit_id: i64,
    pub order_no: i64,
    pub stock_code: String,
    pub order_direction: i32,
    #[sea_orm(column_type = "Decimal(Some((16, 8)))")]
    pub order_price: Decimal,
    pub order_amount: i32,
    pub deal_amount: i32,
    pub canceled_amount: i32,
    pub cancel_type: i32,
    pub cancel_status: i32,
    pub cancel_memo: String,
    pub create_time: i64,
    pub modify_time: i64,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumI<PERSON>, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
