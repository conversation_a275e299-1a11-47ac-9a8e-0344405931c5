use dbconnection::DbConnection;
use crate::dataservice::entities::prelude::{UsersRq, UsersRqEntity};
use crate::dataservice::entities::users_rq;
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, Condition, DbErr, EntityTrait, QueryFilter};

impl users_rq::Model {
    pub async fn find_by_date(db: &DbConnection, stock_id: i64) -> Result<Vec<UsersRq>> {
        let mut condition = Condition::all().add(users_rq::Column::State.eq(1));

        condition = if stock_id != 0 { condition.add(users_rq::Column::CommodityId.eq(stock_id)) } else { condition };

        let ret_data: Result<Vec<UsersRq>, DbErr> = UsersRqEntity::find().filter(condition).all(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }
}
