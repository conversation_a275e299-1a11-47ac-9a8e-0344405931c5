use sea_orm::{ConnectOptions, Database, DatabaseConnection};
use std::time::Duration;
use tracing::*;

#[derive(Debug, Clone)]
pub struct DbConnection {
    pub dbconn: DatabaseConnection,
    // backend: DatabaseBackend,
}

impl DbConnection {
    pub async fn new(uri: &str) -> Self {
        let mut opt = ConnectOptions::new(uri.to_owned());

        opt
            // Connection Pool Settings
            .max_connections(30)        // Increased for high-throughput trading
            .min_connections(10)        // Higher minimum for consistent performance
            .connect_timeout(Duration::from_secs(10))
            .acquire_timeout(Duration::from_secs(5))    // Time to wait for connection from pool            
            // Connection Lifecycle - CRITICAL for production
            .idle_timeout(Duration::from_secs(300))     // 5 minutes - close idle connections
            .max_lifetime(Duration::from_secs(1800))    // 30 minutes - rotate connections            
            // Performance Optimizations
            .sqlx_logging(false)        // Disable in production for performance
            .sqlx_logging_level(log::LevelFilter::Warn) // Only log warnings/errors            
            // Additional optimizations
            .test_before_acquire(true)  // Validate connections before use
            ;

        let db = Database::connect(opt).await.expect("can't connect to database");
        DbConnection {
            dbconn: db,
            // backend: DatabaseBackend::MySql,
        }
    }

    pub fn get_connection(&self) -> &DatabaseConnection {
        &self.dbconn
    }
}
