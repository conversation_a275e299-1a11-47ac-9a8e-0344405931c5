//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_futures_config")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    #[sea_orm(unique)]
    pub user_id: i64,
    pub trade_channel_id: i8,
    pub show_rank_list: i8,
    pub account_type: i8,
    pub bi_no: String,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub deposit_ratio: Decimal,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub yu_jing: Decimal,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub ping_cang: Decimal,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
