//! Manage Client with Graceful Shutdown Support
//!
//! This client provides connection management for the Phoenix Manager Unit service
//! with built-in automatic reconnection and graceful shutdown capabilities.
//!
//! # Example Usage
//!
//! ```no_run
//! use manageclient::Manageclient;
//! use protoes::managerunit::ModifyUnitReq;
//!
//! #[tokio::main]
//! async fn main() -> anyhow::Result<()> {
//!     // Initialize the client
//!     let client = Manageclient::init("http://localhost:50056".to_string()).await;
//!     
//!     // Use the client for management operations
//!     // let modify_req = ModifyUnitReq { /* ... */ };
//!     // client.modify_unit_state(modify_req).await?;
//!     
//!     // Gracefully shutdown when done
//!     client.shutdown().await;
//!     
//!     Ok(())
//! }
//! ```
//!
//! # Graceful Shutdown
//!
//! The client supports graceful shutdown through the `shutdown()` method which:
//! - Stops the background reconnection task
//! - Clears the active client connection
//! - Ensures proper cleanup of resources
//!
//! The background task will respond to shutdown signals quickly (within 3 seconds)
//! during the reconnection monitoring loop.

use anyhow::Result;
use anyhow::anyhow;
use common::logclient::log_error;
use protoes::managerunit::AlgorithmReq;
use protoes::managerunit::ModifyAlgorithmReq;
use protoes::managerunit::ModifyUnitReq;
use protoes::managerunit::OptionQuotationReq;
use protoes::managerunit::OptionQuotationResp;
use protoes::managerunit::QueryAlgorithmResp;
use protoes::managerunit::manager_unit_client::ManagerUnitClient;
use std::sync::Arc;
use tokio::sync::{RwLock, broadcast};
use tonic::transport::Channel;
use tracing::*;
#[derive(Clone)]
pub struct Manageclient {
    client: Arc<RwLock<Option<ManagerUnitClient<Channel>>>>,
    shutdown_tx: broadcast::Sender<()>,
    uri: String,
}

impl Manageclient {
    pub async fn init(url: String) -> Self {
        // Create shutdown channel
        let (shutdown_tx, _shutdown_rx) = broadcast::channel(1);

        let manageclient = Arc::new(RwLock::new(match ManagerUnitClient::connect(url.clone()).await {
            Ok(client) => Some(client),
            Err(err) => {
                error!("connect to Managecenter failed: {:?}", &err);
                log_error(&format!("Connected to Managecenter failed:{:?}", &err)).await;
                None
            }
        }));

        let client = Manageclient {
            client: manageclient,
            shutdown_tx: shutdown_tx.clone(),
            uri: url,
        };
        client.start_reconnect_listen().await;
        client
    }

    pub async fn start_reconnect_listen(&self) {
        let manageclient_clone = Arc::clone(&self.client);
        let url = self.uri.clone();
        let shutdown_tx_clone = self.shutdown_tx.clone();

        tokio::spawn(async move {
            let mut shutdown_rx = shutdown_tx_clone.subscribe();
            loop {
                tokio::select! {
                    _ = shutdown_rx.recv() => {
                        info!("Received shutdown signal, stopping reconnection management task");
                        break;
                    }
                    _ = tokio::time::sleep(std::time::Duration::from_secs(3)) => {
                        if manageclient_clone.read().await.is_none() {
                            info!("manage管理端重新连接中......");
                            log_error(&format!("manage管理端重新连接中......")).await;
                            let client = ManagerUnitClient::connect(url.clone()).await;
                            if client.is_err() {
                                error!("不能连接到manage管理端,3秒后重连");
                                // std::thread::sleep(std::time::Duration::from_secs(3));
                                // continue;
                            }else{
                                info!("manage管理端连接成功......");
                                let mut wr = manageclient_clone.write().await;
                                *wr = Some(client.unwrap());
                            }
                        }
                    }
                }
            }
            info!("Reconnection management task terminated");
        });
    }

    /// Gracefully shutdown the client and stop all background tasks
    pub async fn shutdown(&self) {
        info!("Initiating graceful shutdown for Manageclient");

        // Send shutdown signal to background task
        if let Err(e) = self.shutdown_tx.send(()) {
            warn!("Failed to send shutdown signal: {}", e);
        }

        // Clear the client connection
        {
            let mut write_guard = self.client.write().await;
            *write_guard = None;
        }

        info!("Manageclient shutdown completed");
    }

    /// Get a healthy client instance with optimized error handling
    async fn get_healthy_client(&self) -> Option<ManagerUnitClient<tonic::transport::Channel>> {
        // Clone the client if available
        self.client.read().await.as_ref().map(|client| client.clone())
    }

    /// Handle client errors with standardized error responses
    fn handle_client_error<T>(&self, operation: &str, error: Option<&str>) -> Result<T> {
        let error_msg = match error {
            Some(err) => format!("Managecenter service unavailable during {}: {}", operation, err),
            None => format!("Managecenter service unavailable during {}", operation),
        };

        warn!("{}", error_msg);
        Err(anyhow!(error_msg))
    }

    //修改状态
    pub async fn modify_unit_state(&self, request: ModifyUnitReq) -> Result<()> {
        let mut client = match self.get_healthy_client().await {
            Some(client) => client,
            None => return self.handle_client_error("modify_unit_state", None),
        };

        match client.modify_unit_state(request).await {
            Ok(_) => Ok(()),
            Err(err) => {
                // Mark client as disconnected on error
                *self.client.write().await = None;

                // Log error asynchronously
                let error_str = err.to_string();
                log_error(&format!("manage管理端 modify_unit_state error: {:?}", error_str)).await;

                self.handle_client_error("modify_unit_state", Some(&error_str))
            }
        }
    }

    /// 更新算法单
    pub async fn modify_algorithm(&self, uid: i64, ordernum: i32, dealnum: i32, last_execute_time: i64, status: i32) -> Result<()> {
        let mut client = match self.get_healthy_client().await {
            Some(client) => client,
            None => return self.handle_client_error("modify_algorithm", None),
        };

        let request = ModifyAlgorithmReq {
            uid,
            ordernum,
            dealnum,
            last_execute_time,
            status,
        };

        match client.modify_algorithm(request).await {
            Ok(_) => Ok(()),
            Err(err) => {
                // Mark client as disconnected on error
                *self.client.write().await = None;

                // Log error asynchronously
                log_error(&format!("manage管理端 modify_algorithm异常")).await;

                self.handle_client_error("modify_algorithm", Some(&err.to_string()))
            }
        }
    }

    /// 查询算法单
    pub async fn query_algorithm(&self) -> Result<Vec<QueryAlgorithmResp>> {
        let mut client = match self.get_healthy_client().await {
            Some(client) => client,
            None => return self.handle_client_error("query_algorithm", None),
        };

        match client.query_algorithm(AlgorithmReq {}).await {
            Ok(response) => Ok(response.into_inner().data),
            Err(err) => {
                // Mark client as disconnected on error
                *self.client.write().await = None;

                // Log error asynchronously
                log_error(&format!("manage管理端 query_algorithm异常")).await;

                self.handle_client_error("query_algorithm", Some(&err.to_string()))
            }
        }
    }

    pub async fn query_option_quotations(&self) -> Option<OptionQuotationResp> {
        let mut client = match self.get_healthy_client().await {
            Some(client) => client,
            None => {
                error!("manage管理端未连接");
                return None;
            }
        };

        let req = OptionQuotationReq::default();
        match client.query_option_quotation(req).await {
            Ok(response) => {
                let resp = response.into_inner();
                Some(resp)
            }
            Err(err) => {
                // Mark client as disconnected on error
                *self.client.write().await = None;

                // Log error asynchronously
                log_error(&format!("manage管理端 query_option_quotations异常")).await;
                error!("{:?}", err);
                None
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::test;
    use tokio::time::{Duration, sleep};

    #[test]
    async fn test_manageclient_initialization() {
        // Test that client initializes correctly
        let _client = Manageclient::init("http://localhost:9093".to_string()).await;

        // Should start with potentially unhealthy connection (if service not available)
        // This is expected behavior for tests where service might not be running
    }

    #[test]
    async fn test_graceful_shutdown() {
        let client = Manageclient::init("http://localhost:9093".to_string()).await;

        // Should be able to shutdown gracefully
        client.shutdown().await;

        // Allow some time for shutdown signal to propagate
        sleep(Duration::from_millis(100)).await;
    }

    #[test]
    async fn test_multiple_shutdowns() {
        let client = Manageclient::init("http://localhost:9093".to_string()).await;

        // Should handle multiple shutdown calls gracefully
        client.shutdown().await;
        client.shutdown().await;
        client.shutdown().await;

        // Allow time for all shutdown signals
        sleep(Duration::from_millis(100)).await;
    }

    #[test]
    async fn test_client_error_handling_when_unhealthy() {
        let client = Manageclient::init("http://invalid-endpoint:9999".to_string()).await;

        // Allow time for connection attempts
        sleep(Duration::from_millis(500)).await;

        // Should handle errors gracefully for modify_unit_state
        let modify_req = ModifyUnitReq {
            user_id: 1,
            status: 1,
            push_msg: 0,
            rate: 1.0,
        };

        let result = client.modify_unit_state(modify_req).await;
        assert!(result.is_err());

        // Should handle errors gracefully for modify_algorithm
        let result = client.modify_algorithm(1, 10, 5, **********, 1).await;
        assert!(result.is_err());

        // Should handle errors gracefully for query_algorithm
        let result = client.query_algorithm().await;
        assert!(result.is_err());

        // Should handle errors gracefully for query_option_quotations
        let result = client.query_option_quotations().await;
        assert!(result.is_none());

        // Cleanup
        client.shutdown().await;
    }

    #[test]
    async fn test_helper_methods() {
        let client = Manageclient::init("http://invalid-endpoint:9999".to_string()).await;

        // Test get_healthy_client with unhealthy service
        let healthy_client = client.get_healthy_client().await;
        // Should be None for invalid endpoint
        assert!(healthy_client.is_none());

        // Test handle_client_error
        let error_result: Result<()> = client.handle_client_error("test_operation", Some("test error"));
        assert!(error_result.is_err());

        let error_msg = error_result.unwrap_err().to_string();
        assert!(error_msg.contains("Managecenter service unavailable"));
        assert!(error_msg.contains("test_operation"));
        assert!(error_msg.contains("test error"));

        // Cleanup
        client.shutdown().await;
    }
}
