//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_bank")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub user_id: i64,
    pub card_no: String,
    pub bank_code: String,
    pub bank_name: String,
    pub bank_province: String,
    pub bank_city: String,
    pub bank_branch_name: String,
    pub create_time: i32,
    pub bank_sort: i32,
    pub bank_type: String,
    pub bank_country: String,
    pub status: i8,
    pub swift_code: String,
    pub remittance_path: String,
    pub bank_user: String,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
