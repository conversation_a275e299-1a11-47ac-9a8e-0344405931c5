//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_exam")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub user_id: i64,
    pub mail: String,
    pub mobile: String,
    pub status: i8,
    pub real_name: String,
    pub male: i8,
    pub futures_open_state: i8,
    pub exchange_open_state: i8,
    pub stock_open_state: i8,
    pub create_date: i64,
    pub open_source: i8,
    pub user_type: i8,
    pub invite_code: String,
    pub mobile_type: String,
    pub stock_invite_code: String,
    pub account_cate: i8,
    pub id_card: String,
    pub card_type: i8,
    pub user_name: String,
    pub user_surname: String,
    pub grada_user_id: i64,
    #[sea_orm(primary_key, auto_increment = false)]
    pub en_real_name: String,
    pub apply_date: Option<u64>,
    pub apply_ip: Option<String>,
    pub apply_ip_location: Option<String>,
    pub apply_status: Option<i8>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
