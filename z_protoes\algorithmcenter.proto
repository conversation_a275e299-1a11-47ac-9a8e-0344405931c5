// 算法单proto文件
syntax = "proto3";

package phoenixalgorithmcenter;


service AlgorithmCenter{
  rpc PlaceAlgorithmOrder(AlgorithmOrderReq) returns (AlgorithmOrderResponse) {}   // 算法单下单
  rpc CancelAlgorithmOrder(AlgorithmCancelReq) returns (AlgorithmCancelResponse) {}   // 算法单撤单
}

// 下单请求
message AlgorithmOrderReq {
  int64 algorithm_id = 1;           // 算法单id
  int64 exchange_id = 2;            // 市场id
  string stock_code = 3;            // 品种代码
  int64 user_id = 4;                // 用户id
  int32 algorithm_type = 5;         // 算法类型
  int64 stock_id = 6;               // 品种id
  int64 start_date = 7;             // 开始时间
  int64 end_date = 8;               // 结束时间
  int32 price_type = 9;             // '价格类型，1：限价，2：市价',
  double price_num = 10;            // '价格/市价 1：对手一档，2：对手二挡'
  int32 order_num = 11;             // 订单数量
  int32 order_type = 12;            // '1:买，2：卖',
  int32 entrust_num = 13;           // 已委托数量
  int32 deal_num = 14;              // 已成交数量
  int64 create_date = 15;           // 创建时间
  string trigger_condition = 16;    // trigger类型时，条件的触发条件，>= 等
  double trigger_price = 17;        // trigger类型时，触发价格
  int32 interval_time = 18;         // 间隔时间
  int64 last_execute_time = 19;     // 上一次执行时间
  int32 market_type = 20;           // 市场类型，1：港股，2：美股，3：沪深
  int32 hands_num=21;				// 每手股数
  string market_code=22;			// 市场代码
  int32 maximum_order = 23;         //最大下单量
  int64 unit_id = 24;                // 用户id
}

// 下单返回
message AlgorithmOrderResponse{
  string ret_code =1;//返回结果
  string ret_msg =2;//返回结果
}

// 终止请求
message AlgorithmCancelReq {
  int64 algorithm_id = 1;           // 算法单id
  int32 algorithm_type = 2;         // 1: TWAP 2: VWAP 3: Trigger
}

// 终止返回
message AlgorithmCancelResponse{
  string ret_code =1;//返回结果
  string ret_msg =2;//返回结果
}