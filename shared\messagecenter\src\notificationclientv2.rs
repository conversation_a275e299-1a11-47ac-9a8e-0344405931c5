use anyhow::{anyhow, Result};
use deadpool_lapin::lapin::{options::BasicPublishOptions, BasicProperties};
use deadpool_lapin::{Config, Pool, Runtime};
use futures_lite::stream::StreamExt;
use lapin::{options::*, types::FieldTable, Channel};
use prost::Message;
use protoes::phoenixnotification::NotificationMessage;
use std::sync::Arc;
use tokio::sync::{mpsc, RwLock};
use tokio::time::Duration;
use tracing::*;

#[derive(Debug, Clone)]
pub struct NotificationClientV2 {
    addr: String,
    publish_channel: Option<Channel>,
    consume_channel: Option<Channel>,
    routing_keys: Vec<String>,
    queue_name: String,
    exchanger: String,
    pool: Arc<Pool>,
    sender: mpsc::Sender<NotificationMessage>,
    reconnect_interval: Duration,
}

/// usage
// 1. Create message channel for receiving consumed messages
// let (tx, mut rx) = mpsc::channel::<NotificationMessage>(1000);
//
// 2. Initialize the client
// let client = NotificationClient2::new(
//     "phoenix_notification",           // exchanger name
//     "my_queue_name",                 // queue name
//     vec![                            // routing keys to bind
//         "order.*".to_string(),
//         "trade.*".to_string(),
//         "risk.*".to_string(),
//     ],
//     "amqp://user:pass@localhost:5672", // RabbitMQ URL
//     tx,                              // message sender
// ).await?;
// let client = Arc::new(RwLock::new(client));
//
// 3. Start auto-reconnection monitoring
// NotificationClientV2::start_with_auto_reconnect(client.clone()).await;

// // 4. Start consuming messages
// client.write().await.start_consuming().await?;

// // 5. Handle consumed messages
// let client_for_handler = client.clone();
// tokio::spawn(async move {
//     while let Some(message) = rx.recv().await {
//         info!("Received message: {:?}", message);
//         // Process your message here
//     }
// });

// // 6. Publishing messages
// let client_for_publish = client.clone();
// tokio::spawn(async move {
//     loop {
//         tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;

//         let message = NotificationMessage {
//             message_type: "test".to_string(),
//             content: "Hello World".to_string(),
//             timestamp: chrono::Utc::now().timestamp(),
//             ..Default::default()
//         };

//         if let Err(e) = client_for_publish.read().await.publish("order.created", &message).await {
//             error!("Failed to publish message: {}", e);
//         } else {
//             info!("Message published successfully");
//         }
//     }
// });
//
//
impl NotificationClientV2 {
    // Add unified initialization method
    pub async fn init_with_auto_management(exchanger: &str, queue_name: &str, routing_keys: Vec<String>, addr: &str, sender: mpsc::Sender<NotificationMessage>) -> Result<Arc<RwLock<Self>>> {
        let client = Self::new(exchanger, queue_name, routing_keys, addr, sender).await?;
        let client = Arc::new(RwLock::new(client));

        // Start auto-reconnect monitoring
        Self::start_with_auto_reconnect(client.clone()).await;

        // Start consuming
        if let Err(e) = client.write().await.start_consuming().await {
            error!("Failed to start consuming: {}", e);
        }

        info!("NotificationClientV2 initialized with auto-management");
        Ok(client)
    }
    pub async fn new(exchanger: &str, queue_name: &str, routing_keys: Vec<String>, addr: &str, sender: mpsc::Sender<NotificationMessage>) -> Result<Self> {
        let mut cfg = Config::default();
        cfg.url = Some(addr.into());
        let pool = Arc::new(cfg.create_pool(Some(Runtime::Tokio1))?);

        let mut client = NotificationClientV2 {
            addr: addr.to_string(),
            publish_channel: None,
            consume_channel: None,
            routing_keys,
            queue_name: queue_name.to_string(),
            exchanger: exchanger.to_string(),
            pool,
            sender,
            reconnect_interval: Duration::from_secs(5),
        };

        client.connect().await?;
        // info!("NotificationClient2 initialized successfully");
        Ok(client)
    }

    async fn connect(&mut self) -> Result<()> {
        let connection = self.pool.get().await?;

        let publish_channel = connection.create_channel().await?;
        let consume_channel = connection.create_channel().await?;

        self.publish_channel = Some(publish_channel);
        self.consume_channel = Some(consume_channel);

        info!("Connected to RabbitMQ successfully");
        Ok(())
    }

    pub async fn is_connected(&self) -> bool {
        match (&self.publish_channel, &self.consume_channel) {
            (Some(pub_ch), Some(cons_ch)) => pub_ch.status().connected() && cons_ch.status().connected(),
            _ => false,
        }
    }

    async fn reconnect(&mut self) -> Result<()> {
        warn!("Attempting to reconnect to RabbitMQ");

        // Clear existing channels
        self.publish_channel = None;
        self.consume_channel = None;

        // Recreate pool
        let mut cfg = Config::default();
        cfg.url = Some(self.addr.clone());
        self.pool = Arc::new(cfg.create_pool(Some(Runtime::Tokio1))?);

        // Reconnect
        self.connect().await?;
        info!("Reconnected to RabbitMQ successfully");
        Ok(())
    }

    pub async fn publish(&self, routing_key: &str, data: &NotificationMessage) -> Result<()> {
        if !self.is_connected().await {
            return Err(anyhow!("Not connected to RabbitMQ"));
        }

        let publish_channel = self.publish_channel.as_ref().ok_or_else(|| anyhow!("Publish channel not available"))?;

        let mut payload = Vec::new();
        data.encode(&mut payload)?;

        let confirm = publish_channel
            .basic_publish(&self.exchanger, routing_key, BasicPublishOptions::default(), &payload, BasicProperties::default())
            .await?
            .await?;

        if confirm.is_nack() {
            return Err(anyhow!("Message not confirmed by broker"));
        }

        Ok(())
    }

    pub async fn start_consuming(&mut self) -> Result<()> {
        if !self.is_connected().await {
            return Err(anyhow!("Not connected to RabbitMQ"));
        }

        let consume_channel = self.consume_channel.as_ref().ok_or_else(|| anyhow!("Consume channel not available"))?;

        // Declare queue
        consume_channel
            .queue_declare(
                &self.queue_name,
                QueueDeclareOptions {
                    passive: false,
                    durable: false,
                    exclusive: false,
                    auto_delete: true,
                    nowait: false,
                },
                FieldTable::default(),
            )
            .await?;

        // Bind routing keys
        for routing_key in &self.routing_keys {
            consume_channel
                .queue_bind(&self.queue_name, &self.exchanger, routing_key, QueueBindOptions::default(), FieldTable::default())
                .await?;
        }

        let mut consumer = consume_channel.basic_consume(&self.queue_name, "", BasicConsumeOptions::default(), FieldTable::default()).await?;

        let sender = self.sender.clone();
        tokio::spawn(async move {
            while let Some(delivery) = consumer.next().await {
                match delivery {
                    Ok(delivery) => {
                        if let Ok(message) = NotificationMessage::decode(&*delivery.data) {
                            if let Err(e) = sender.send(message).await {
                                error!("Failed to send message to channel: {}", e);
                            }
                        }
                        if let Err(e) = delivery.ack(BasicAckOptions::default()).await {
                            error!("Failed to ack message: {}", e);
                        }
                    }
                    Err(e) => {
                        error!("Consumer error: {}", e);
                        break;
                    }
                }
            }
        });

        Ok(())
    }

    pub async fn start_with_auto_reconnect(client: Arc<RwLock<Self>>) {
        let reconnect_interval = client.read().await.reconnect_interval;
        let mut interval = tokio::time::interval(reconnect_interval);

        tokio::spawn(async move {
            loop {
                interval.tick().await;

                let is_connected = client.read().await.is_connected().await;
                if !is_connected {
                    warn!("Connection lost, attempting to reconnect");

                    if let Err(e) = client.write().await.reconnect().await {
                        error!("Reconnection failed: {}", e);
                        continue;
                    }

                    if let Err(e) = client.write().await.start_consuming().await {
                        error!("Failed to restart consuming after reconnect: {}", e);
                    }
                }
            }
        });
    }

    pub async fn update_routing_keys(&mut self, new_keys: Vec<String>) -> Result<()> {
        if !self.is_connected().await {
            return Err(anyhow!("Not connected to RabbitMQ"));
        }

        let consume_channel = self.consume_channel.as_ref().ok_or_else(|| anyhow!("Consume channel not available"))?;

        // Unbind old keys
        for key in &self.routing_keys {
            consume_channel.queue_unbind(&self.queue_name, &self.exchanger, key, FieldTable::default()).await?;
        }

        // Bind new keys
        for key in &new_keys {
            consume_channel.queue_bind(&self.queue_name, &self.exchanger, key, QueueBindOptions::default(), FieldTable::default()).await?;
        }

        self.routing_keys = new_keys;
        Ok(())
    }
}
