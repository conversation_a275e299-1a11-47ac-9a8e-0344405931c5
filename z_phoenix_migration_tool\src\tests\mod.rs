// use crate::dataservice::dbsetup::DbConnection;
// use crate::dataservice::old_stock_entities::prelude::Tstockposition;
// use utility::loggings::log_init;

#[tokio::test]
async fn test_db() {
    // log_init();
    // let db = DbConnection::new("mysql://companytest:<EMAIL>:13301/stock_test").await;
    // let ret = Tstockposition::query_many2(200840, &db).await;
    // log::info!("{:#?}", ret.unwrap())
}
