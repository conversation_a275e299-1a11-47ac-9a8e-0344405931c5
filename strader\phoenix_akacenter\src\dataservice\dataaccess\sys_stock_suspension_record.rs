use crate::dataservice::{
    entities::{
        prelude::{SysStockSuspensionRecord, SysStockSuspensionRecordEntity},
        sys_stock_suspension_record,
    },
};
use dbconnection::DbConnection;
use anyhow::{anyhow, Result};
use rust_decimal::Decimal;
use sea_orm::{ColumnTrait, DbErr, EntityTrait, FromQueryResult, QueryFilter, QuerySelect};

#[derive(Debug, FromQueryResult)]
pub struct StockSuspensionRecordSelectRes {
    pub stock_id: i32,
    pub susp_date: i32,
    pub process_date: i64,
    pub margin_rate_increment: Decimal,
}

impl SysStockSuspensionRecord {
    pub async fn find_all(db: &DbConnection) -> Result<Vec<StockSuspensionRecordSelectRes>> {
        let ret_data: Result<Vec<StockSuspensionRecordSelectRes>, DbErr> = SysStockSuspensionRecordEntity::find()
            .filter(sys_stock_suspension_record::Column::SuspState.eq(1))
            .column(sys_stock_suspension_record::Column::StockId)
            .column(sys_stock_suspension_record::Column::SuspDate)
            .column(sys_stock_suspension_record::Column::ProcessDate)
            .column(sys_stock_suspension_record::Column::MarginRateIncrement)
            .into_model::<StockSuspensionRecordSelectRes>()
            .all(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }
}
