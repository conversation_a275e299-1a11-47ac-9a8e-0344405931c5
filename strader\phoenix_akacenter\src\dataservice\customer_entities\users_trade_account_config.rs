//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_trade_account_config")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub trade_account_id: i64,
    pub return_type: i8,
    pub show_rank: i8,
    pub rank_cate: i8,
    pub allow_pc_login: i8,
    pub allow_app_login: i8,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub deposit_ratio: Decimal,
    #[sea_orm(column_type = "Decimal(Some((10, 4)))")]
    pub yu_jing: Decimal,
    #[sea_orm(column_type = "Decimal(Some((10, 4)))")]
    pub ping_cang: Decimal,
    pub is_open_discount: i8,
    #[sea_orm(column_type = "Decimal(Some((16, 8)))")]
    pub year_rate: Decimal,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub lever_rate: Decimal,
    #[sea_orm(column_type = "Decimal(Some((10, 4)))", nullable)]
    pub gem_limit: Option<Decimal>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
