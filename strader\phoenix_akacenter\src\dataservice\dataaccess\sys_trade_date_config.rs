use crate::dataservice::{
    dbsetup::DbConnection,
    entities::{
        prelude::{SysTradeDateConfig, SysTradeDateConfigEntity},
        sys_trade_date_config,
    },
};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, Condition, DbErr, EntityTrait, QueryFilter};

impl SysTradeDateConfig {
    pub async fn _find_all(db: &DbConnection) -> Result<Vec<SysTradeDateConfig>> {
        let ret_data: Result<Vec<SysTradeDateConfig>, DbErr> = SysTradeDateConfigEntity::find().all(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }

    pub async fn find_by_condition(db: &DbConnection, condition: Condition) -> Result<Vec<SysTradeDateConfig>> {
        let ret_data: Result<Vec<SysTradeDateConfig>, DbErr> = SysTradeDateConfigEntity::find().filter(condition).all(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }

    #[allow(dead_code)]
    pub async fn find_by_id(db: &DbConnection, commodity_id: i64, market_id: i64) -> Result<SysTradeDateConfig> {
        let ret_data: Result<Option<SysTradeDateConfig>, DbErr> = SysTradeDateConfigEntity::find()
            .filter(sys_trade_date_config::Column::CommodityId.eq(commodity_id))
            .filter(sys_trade_date_config::Column::MarketId.eq(market_id))
            .one(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        let data = ret_data.unwrap();
        return match data {
            Some(v) => Ok(v),
            None => {
                let ret_data: Result<Option<SysTradeDateConfig>, DbErr> = SysTradeDateConfigEntity::find()
                    .filter(sys_trade_date_config::Column::CommodityId.eq(0))
                    .filter(sys_trade_date_config::Column::MarketId.eq(market_id))
                    .one(db.get_connection())
                    .await;
                if ret_data.is_err() {
                    return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
                }

                let data = ret_data.unwrap();
                match data {
                    Some(v) => Ok(v),
                    None => Err(anyhow!("SysTradeDateConfig数据不存在")),
                }
            }
        };
    }
}
