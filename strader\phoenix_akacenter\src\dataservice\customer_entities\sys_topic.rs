//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "sys_topic")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub topic_type: i8,
    pub topic_title: String,
    pub topic_desc: String,
    pub topic_status: i8,
    pub create_time: i64,
    pub create_name: String,
    pub topic_category: i8,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
