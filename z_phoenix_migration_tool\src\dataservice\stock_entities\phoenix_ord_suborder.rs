//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_ord_suborder")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    #[sea_orm(unique)]
    pub sub_id: i64,
    pub sys_date: i32,
    pub order_no: i64,
    pub unit_id: i64,
    pub stock_code: String,
    pub channel_id: i32,
    pub channel_type: i32,
    pub order_amount: i32,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub order_price: Decimal,
    pub price_type: i32,
    pub confirm_no: String,
    pub order_status: i32,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub deal_value: Decimal,
    pub deal_amount: i32,
    pub modify_time: i64,
    pub create_time: i64,
    pub relate_order: i64,
    pub cancel_flag: i32,
    pub remark: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
