// 美股服务proto文件

syntax = "proto3";
package phoenixusserver;



service UsServer{
  rpc PostUsSubscribeHqMsg(stream UsSubscribeHqMsgReq) returns (stream HqNotificationMessage) {}	//行情订阅接口
  rpc PostUsCurrentKlineHq(UsKLineRequest) returns(UsKLineResp){};			//当前K线
  rpc PostUsCurrentFenShiHq (UsTimeLineReq) returns(UsTimeLineResp){};		//当前分时
  rpc PostUsTickHq(UsTickHqReq) returns (UsTickHqResp) {};				//最新TICK数据
}

message UsTickHqReq {
  repeated string secuIds = 1;       // 证券唯一标识列表  字符串数组，每个元素为证券的唯一标识，由市场标识+证券代码构成，如腾讯的唯一标识为hk00700
}

message UsTickHqResp {
  int32 code = 1;                    // 错误码，0表示成功，其他表示失败
  string msg = 2;                    // 消息
  repeated Security list = 3;        // 证券数据列表
}

message Security {
  string market = 1;         // 市场标识
  string symbol = 2;         // 证券代码
  double latest_price = 3;   // 最新价
  double open = 4;           // 开盘价
  double low = 5;            // 最低价
  double close = 6;          // 收盘价
  double high = 7;           // 最高价
  int64 latest_time = 8;     // 最近行情时间
  double pre_close = 9;      // 昨收价
  double turnover = 10;      // 总成交额
  int64 volume = 11;         // 总成交量
  double bid_price = 12;     // 买一价
  int64 bid_size = 13;       // 买一量
  double ask_price = 14;     // 卖一价
  int64 ask_size = 15;       // 卖一量
  double up_limit = 16;      // 涨停价
  double down_limit = 17;    // 跌停价
  double qty_unit = 18;      // 实时价差
  int32 trd_status = 19;     // 证券状态   取值 	说明 0 未知 1停牌 2港股波动中断 3未上市  4暂停上市(A股)  5退市  6交易中
  double q_change_value = 20;     //
  double q_change_rate = 21;     //
  double q_last_turnover = 22;     //
  int64 q_last_qty = 23;     //
  double q_average_price = 24;     //
}
/*-----------------------------------------------------------------------------*/

message TimePoint {
  double price = 1;             // 最新价
  double avg = 2;               // 均价
  int64 latest_time = 3;        // 最近行情时间
  double pre_close = 4;         // 昨收价
  double amount = 5;            // 成交额
  int64 volume = 6;             // 成交量
  double netchng = 7;           // 涨跌额
  double pctchng = 8;           // 涨跌幅
}

message UsTimeLineResp {
  int32 code = 1;               // 错误码，0表示成功，其他表示失败
  string msg = 2;               // 消息
  repeated TimePoint points = 3; // 分时点数据列表
}

message UsTimeLineReq {
  string secuId = 1;            // 证券的唯一标识
  int32 type = 2;               // 分时类型，0：一日分时，1：五日分时
}

/*-----------------------------------------------------------------------------*/

message KLine {
  double open = 1;              // 开盘价
  double close = 2;             // 收盘价
  int64 latest_time = 3;        // 最近行情时间
  double pre_close = 4;         // 昨收价
  double amount = 5;            // 成交额
  int64 volume = 6;             // 成交量
  double high = 7;              // 最高价
  double low = 8;               // 最低价
}

message UsKLineResp {
  int32 code = 1;               // 错误码，0表示成功，其他表示失败
  string msg = 2;               // 消息
  repeated KLine klines = 3;    // K线数据列表
}

message UsKLineRequest {
  string secuId = 1;            // 证券的唯一标识
  int32 type = 2;               // K线类型  0 	默认值，不返回任何数据;1:1分钟K线;2:5分钟K线; 3:10分钟K线;4:15分钟K线;5:30分钟K线;6:60分钟K线;7:1日K线;8:1周K线;9:1月K线;10:3月K线;11:6月K线;12:一年K线
  int64 start = 3;              // 当前页的起始时间
  int32 right = 4;              // 复权类型
  int32 count = 5;              // 每页大小
}

/*-----------------------------------------------------------------------------*/

message UsSubscribeHqMsgReq {
  string op = 1;            // 操作标识符 sub 订阅 unsub 取消订阅
  repeated string topiclist = 2;  //示例:["rt.hk.00700", "tk.hk.00700","ob.hk.00700"] 7. 行情类型 rt 	realtime, 实时行情 tk 	tick, 逐笔成交 ob 	orderbook, 买卖盘
}

message HqNotificationMessage
{
  HqNotificationType msg_type = 1;  //1：消息类型
  HqMessageBody msg_body = 2; //消息内容
}

// 通知类别
enum HqNotificationType {
  UndefNotificationType  = 0;
  TickMsg            = 1;   // 实时行情
  TickQuoteMsg     = 2;   // tick行情
  OrderBookMsg           = 3;   // 买卖盘
}

message HqMessageBody{
  Security msg_security = 1;        //实时行情
  TickQuote msg_tickquote = 2;  // tick行情
  OrderBook msg_orderbook = 3;      // 买卖盘
}

// Tick行情
message TickQuote {
  int32 seq = 1;                 // 行情序号
  int64 time = 2;                // 行情时间
  double price = 3;              // 价格
  double volume = 4;             // 成交量
  int32 direction = 5;           // 买卖方向
  int64 trd_type = 6;            // 逐笔类型 逐笔类型，港股所特有，数值与类型之间的对应关系为：4:P 22:M 100:Y 101:X 102:D 103:U
}

// 买卖盘
message OrderBook {
  double bid_price = 1;          // 买盘价
  int64 bid_volume = 2;          // 买盘量
  int64 bid_order_count = 3;     // 买委托订单个数
  double ask_price = 4;          // 卖盘价
  int64 ask_volume = 5;          // 卖盘量
  int64 ask_order_count = 6;     // 卖委托订单个数
}