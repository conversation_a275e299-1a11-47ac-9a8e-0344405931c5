[package]
name = "phoenix_ordercenter"
version = "0.2.0"
edition = "2021"
description = "订单中心 build time: 2025-07-07"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
utility = { workspace = true }
common = { workspace = true }
akaclient = { workspace = true }
messagecenter = { workspace = true }
protoes = { workspace = true }

accountriskcenterclient = { workspace = true }
riskcenterclient = { workspace = true }

tokio = { workspace = true }
tokio-stream = { workspace = true }
async-stream = { workspace = true }
tonic = { workspace = true }
futures-util = { workspace = true }
futures = { workspace = true }
futures-lite = { workspace = true }
prost = { workspace = true }
prost-types = { workspace = true }
config = { workspace = true }
# log = { workspace = true }
tracing = { workspace = true }
anyhow = { workspace = true }
# dashmap = "5.4.0"
# 时间
chrono = { workspace = true }
# time = "0.3"
# 序列化
serde = { workspace = true }
serde_json = { workspace = true }
# serde_yaml = { version = "0.9", optional = true }
# h2 = "0.3"
# sql
sea-orm = { workspace = true }
# mq
lapin = { workspace = true }
# rgb = "0.8.34"
# console = "0.15.0"
rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }
# [build-dependencies]
# # tonic-build = { workspace = true, features = ["prost"] }
# tonic-build.workspace = true

# [[bin]]
# name = "s"
# path = "./src/main.rs"

# [[bin]]
# name = "OrderExec"
# path = "./src/test1.rs"
# # required-features = ["default"]
