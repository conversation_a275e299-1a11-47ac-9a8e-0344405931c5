use crate::dataservice::entities::prelude::{SysTradeChannelEntity, SysTradeConfigChannelEntity};
use crate::dataservice::entities::{sys_trade_channel, sys_trade_config_channel};
use crate::dataservice::{
    entities::{
        prelude::{SysTradeConfigCommodity, SysTradeConfigCommodityEntity},
        sys_trade_config_commodity,
    },
};
use dbconnection::DbConnection;
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, Condition, DbErr, EntityTrait, FromQueryResult, QueryFilter, QuerySelect};

#[derive(Debug, FromQueryResult)]
#[allow(unused)]
pub struct SysTradeConfigCommoditySelectRes {
    pub id: i64,
    pub trade_config_id: i64,
    pub trade_channel_id: i64,
    pub level: i32,
    pub user_id: i64,
    pub name: String,
    pub state: i32,
    pub inner_type: i8,
    pub qfstate: i8,
}

impl SysTradeConfigCommodity {
    #[allow(unused)]
    pub async fn find_by_commodity_id(db: &DbConnection, commodity_id: i64) -> Result<SysTradeConfigCommodity> {
        let ret_data: Result<Option<SysTradeConfigCommodity>, DbErr> = SysTradeConfigCommodityEntity::find()
            .filter(sys_trade_config_commodity::Column::CommodityId.eq(commodity_id))
            .one(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        let data = ret_data.unwrap();
        return match data {
            Some(v) => Ok(v),
            None => Err(anyhow!("SysTradeConfigCommodity数据不存在")),
        };
    }
    #[allow(unused)]
    pub async fn find_join_others_by_condition(db: &DbConnection, condition: Condition) -> Result<Vec<SysTradeConfigCommoditySelectRes>> {
        let ret_data = SysTradeConfigCommodityEntity::find()
            .inner_join(SysTradeConfigChannelEntity)
            .inner_join(SysTradeChannelEntity)
            .select_only()
            .column(sys_trade_config_channel::Column::Id)
            .column(sys_trade_config_commodity::Column::TradeConfigId)
            .column(sys_trade_config_channel::Column::TradeChannelId)
            .column(sys_trade_config_channel::Column::Level)
            .column(sys_trade_config_channel::Column::UserId)
            .column(sys_trade_channel::Column::Name)
            .column(sys_trade_config_channel::Column::State)
            .column(sys_trade_channel::Column::InnerType)
            .column(sys_trade_channel::Column::Qfstate)
            .filter(condition)
            .into_model::<SysTradeConfigCommoditySelectRes>()
            .all(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }
}
