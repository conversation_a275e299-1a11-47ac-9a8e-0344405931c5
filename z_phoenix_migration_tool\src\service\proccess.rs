// use std::collections::HashMap;
use crate::dataservice::dbsetup::DbConnection;
use crate::dataservice::old_stock_entities::prelude::{Tstockposition, Tunitasset};
use crate::dataservice::stock_entities::prelude::{PhoenixAstStockposition, PhoenixAstUnitasset};
use anyhow::{anyhow, Result};
use rust_decimal::prelude::ToPrimitive;
use sea_orm::DatabaseTransaction;
// use crate::protofiles::phoenixmigrationtool::MigrationDataReq;

#[macro_export]
macro_rules! result {
    ($a: expr, $b: expr) => {
        match $a {
            Ok(c) => c,
            Err(e) => {
                log::error!("{}:{}", $b, e);
                return Err(anyhow!(format!("{}:{:?}", $b, e)));
            }
        }
    };
}

pub async fn migration_data(txn: &DatabaseTransaction, src_db: &DbConnection, old_unit_asset: &Tunitasset, rate: f64, f_fill: bool) -> Result<()> {
    let mut new_unit_asset = vec![result!(PhoenixAstUnitasset::covert_form_tunitasset(src_db, old_unit_asset, rate).await, "数据库查询失败")];

    match f_fill {
        true => {
            result!(
                PhoenixAstUnitasset::save_many(&mut new_unit_asset, txn).await,
                format!("保存资产数据库失败unit_id：{},err", old_unit_asset.l_unit_id)
            );
        }
        false => {
            let id = result!(PhoenixAstUnitasset::insert_many(&new_unit_asset, txn).await, format!("插入资产数据库失败unit_id：{},err", old_unit_asset.l_unit_id));
            if id == 0 {
                log::warn!("该账户已存在：{}，跳过", old_unit_asset.l_unit_id);
                return Err(anyhow!("该账户已存在：{}，跳过", old_unit_asset.l_unit_id));
            }
            println!("资产表最后插入id: {}", id);
        }
    }

    let old_stock_positions = result!(Tstockposition::query_many(old_unit_asset.l_unit_id.to_i64().unwrap_or_default(), src_db).await, "数据库查询失败");
    if old_stock_positions.is_empty() {
        return Ok(());
    }

    let mut new_stock_positions = Vec::new();
    for model in old_stock_positions.iter() {
        let new_model = result!(PhoenixAstStockposition::covert_form_tstockposition(model, old_unit_asset.en_credit_multiple).await, "数据库查询失败");
        new_stock_positions.push(new_model);
    }

    log::info!("账户：{} 找到{}条持仓记录", old_unit_asset.l_unit_id, new_stock_positions.len());

    match f_fill {
        true => {
            let _result = result!(
                PhoenixAstStockposition::save_many(&mut new_stock_positions, txn).await,
                format!("保存持仓数据库失败unit_id：{},err", old_unit_asset.l_unit_id)
            );
        }
        false => {
            let id = result!(
                PhoenixAstStockposition::insert_many(&new_stock_positions, txn).await,
                format!("插入持仓数据库失败unit_id：{},err", old_unit_asset.l_unit_id)
            );
            if id == 0 {
                log::warn!("该账户已存在：{}，跳过", old_unit_asset.l_unit_id);
                return Err(anyhow!("该账户已存在：{}，跳过", old_unit_asset.l_unit_id));
            }
            println!("插入id: {}", id);
        }
    }

    Ok(())
}
