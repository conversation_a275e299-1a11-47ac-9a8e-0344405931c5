//! Account Risk Center Client with Graceful Shutdown Support
//!
//! This client provides connection management for the Phoenix Account Risk Center service
//! with built-in heartbeat monitoring and graceful shutdown capabilities.
//!
//! # Example Usage
//!
//! ```no_run
//! use accountriskcenterclient::AccountRiskClient;
//! use std::time::Duration;
//!
//! #[tokio::main]
//! async fn main() -> anyhow::Result<()> {
//!     // Initialize the client
//!     let client = AccountRiskClient::init(
//!         "http://localhost:50052",  // endpoint
//!         "my_client_id",            // client_id
//!         "my_service_name"          // service_name
//!     ).await;
//!     
//!     // Use the client for operations
//!     let margin_ratio = client.query_margin_ratio(123, 456).await?;
//!     
//!     // Gracefully shutdown when done
//!     client.shutdown().await;
//!     
//!     Ok(())
//! }
//! ```
//!
//! # Graceful Shutdown
//!
//! The client supports graceful shutdown through the `shutdown()` method which:
//! - Stops the background connection management task
//! - Stops the heartbeat client
//! - Clears the active client connection
//! - Ensures proper cleanup of resources
//!
//! The background task will respond to shutdown signals quickly (within 1 second)
//! even during heartbeat monitoring loops.
//!
//! # External Coordination
//!
//! For advanced use cases, you can get a shutdown sender for external coordination:
//!
//! ```no_run
//! use accountriskcenterclient::AccountRiskClient;
//!
//! #[tokio::main]
//! async fn main() -> anyhow::Result<()> {
//!     let client = AccountRiskClient::init(
//!         "http://localhost:50052",
//!         "my_client_id",
//!         "my_service_name"
//!     ).await;
//!
//!     // Get shutdown sender for external coordination
//!     let shutdown_sender = client.get_shutdown_sender();
//!
//!     // In another part of the system
//!     let _ = shutdown_sender.send(()); // Signal shutdown
//!
//!     Ok(())
//! }
//! ```

use anyhow::{Result, anyhow};
use common::logclient::log_error;
use heartbeatclient::HeartbeatClient;
// use core::error;
use protoes::phoenixaccountriskcenter::account_risk_center_client::AccountRiskCenterClient;
use protoes::phoenixaccountriskcenter::{
    AccountAvailableAmountRequest, AccountAvailableAmountResponse, MarginRatioReq, MarginRatioResp, PhoenixStockPositionRequest, PhoenixStockPositionResponse, UserAssetsReq, UserAssetsResp, UserPositionReq,
    UserPositionResp,
};
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::{RwLock, broadcast};
use tonic::transport::Channel;
use tracing::*;

#[derive(Clone)]
pub struct AccountRiskClient {
    client: Arc<RwLock<Option<AccountRiskCenterClient<Channel>>>>,
    heartbeat: HeartbeatClient,
    shutdown_tx: broadcast::Sender<()>,
    // uri: String,
}

impl AccountRiskClient {
    pub async fn init(endpoint: &str, client_id: &str, service_name: &str) -> Self {
        // 等待 HeartbeatClient 初始化（包含重試邏輯）
        // let heartbeat = HeartbeatClient::new(endpoint, client_id.to_string(), service_name.to_string()).await;
        let heartbeat = HeartbeatClient::new(endpoint, client_id.to_string(), service_name.to_string(), true).await;
        let client = Arc::new(RwLock::new(None));
        if let Ok(channel) = tonic::transport::Channel::from_shared(endpoint.to_string()) {
            if let Ok(channel) = channel.connect().await {
                let proto_client = AccountRiskCenterClient::new(channel);
                let mut wr = client.write().await;
                *wr = Some(proto_client);
            } else {
                log_error(&format!("connect to AccountRiskCenter failed: {:?}", endpoint)).await;
                error!("connect to AccountRiskCenter failed: {:?}", endpoint);
            }
        }

        let client_clone = client.clone();

        // Create shutdown channel for the background task
        let (shutdown_tx, _shutdown_rx) = broadcast::channel(1);

        // Clone values to move into the async task
        let endpoint_owned = endpoint.to_string();
        let service_name_owned = service_name.to_string();
        let heartbeat_clone = heartbeat.clone();
        let shutdown_tx_clone = shutdown_tx.clone();

        // 啟動後台任務管理業務客戶端連接
        tokio::spawn(async move {
            let mut shutdown_rx1 = shutdown_tx_clone.subscribe();
            let retry_interval = 10;
            loop {
                tokio::select! {
                    _ = shutdown_rx1.recv() => {
                        info!(service = %service_name_owned, "AccountRiskClient background task received shutdown signal. Exiting.");
                        break;
                    },
                    _ = async {
                        let mut shutdown_rx2 = shutdown_tx_clone.subscribe();
                        info!(service = %service_name_owned, endpoint = %endpoint_owned, "Attempting to connect Service client");
                        match tonic::transport::Channel::from_shared(endpoint_owned.clone()) {
                            Ok(channel) => match channel.connect().await {
                                Ok(channel) => {
                                    {
                                        if client_clone.read().await.is_none() {
                                            let proto_client = AccountRiskCenterClient::new(channel);
                                            let mut wr = client_clone.write().await;
                                            *wr = Some(proto_client);
                                            // *client_clone.write().await.unwrap() = Some(proto_client);
                                            info!(service = %service_name_owned, "Service client connected,");
                                        }
                                    } // 等待 HeartbeatClient 報告不健康，然後重試
                                    loop {
                                        tokio::select! {
                                            _ = shutdown_rx2.recv() => {
                                                info!(service = %service_name_owned, "AccountRiskClient health check received shutdown signal. Exiting.");
                                                return; // Exit the entire async block
                                            },
                                            _ = tokio::time::sleep(Duration::from_secs(1)) => {
                                                if !heartbeat_clone.is_healthy() {
                                                    break; // Exit health check loop
                                                }
                                            }
                                        }
                                    }
                                    {
                                        let mut wr = client_clone.write().await;
                                        *wr = None::<AccountRiskCenterClient<tonic::transport::Channel>>;
                                        error!(service = %service_name_owned, "Service client disconnected due to unhealthy state");
                                        log_error(&format!("Service client disconnected due to unhealthy state")).await;
                                    }
                                }
                                Err(e) => {
                                    error!(service = %service_name_owned, "Service client connection failed: {}", e);
                                    log_error(&format!("Service client connection failed: {}", e)).await;
                                }
                            },
                            Err(e) => {
                                error!(service = %service_name_owned, "Invalid endpoint: {}", e);
                                log_error(&format!("Invalid endpoint: {}", e)).await;
                            }
                        }
                        warn!(service = %service_name_owned, "Retrying Service1 client connection in {retry_interval} seconds");
                        tokio::time::sleep(Duration::from_secs(retry_interval)).await;
                    } => {}
                }
            }
        });

        Self {
            client,
            heartbeat,
            shutdown_tx,
            // uri: endpoint.to_string(),
        }
    }

    /// Helper method to get a healthy client connection
    /// Returns Ok(client) if healthy and connected, Err otherwise
    ///
    /// # Performance optimizations:
    /// - Fast health check (no async ops)
    /// - Functional chain with lazy error creation
    /// - Automatic lock release (no explicit drop needed)
    async fn get_healthy_client(&self) -> Result<AccountRiskCenterClient<Channel>> {
        // Fast health check first (synchronous)
        if !self.heartbeat.is_healthy() {
            return Err(anyhow!("账户风控中心服务不健康，请稍后重试"));
        }

        // Functional chain: read lock -> clone if Some -> error if None
        self.client.read().await.as_ref().cloned().ok_or_else(|| anyhow!("账户风控中心客户端未连接"))
    }

    /// Helper method to handle client errors and reset connection
    async fn handle_client_error(&self, error: tonic::Status, operation: &str) -> anyhow::Error {
        // Reset client connection on error
        let mut write_guard = self.client.write().await;
        *write_guard = None;
        drop(write_guard);

        // Log error asynchronously
        let error_msg = format!("账户风控中心 {} 操作失败: {:?}", operation, error);
        log_error(&error_msg).await;

        anyhow!("账户风控中心操作失败")
    }

    /// Gracefully shutdown the AccountRiskClient
    /// This will stop the background connection management task and cleanup resources
    pub async fn shutdown(&self) {
        info!("Initiating graceful shutdown of AccountRiskClient");

        // Stop the heartbeat client first
        self.heartbeat.stop();

        // Send shutdown signal to background task
        if let Err(e) = self.shutdown_tx.send(()) {
            warn!("Failed to send shutdown signal: {:?}", e);
        }

        // Clear the client connection
        {
            let mut write_guard = self.client.write().await;
            *write_guard = None;
        }

        // Give a brief moment for background tasks to complete
        tokio::time::sleep(Duration::from_millis(100)).await;

        info!("AccountRiskClient shutdown completed");
    }

    /// Get a shutdown sender for external coordination
    /// This allows other parts of the system to trigger shutdown
    pub fn get_shutdown_sender(&self) -> broadcast::Sender<()> {
        self.shutdown_tx.clone()
    }

    pub async fn query_margin_ratio(&self, user_id: i64, stock_id: i64) -> Result<MarginRatioResp> {
        // Get healthy client (combines health check and client acquisition)
        let mut client = self.get_healthy_client().await?;

        let req = MarginRatioReq { user_id, stock_id };
        match client.query_margin_ratio(req).await {
            Ok(response) => Ok(response.into_inner()),
            Err(err) => Err(self.handle_client_error(err, "query_margin_ratio").await),
        }
    }

    pub async fn query_user_assets(&self, req: &UserAssetsReq) -> Result<UserAssetsResp> {
        // Get healthy client (combines health check and client acquisition)
        let mut client = self.get_healthy_client().await?;

        match client.query_user_assets(req.clone()).await {
            Ok(response) => Ok(response.into_inner()),
            Err(err) => Err(self.handle_client_error(err, "query_user_assets").await),
        }
    }

    pub async fn query_user_positions(&self, req: &UserPositionReq) -> Result<UserPositionResp> {
        // Get healthy client (combines health check and client acquisition)
        let mut client = self.get_healthy_client().await?;

        match client.query_user_positions(req.clone()).await {
            Ok(response) => Ok(response.into_inner()),
            Err(err) => Err(self.handle_client_error(err, "query_user_positions").await),
        }
    }

    pub async fn query_stock_positions(&self, req: &PhoenixStockPositionRequest) -> Result<PhoenixStockPositionResponse> {
        // Get healthy client (combines health check and client acquisition)
        let mut client = self.get_healthy_client().await?;

        match client.query_stock_positions(req.clone()).await {
            Ok(response) => Ok(response.into_inner()),
            Err(err) => Err(self.handle_client_error(err, "query_stock_positions").await),
        }
    }

    pub async fn query_account_available_amount(&self, req: &AccountAvailableAmountRequest) -> Result<AccountAvailableAmountResponse> {
        // Get healthy client (combines health check and client acquisition)
        let mut client = self.get_healthy_client().await?;

        match client.query_account_available_amount(req.clone()).await {
            Ok(response) => Ok(response.into_inner()),
            Err(err) => Err(self.handle_client_error(err, "query_account_available_amount").await),
        }
    }

    pub async fn query_user_currency(&self, user_id: i64, unit_id: i64) -> Result<String> {
        // Get healthy client (combines health check and client acquisition)
        let mut client = self.get_healthy_client().await?;

        // info!("query user currency user_id:{}, unit_id:{}", user_id, unit_id);
        let mut request = UserAssetsReq::default();
        request.user_id.push(user_id);
        request.unit_id.push(unit_id);

        match client.query_user_assets(request).await {
            Ok(response) => {
                let user_assets = response.into_inner().assets;
                if user_assets.is_empty() {
                    error!("account risk user assets is empty");
                    return Err(anyhow!("account risk user assets is empty"));
                }
                info!("account risk user assets: {:?}", user_assets);
                let currency = if let Some(assets) = user_assets.iter().find(|x| x.unit_id == unit_id) {
                    assets.currency.to_owned()
                } else {
                    "".to_string()
                };
                if currency.is_empty() {
                    error!("没有找到用户资产: {}, {}", user_id, unit_id);
                    return Err(anyhow!("account risk user assets is empty"));
                }
                return Ok(currency);
            }
            Err(status) => {
                error!("{:?}", status);
                log_error(format!("account risk center err code: {:?} message: {:?}", status.code(), status.message()).as_str()).await;
                return Err(anyhow!("account risk center error"));
            }
        }
    }
}
// }
