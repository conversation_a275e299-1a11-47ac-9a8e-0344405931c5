use anyhow::{Result, anyhow};
use common::logclient::log_error;
use heartbeatclient::HeartbeatClient;
// use core::error;
use protoes::phoenixaccountriskcenter::account_risk_center_client::AccountRiskCenterClient;
use protoes::phoenixaccountriskcenter::{
    AccountAvailableAmountRequest, AccountAvailableAmountResponse, MarginRatioReq, MarginRatioResp, PhoenixStockPositionRequest, PhoenixStockPositionResponse, UserAssetsReq, UserAssetsResp, UserPositionReq,
    UserPositionResp,
};
use std::sync::Arc;
use tokio::sync::RwLock;
use tonic::transport::Channel;
use tracing::*;

#[derive(Clone)]
pub struct AccountRiskClient {
    client: Arc<RwLock<Option<AccountRiskCenterClient<Channel>>>>,
    hearbeat: HeartbeatClient,
    uri: String,
}

impl AccountRiskClient {
    pub async fn new(endpoint: &str, client_id: &str, service_name: &str) -> Result<Self, Box<dyn std::error::Error>> {
        // 等待 HeartbeatClient 初始化（包含重試邏輯）
        let heartbeat = HeartbeatClient::new(endpoint, client_id.to_string(), service_name.to_string()).await;

        // 使用相同的 endpoint 創建 Service1Client，無需額外重試
        let channel = tonic::transport::Channel::from_shared(endpoint.to_string())
            .map_err(|e| format!("Invalid endpoint: {}", e))?
            .connect()
            .await
            .map_err(|e| format!("Connection failed: {}", e))?;
        let client = Arc::new(RwLock::new(Some(AccountRiskCenterClient::new(channel))));

        Ok(Self {
            client,
            hearbeat: heartbeat,
            uri: endpoint.to_string(),
        })
    }

    pub async fn init(url: String) -> Self {
        // info!(url, "账户风险中心");
        // let client = Arc::new(RwLock::new(None::<AccountRiskCenterClient<Channel>>));
        // // let client = AccountRiskCenterClient::connect(url.clone()).await;
        // // if client.is_ok() {
        // //     // info!("账户风险中心连接成功");
        // //     let mut wr = client.unwrap().write().await;
        // //     *wr = Some(client.unwrap());
        // // }
        // let uri = url.clone();
        let channel = tonic::transport::Channel::from_shared(url.clone()).expect("invalid url").connect().await.expect("connection error");
        let client = AccountRiskCenterClient::new(channel.clone());
        info!("账户风控中心连接成功......");
        let heartbeat = HeartbeatClient::init(channel.clone(), "client_id".to_string(), "accountriskcenter".to_string()).await;

        Self {
            client: Arc::new(RwLock::new(Some(client))),
            hearbeat: heartbeat,
            uri: url,
        }

        // let client = Arc::new(RwLock::new(match AccountRiskCenterClient::connect(url.clone()).await {
        //     Ok(client) => Some(client),
        //     Err(err) => {
        //         error!("connect to accountriskcenter failed: {:?}", &err);
        //         log_error(&format!("Connected to accountriskcenter failed:{:?}", &err)).await;
        //         None
        //     }
        // }));
        // let client = AccountRiskClient { client: client, uri: url };
        // client.start_reconnect_listen().await;
        // client
    }

    pub async fn start_reconnect_listen(&self) {
        //每3秒检查连接是否建立
        //如果连接断开，则尝试重连
        //如果连接成功，则设置连接状态为已连接
        //如果连接失败，则设置连接状态为未连接
        let client_clone = self.client.clone();
        let url = self.uri.clone();
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    _ = tokio::time::sleep(std::time::Duration::from_secs(2)) => {
                        if client_clone.read().await.is_none() {
                            error!("账户风险中心重新连接中......");
                            log_error(&format!("账户风险中心重新连接中......:{url}")).await;
                            let client = AccountRiskCenterClient::connect(url.clone()).await;
                            if client.is_err() {
                                error!("不能连接到账户风险中心,3秒后重连,错误信息:{}", client.err().unwrap());
                            }else{
                                info!("账户风险中心连接成功......");
                                let mut wr = client_clone.write().await;
                                *wr = Some(client.unwrap());
                            }
                        }
                    }
                }
            }
        });

        // let riskclient_clone = Arc::clone(&self.client);
        // let url = self.uri.clone();
        // tokio::spawn(async move {
        //     loop {
        //         tokio::select! {
        //             _ = tokio::time::sleep(std::time::Duration::from_secs(3)) => {
        //                 if riskclient_clone.read().await.is_none() {
        //                     error!("账户风险中心重新连接中......");
        //                     log_error(&format!("账户风险中心重新连接中......:{url}")).await;
        //                     let client = AccountRiskCenterClient::connect(url.clone()).await;
        //                     if client.is_err() {
        //                         // error!("不能连接到账户风险中心,3秒后重连,错误信息:{}", client.err().unwrap()));
        //                         error!("不能连接到账户风险中心,3秒后重连,错误信息:{}", client.as_ref().unwrap_err().to_string());
        //                         log_error(&format!("连接分帐户风控中心失败......:{}", client.as_ref().unwrap_err().to_string())).await;
        //                     }else{
        //                         info!("账户风险中心连接成功......");
        //                         let mut wr = riskclient_clone.write().await;
        //                         *wr = Some(client.unwrap());
        //                     }
        //                 }
        //             }
        //         }
        //     }
        // });
    }

    pub async fn query_margin_ratio(&self, user_id: i64, stock_id: i64) -> Result<MarginRatioResp> {
        if !self.hearbeat.is_healthy() {
            error!("{} is unhealthy, please try again later", self.hearbeat.service_name);
            return Err(anyhow!("账户风险中心未连接"));
        }
        if self.client.read().await.is_none() {
            error!("账户风险中心未连接");
            return Err(anyhow!("账户风险中心未连接"));
        }
        let mut wr = self.client.write().await;
        let req = MarginRatioReq { user_id, stock_id };
        let ret = wr.as_mut().unwrap().query_margin_ratio(req.to_owned()).await;
        if ret.as_ref().is_err() {
            log_error(&format!("账户风险中心 query_margin_ratio error: {:?}", ret.as_ref().err().unwrap())).await;
            *wr = None;
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }

        let margin_ratio = ret.unwrap();
        Ok(margin_ratio.into_inner())
    }

    pub async fn query_user_assets(&self, req: &UserAssetsReq) -> Result<UserAssetsResp> {
        if self.client.read().await.is_none() {
            error!("账户风险中心未连接");
            return Err(anyhow!("账户风险中心未连接"));
        }
        let mut wr = self.client.write().await;
        let ret = wr.as_mut().unwrap().query_user_assets(req.to_owned()).await;
        if ret.as_ref().is_err() {
            log_error(&format!("账户风险中心 query_user_assets error: {:?}", ret.as_ref().err().unwrap())).await;
            *wr = None;
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let user_assets = ret.unwrap();
        Ok(user_assets.into_inner())
    }

    pub async fn query_user_positions(&self, req: &UserPositionReq) -> Result<UserPositionResp> {
        if self.client.read().await.is_none() {
            error!("账户风险中心未连接");
            return Err(anyhow!("账户风险中心未连接"));
        }
        let mut wr = self.client.write().await;
        let ret = wr.as_mut().unwrap().query_user_positions(req.to_owned()).await;
        if ret.as_ref().is_err() {
            log_error(&format!("账户风险中心 query_user_positions error: {:?}", ret.as_ref().err().unwrap())).await;
            *wr = None;
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let user_positions = ret.unwrap();
        Ok(user_positions.into_inner())
    }

    pub async fn query_stock_positions(&self, req: &PhoenixStockPositionRequest) -> Result<PhoenixStockPositionResponse> {
        if self.client.read().await.is_none() {
            error!("账户风险中心未连接");
            return Err(anyhow!("账户风险中心未连接"));
        }
        let mut wr = self.client.write().await;
        let ret = wr.as_mut().unwrap().query_stock_positions(req.to_owned()).await;
        if ret.as_ref().is_err() {
            log_error(&format!("账户风险中心 query_stock_positions error: {:?}", ret.as_ref().err().unwrap())).await;
            *wr = None;
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let stock_positions = ret.unwrap();
        Ok(stock_positions.into_inner())
    }

    pub async fn query_account_available_amount(&self, req: &AccountAvailableAmountRequest) -> Result<AccountAvailableAmountResponse> {
        let mut wr = self.client.write().await;

        let ret = wr.as_mut().unwrap().query_account_available_amount(req.to_owned()).await;
        if ret.as_ref().is_err() {
            log_error(&format!("账户风险中心 query_account_available_amount error: {:?}", ret.as_ref().err().unwrap())).await;
            *wr = None;
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }

        let account_available_amount = ret.unwrap();
        Ok(account_available_amount.into_inner())
    }

    pub async fn query_user_currency(&self, user_id: i64, unit_id: i64) -> Result<String> {
        if self.client.read().await.is_none() {
            error!("账户风险中心未连接");
            return Err(anyhow!("账户风险中心未连接"));
        }
        // info!("query user currency user_id:{}, unit_id:{}", user_id, unit_id);
        let mut request = UserAssetsReq::default();
        request.user_id.push(user_id);
        request.unit_id.push(unit_id);

        let mut wr = self.client.write().await;

        match wr.as_mut().unwrap().query_user_assets(request.to_owned()).await {
            Ok(val) => {
                let user_assets = val.into_inner().assets;
                if user_assets.is_empty() {
                    error!("account risk user assets is empty");
                    return Err(anyhow!("account risk user assets is empty"));
                }
                info!("account risk user assets: {:?}", user_assets);
                let currency = if let Some(assets) = user_assets.iter().find(|x| x.unit_id == unit_id) {
                    assets.currency.to_owned()
                } else {
                    "".to_string()
                };
                if currency.is_empty() {
                    error!("没有找到用户资产: {}, {}", user_id, unit_id);
                    return Err(anyhow!("account risk user assets is empty"));
                }
                return Ok(currency);
            }
            Err(status) => {
                error!("{:?}", status);
                log_error(format!("account risk center err code: {:?} message: {:?}", status.code(), status.message()).as_str()).await;
                return Err(anyhow!("account risk center error"));
            }
        }
    }
}
// }
