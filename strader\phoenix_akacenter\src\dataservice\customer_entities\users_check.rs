//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_check")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub t_id: i64,
    pub t_type: i8,
    pub check_time: i64,
    pub user_id: i64,
    pub user_name: String,
    pub check_mark: String,
    pub status: i8,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
