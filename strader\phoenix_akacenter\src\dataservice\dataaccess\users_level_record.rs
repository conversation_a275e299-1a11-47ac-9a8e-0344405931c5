use crate::dataservice::{
    dbsetup::DbConnection,
    entities::{
        prelude::{UsersLevelRecord, UsersLevelRecordEntity},
        users_level_record,
    },
};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, DbErr, EntityTrait, QueryFilter};

impl UsersLevelRecord {
    pub async fn find_by_user_id(db: &DbConnection, user_id: i64, current_date: i64) -> Result<Option<UsersLevelRecord>> {
        let ret_data: Result<Option<UsersLevelRecord>, DbErr> = UsersLevelRecordEntity::find()
            .filter(users_level_record::Column::UserId.eq(user_id))
            .filter(users_level_record::Column::StopDate.eq(0))
            .filter(users_level_record::Column::ExpireDate.gte(current_date))
            .one(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        let data = ret_data.unwrap();
        Ok(data)
    }

    pub async fn find_by_expire_date(db: &DbConnection, current_date: i64) -> Result<Vec<UsersLevelRecord>> {
        let ret_data: Result<Vec<UsersLevelRecord>, DbErr> = UsersLevelRecordEntity::find()
            .filter(users_level_record::Column::StopDate.eq(0))
            .filter(users_level_record::Column::ExpireDate.gte(current_date))
            .all(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }
}
