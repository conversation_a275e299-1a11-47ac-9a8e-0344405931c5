use crate::client::OrderCenterClient;
use crate::client::OrderRouterClient;
use crate::config::settings::Settings;
// use futures::{
//     channel::mpsc::{channel, Receiver},
//     SinkExt, StreamExt, TryFutureExt,
// };
// use notify::{Config, Event, RecommendedWatcher, RecursiveMode, Watcher};
// use std::error::Error;
// use std::path::Path;
use tonic::{self, Request, Response, Status};
// use std::fmt::Debug;
use async_channel::unbounded;
use std::sync::Arc;
use std::{pin::Pin, time::Duration};

use crate::controller::ServerController;
use common::logclient::LogClient;
use protoes::phoenixmessagerouter::message_router_service_server::MessageRouterService;
use protoes::phoenixordercenter::{ReplenishOrderReq, ReplenishOrderResp};
use protoes::phoenixordermsg::{MsgContent, ReqResp};
use protoes::{
    // phoenixordercenter,
    phoenixordercenter::{OrderReq, OrderResp},
    phoenixordermsg::{MsgType, RouterMsg},
};
use tokio::sync::{mpsc, oneshot, RwLock};
use tracing::*;

type StubType = Arc<ServerController>;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

pub struct ServerHandler {
    stub: StubType,
    pub settings: Settings,
    task_dispacther: mpsc::Sender<ControllerAction>,
    // order_dispacther: mpsc::Sender<PhoenixRiskCheckInfo>,
    set_close: Option<oneshot::Sender<()>>,
    // mqclient: QuotationClient,
}
pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);

impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

impl ServerHandler {
    pub async fn new(settings: &Settings) -> Self {
        let mut persist_interval = tokio::time::interval(std::time::Duration::from_secs(5 as u64));

        // let common = CommonUtil::new();
        // common.init(&settings).await.expect("init time error");

        let (tx_order_router_to_router, mut _rx_order_router_to_router) = unbounded::<RouterMsg>();
        // //order_router
        let mut order_router_client = OrderRouterClient::new(&settings.grpcserver.orderrouterserver.to_string(), _rx_order_router_to_router).await;
        //
        let mut retry_interval = tokio::time::interval(Duration::from_secs(3));
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    _ = retry_interval.tick() => {
                        if let Err(err) = order_router_client.order_transfer().await {
                            info!("{:?}", err);
                            let _ = order_router_client.retry_order_transfer().await;
                        }
                    }
                }
            }
            // order_router_client.order_transfer().await;
        });
        //let uidsvc = Arc::new(RwLock::new(UidgenService::new(settings.application.machineid, settings.application.nodeid)));
        let center_client = OrderCenterClient::new(&settings.grpcserver.ordercenterserver).await;
        //let router_client = OrderRouterClient::new(&settings.grpcserver.orderrouterserver).await;
        let stub = ServerController {
            settings: Arc::new(RwLock::new(settings.clone())), // rbcon: rb.to_owned(),
            //  uidsvc,
            order_center_client: center_client,
            // order_router_client :router_client,
            router_sender: tx_order_router_to_router.clone(),
        };

        let stub = Arc::new(stub);

        let (tx, mut rx) = mpsc::channel(16);
        let (tx_close, mut rx_close) = oneshot::channel();

        // let stub_clone = stub.clone();
        let stub_for_dispatch = stub.clone();

        let svr_handler = ServerHandler {
            task_dispacther: tx,
            set_close: Some(tx_close),
            stub,
            settings: settings.clone(),
        };
        //  let (tx_order_router, mut _rx_order_router) = unbounded::<RouterMsg>();
        tokio::spawn(async move {
            persist_interval.tick().await; //skip first tick
            loop {
                tokio::select! {
                    may_task = rx.recv() => {
                        if let Some(task) = may_task{
                            task(stub_for_dispatch.clone()).await;
                        }
                    }
                    // _ = persist_interval.tick() => {
                    //     info!("Start a time interval task (persist, computing):{:?}",std::thread::current());
                    //     let _ = stub_clone.print_configurations().await;

                    // }
                    // config = rx_config.recv() =>{
                    //   info!("configuration file changed......：{:?}", config);
                    //   let ret = Settings::new();
                    //   if ret.as_ref().is_ok(){
                    //     let _ = stub_clone.update_configurations(&ret.unwrap()).await;
                    //   }
                    // }
                    // notification = rx_notification.recv() => {
                    //     //收到消息通知
                    //     if let Some(message) = &notification{
                    //         //receive message
                    //         info!("receive message from message center: {:?}", &message);
                    //     }
                    // }
                  //   order_msg = _rx_order_router.recv()=>{
                  //     info!("收到订单消息......:{:?}",order_msg);
                  //
                  // }

                    _ = &mut rx_close => {
                        info!("Server scheduler is notified to close");
                        rx.close();
                        break;
                    }
                }
            }

            warn!("Server scheduler has exited");
        });

        svr_handler
    }
    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

//这里实现grpc的接口
#[tonic::async_trait]
impl MessageRouterService for ServerHandler {
    async fn order_message_transfer(&self, request: Request<OrderReq>) -> Result<Response<OrderResp>, Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        let req = request.into_inner();
        match self.stub.order_message_transfer(&req).await {
            Ok(val) => Ok(Response::new(val)),
            Err(err) => {
                error!("place order err: {:?}", err);
                LogClient::get().expect("get log client error").push_error(&format!("order_message_transfer 接口出错:{:?}", err.to_string())).await;
                //let msg = err.to_string();
                Ok(Response::new(OrderResp {
                    msg_id: req.msg_id,
                    order_id: req.unit_id,
                    error_code: -1,
                    error_msg: err.to_string(),
                }))
            }
        }
    }

    async fn exec_order_message_transfer(&self, request: Request<RouterMsg>) -> Result<Response<RouterMsg>, Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        let req = request.into_inner();
        match self.stub.exec_order_message_transfer(&req).await {
            Ok(val) => Ok(Response::new(val)),
            Err(err) => {
                error!("exec_order_message_transfer err: {:?}", err);
                LogClient::get()
                    .expect("get log client error")
                    .push_error(&format!("exec_order_message_transfer 接口出错:{:?}", err.to_string()))
                    .await;
                Ok(Response::new(RouterMsg {
                    msg_type: MsgType::Response as i32,
                    msg_content: Some(MsgContent {
                        register_req: None,
                        order_msg: None,
                        exec_msg: None,
                        resp: Some(ReqResp {
                            msg_id: req.msg_id,
                            error_code: -1,
                            error_msg: err.to_string(),
                        }),
                    }),
                    msg_id: 0,
                    msg_time: 0,
                }))
            }
        }
    }

    async fn replenishment_order_transfer(&self, request: Request<ReplenishOrderReq>) -> Result<Response<ReplenishOrderResp>, Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        let req = request.into_inner();
        match self.stub.replenishment_order_transfer(&req).await {
            Ok(val) => Ok(Response::new(val)),
            Err(err) => {
                error!("place order err: {:?}", err);
                LogClient::get().expect("get log client error").push_error(&format!("order_message_transfer 接口出错:{:?}", err.to_string())).await;
                //let msg = err.to_string();
                Ok(Response::new(ReplenishOrderResp { err_msg: err.to_string(), err_code: -1 }))
            }
        }
    }
}

// fn async_watcher() -> notify::Result<(RecommendedWatcher, Receiver<notify::Result<Event>>)> {
//     let (mut tx, rx) = channel(1);

//     // Automatically select the best implementation for your platform.
//     // You can also access each implementation directly e.g. INotifyWatcher.
//     let watcher = RecommendedWatcher::new(
//         move |res| {
//             futures::executor::block_on(async {
//                 tx.send(res).await.unwrap();
//             })
//         },
//         Config::default(),
//     )?;

//     Ok((watcher, rx))
// }

// async fn async_watch<P: AsRef<Path>>(path: P, tx: tokio::sync::mpsc::Sender<Event>) -> notify::Result<()> {
//     let (mut watcher, mut rx) = async_watcher()?;

//     // Add a path to be watched. All files and directories at that path and
//     // below will be monitored for changes.
//     watcher.watch(path.as_ref(), RecursiveMode::Recursive)?;

//     while let Some(res) = rx.next().await {
//         match res {
//             Ok(event) => {
//                 // info!("changed: {:?}", event);
//                 let _ = tx.send(event).await;
//             }
//             Err(e) => error!("watch error: {:?}", e),
//         }
//     }

//     Ok(())
// }
