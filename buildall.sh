#!/bin/bash

TARGET="x86_64-unknown-linux-gnu"
if [ "$1" == "musl" ]; then
    TARGET="x86_64-unknown-linux-musl"
fi

cargo build -p phoenix_accountriskcenter --release --target=$TARGET
cargo build -p phoenix_akacenter --release --target=$TARGET
cargo build -p phoenix_algorithmcenter --release --target=$TARGET
cargo build -p phoenix_assetscenter --release --target=$TARGET
cargo build -p phoenix_blackscholes --release --target=$TARGET
cargo build -p phoenix_exchanger --release --target=$TARGET
cargo build -p phoenix_matchserver --release --target=$TARGET
cargo build -p phoenix_ordercenter --release --target=$TARGET
cargo build -p phoenix_orderrouter --release --target=$TARGET
cargo build -p phoenix_riskcenter --release --target=$TARGET
cargo build -p phoenix_satcenter --release --target=$TARGET
cargo build -p phoenix_settlecenter --release --target=$TARGET
# cargo build -p phoenix_rustomsgateway --release
# cargo build -p phoenix_logcenter --release
# cargo build -p phoenix_hqcenter --release
# cargo build -p phoenix_sockethqcenter --release
# cargo build -p phoenix_stockaid --release
# cargo build -p phoenix_tickcenter --release
# cargo build -p phoenix_fiuhqsource --release
# cargo build -p phoenix_hqcalccenter --release
