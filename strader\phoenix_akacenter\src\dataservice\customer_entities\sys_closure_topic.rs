//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "sys_closure_topic")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub topic_id: i32,
    pub topic_type: Option<String>,
    pub topic_content: Option<String>,
    pub topic_content_en: Option<String>,
    pub answer_items: Option<String>,
    pub answer_items_en: Option<String>,
    pub topic_flag: Option<i8>,
    pub topic_sort: Option<i32>,
    pub topic_cate: Option<i32>,
    pub unit_name: Option<String>,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
