//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_exc_orderinfo")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    #[sea_orm(unique)]
    pub order_id: i64,
    pub stock_id: String,
    pub stock_code: String,
    pub exchange_code: String,
    pub order_direction: i32,
    pub order_amount: i32,
    pub order_amount_dealed: i32,
    pub order_amount_cancel: i32,
    pub price_type: i32,
    pub order_price: String,
    pub currency_no: String,
    pub channel_type: i32,
    pub channel_id: i32,
    pub confirm_no: String,
    pub relate_id: i32,
    pub receive_date: i32,
    pub ex_status: i32,
    pub created_at: i64,
    pub updated_at: i64,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
