// 证券指定数据
syntax = "proto3";

package phoenixstockaid;

service PhoenixStockAid {
  rpc PostMainIndexMsg(ReqIndexOrRankMsg) returns (ResultMainIndexMsg) {
  } // 获取首页指数
  rpc PostMainRankMsg(ReqIndexOrRankMsg) returns (ResultMainRankMsg) {
  } // 获取首页热门排行榜 :如涨幅榜 跌幅榜
  rpc PostPlateInfoMsg(ReqDetailMsg) returns (ResultPlateInfoMsg) {
  } // 获取板块数据 板块展示,板块内个股展示  获取首页热门板块
    // 根据获取数量进行区分
  rpc PostTrsInfoMsg(ReqTrsMsg) returns (ResultPlateInfoMsg) {} // 获取Trs信息
}

// 请求数据包
message ReqIndexOrRankMsg {
  int32 marketType = 1; // 市场类型,港股/美股/沪深
  //	int32 rankCode = 2; //获取各个市场排行榜下的具体各股数据
  // 根据排行榜code去获取 涨幅榜:19999 跌幅榜: 29999
  int32 count = 3; // 板块下所需个数
}

message ReqDetailMsg {
  int32 marketType = 1; // 市场类型
  int32 infoType = 2;   // 行情类型  1指数2板块3股票    当
                      // infoType的值为2的时候,相当于打开板块排行榜
                      // 当infoType的值为3,plateCode不能为0, 例:
                      // infoType:3,plateCode:19999 就去查询 板块code19999的详情
  int32 plateCode = 3; // 板块代码
  int32 count = 4;     // 所需个数
  //	bool	order = 5;			//true:正序;false:倒序
  //	int32   orderType = 6;      //0 根据最新价排序  1 根据涨跌幅排序
  //	int32   tradeCode = 7;      //0 只获取可交易代码 1 获取所有代码
}
message ReqTrsMsg {
  int32 count = 1;     // 所需个数
  bool order = 2;      // true:正序;false:倒序
  int32 orderType = 3; // 0 根据最新价排序  1 根据涨跌幅排序
  //	int32   tradeCode = 4;      //0 只获取可交易代码 1 获取所有代码
}
message DetailCodeInfo {
  string code = 1;        // 证券代码
  double lastPrice = 2;   // 最新价
  double changeValue = 3; // 涨跌值
  double changeRate = 4;  // 涨跌幅
  string topStock =
      5; // 板块下的领涨股的code  获取板块下所有个股的时候这个字段为空
}

message DetailPlateInfo {
  int32 id = 1;                           // 板块ID
  string code = 2;                        // 板块代码
  string name = 3;                        // 名称
  int32 count = 4;                        // 显示板块前几数量
  repeated DetailCodeInfo stockInfos = 5; // 前几板块列表
}

message ResultPlateInfoMsg {
  repeated DetailCodeInfo plateInfos = 1; // 板块详情
}

// 指数
message ResultMainIndexMsg {
  repeated DetailCodeInfo indexInfos = 1; // 指数行情
}
// 排行榜
message ResultMainRankMsg {
  //	  int32  id = 1;          //板块ID
  //		string code = 2;		//板块代码
  //	  string name = 3;        //名称
  //		int32  count = 4;       //显示板块前几数量
  //		repeated CodeInfo stockInfos = 5;
  ////前几板块列表
  repeated DetailCodeInfo mainPlate = 1;
  repeated DetailPlateInfo hotPlate = 2;
}