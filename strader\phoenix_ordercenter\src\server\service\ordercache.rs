use dbconnection::DbConnection;
use crate::dataservice::entities::prelude::{PhoenixOrdPendSettle, PhoenixOrdStockdeal, PhoenixOrdStockorder, PhoenixOrdSuborder};
use anyhow::Result;
use common::redisclient::redispool::RedisClient;
use tracing::{error, info};
pub const ORDER: &'static str = "order_";
pub const LOCK_ORDER: &'static str = "lock_new_order_";
pub const SUB_ORDER: &'static str = "sub_order_";
pub const PENDSETTLE: &'static str = "pendsettle_";
pub const ORDER_DEAL: &'static str = "order_deal_";
#[allow(dead_code)]
pub const ORDER_CANCEL: &'static str = "order_cancel_";
// pub const LOCK_SUB_ORDER:&'static str = "lock_sub_order_";

//缓存超时时间常量
#[allow(dead_code)]
pub const EXPARE_TIME_1_HOUR: i64 = 1 * 60 * 60;
pub const EXPARE_TIME_8_HOUR: i64 = 8 * 60 * 60;
#[allow(dead_code)]
pub const EXPARE_TIME_1_DAY: i64 = 24 * 60 * 60;

#[derive(Debug, Clone, Default)]
#[allow(dead_code)]
pub struct CacheKey {
    pub unit_id: i64,
    pub order_id: i64,
    pub settle_id: i64,
    pub exec_no: String,
    pub sub_id: i64,
    pub channel_id: i32,
    pub channel_type: i32,
}

impl CacheKey {
    #[allow(dead_code)]
    pub async fn new() -> Self {
        CacheKey::default()
    }

    pub async fn init_order_cache(&self, redis: &RedisClient, db: &DbConnection) -> Result<PhoenixOrdStockorder> {
        match PhoenixOrdStockorder::query_order(self.order_id, db).await {
            Ok(model) => {
                let _ = self.update_order_cache(&model, redis).await;
                Ok(model)
            }
            Err(err) => {
                error!("{:?}", &err);
                return Err(err);
            }
        }
    }

    pub async fn init_sub_order_cache(&self, redis: &RedisClient, db: &DbConnection) -> Result<Vec<PhoenixOrdSuborder>> {
        match PhoenixOrdSuborder::query_all_sub_order(self.order_id, db).await {
            Ok(model) => {
                let _ = self.update_sub_order_cache(&model, redis).await;
                Ok(model)
            }
            Err(err) => {
                error!("{:?}", &err);
                return Err(err);
            }
        }
    }

    pub async fn update_order_cache(&self, model: &PhoenixOrdStockorder, redis: &RedisClient) -> Result<()> {
        // info!("update_order_cache...");
        let key = format!("{}{}", ORDER, self.order_id);
        let data = serde_json::json!(model).to_string();
        // let data = serde_json::to_string(&model).unwrap();
        if let Err(err) = redis.set_str_value(&key, EXPARE_TIME_8_HOUR, &data).await {
            error!("更新订单缓存出错: {:?}", err);
            return Err(err);
        }
        // info!("更新订单缓存成功: {:?}", model);
        Ok(())
    }

    pub async fn update_sub_order_cache(&self, model: &Vec<PhoenixOrdSuborder>, redis: &RedisClient) -> Result<()> {
        // info!("update_sub_order_cache...");
        let key = format!("{}{}", SUB_ORDER, self.order_id);
        let data = serde_json::json!(model).to_string();
        // let data = serde_json::to_string(&model).unwrap();
        if let Err(err) = redis.set_str_value(&key, EXPARE_TIME_8_HOUR, &data).await {
            error!("更新子订单缓存出错: {:?}", err);
            return Err(err);
        }
        // info!("更新子订单缓存成功: {:?}", &model);
        Ok(())
    }

    pub async fn query_order(&self, redis: &RedisClient, db: &DbConnection) -> Result<PhoenixOrdStockorder> {
        let key = format!("{}{}", ORDER, self.order_id);
        let data = redis.get_value_by_get(&key).await;
        if !data.is_empty() {
            match serde_json::from_str(&data) {
                Ok(model) => {
                    info!("取缓存 {} 订单: {:?}", self.order_id, &model);
                    return Ok(model);
                }
                Err(err) => {
                    error!("订单{}解析失败{:?}", &self.order_id, &err);
                }
            }
        }
        self.init_order_cache(redis, db).await
    }

    pub async fn query_all_sub_order(&self, redis: &RedisClient, db: &DbConnection) -> Result<Vec<PhoenixOrdSuborder>> {
        let key = format!("{}{}", SUB_ORDER, self.order_id);
        let data = redis.get_value_by_get(&key).await;
        if !data.is_empty() {
            match serde_json::from_str(&data) {
                Ok(models) => {
                    info!("取缓存 {} 子订单: {:?}", self.order_id, &models);
                    return Ok(models);
                }
                Err(err) => {
                    error!("子订单{}解析失败{:?}", &self.order_id, &err);
                }
            }
        }
        self.init_sub_order_cache(redis, db).await
    }

    pub async fn query_order_info(&self, redis: &RedisClient, db: &DbConnection) -> Result<(Vec<PhoenixOrdSuborder>, PhoenixOrdStockorder)> {
        let ret = self.query_all_sub_order(redis, db).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let sub_orders = ret.unwrap();

        let ret = self.query_order(redis, db).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let order = ret.unwrap();
        Ok((sub_orders, order))
    }

    // #[allow(dead_code)]
    // pub async fn init_pendsettle_cache(&self, redis: &RedisClient, db: &DbConnection) -> Result<PhoenixOrdPendSettle> {
    //     match PhoenixOrdPendSettle::query_by_settle_id(self.settle_id, db).await {
    //         Ok(model) => {
    //             let _ = self.update_pendsettle_cache(&model, redis).await;
    //             Ok(model)
    //         }
    //         Err(err) => {
    //             error!("{:?}", &err);
    //             return Err(err);
    //         }
    //     }
    // }

    pub async fn update_pendsettle_cache(&self, model: &PhoenixOrdPendSettle, redis: &RedisClient) -> Result<()> {
        let key = format!("{}{}_{}_{}", PENDSETTLE, self.order_id, self.channel_id, self.unit_id);
        let data = serde_json::json!(model).to_string();
        if let Err(err) = redis.set_str_value(&key, EXPARE_TIME_8_HOUR, &data).await {
            error!("更新交收缓存出错: {:?}", err);
            return Err(err);
        }
        // info!("更新交收缓存成功: {:?}", &model);
        Ok(())
    }

    pub async fn query_pendsettle_info(&self, redis: &RedisClient, _db: &DbConnection) -> Option<PhoenixOrdPendSettle> {
        let key = format!("{}{}_{}_{}", PENDSETTLE, self.order_id, self.channel_id, self.unit_id);
        let data = redis.get_value_by_get(&key).await;
        if !data.is_empty() {
            match serde_json::from_str(&data) {
                Ok(models) => {
                    // info!("取缓存 {} 交收: {:?}", self.order_id, &models);
                    return Some(models);
                }
                Err(err) => {
                    error!("交收{}解析失败{:?}", &self.order_id, &err);
                    return None;
                }
            }
        }
        // self.init_pendsettle_cache(redis, db).await
        return None;
    }

    pub async fn insert_deal_cache(&self, model: &PhoenixOrdStockdeal, redis: &RedisClient) -> Result<()> {
        let key = format!("{}{}", ORDER_DEAL, self.exec_no);
        let data = serde_json::json!(model).to_string();
        if let Err(err) = redis.set_str_value(&key, EXPARE_TIME_8_HOUR, &data).await {
            error!("插入成交缓存出错: {:?}", err);
            return Err(err);
        }
        // info!("插入成交缓存成功: {:?}", &model);
        Ok(())
    }

    pub async fn cheak_order_deal(&self, redis: &RedisClient) -> Result<()> {
        // info!("查询订单{}是否成交: {}", self.order_id, self.exec_no);
        let key = format!("{}{}", ORDER_DEAL, self.exec_no);
        let data = redis.get_value_by_get(&key).await;
        if !data.is_empty() {
            info!("{}", data);
            return Err(anyhow!("成交已处理 stockdeal: {:?}", data));
        }
        return Ok(());
    }

    // #[allow(dead_code)]
    // pub async fn update_cancel_cache(&self, model: &PhoenixOrdCancel, redis: &RedisClient) -> Result<()> {
    //     let key = format!("{}{}", ORDER_CANCEL, self.order_id);
    //     let data = serde_json::json!(model).to_string();
    //     if let Err(err) = redis.set_str_value(&key, EXPARE_TIME_8_HOUR, &data).await {
    //         error!("插入撤单委托缓存出错: {:?}", err);
    //         return Err(err);
    //     }
    //     info!("插入撤单委托缓存成功: {:?}", &model);
    //     Ok(())
    // }

    // #[allow(dead_code)]
    // pub async fn init_cancel_cache(&self, redis: &RedisClient, db: &DbConnection) -> Result<PhoenixOrdCancel> {
    //     match PhoenixOrdCancel::query_cancel_order(self.order_id, db).await {
    //         Ok(model) => {
    //             let _ = self.update_cancel_cache(&model, redis).await;
    //             Ok(model)
    //         }
    //         Err(err) => {
    //             error!("{:?}", &err);
    //             return Err(err);
    //         }
    //     }
    // }

    // #[allow(dead_code)]
    // pub async fn query_cancel_order(&self, redis: &RedisClient, db: &DbConnection) -> Result<PhoenixOrdCancel> {
    //     let key = format!("{}{}", ORDER_CANCEL, self.order_id);
    //     let data = redis.get_value_by_get(&key).await;
    //     if !data.is_empty() {
    //         match serde_json::from_str(&data) {
    //             Ok(model) => {
    //                 info!("取缓存 {} 撤单委托: {:?}", self.order_id, &model);
    //                 return Ok(model);
    //             }
    //             Err(err) => {
    //                 error!("撤单委托{}解析失败{:?}", &self.order_id, &err);
    //             }
    //         }
    //     }
    //     self.init_cancel_cache(redis, db).await
    // }
}

