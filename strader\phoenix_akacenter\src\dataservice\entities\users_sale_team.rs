//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_sale_team")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub team_name: String,
    pub user_id: i64,
    pub create_date: i64,
    pub sub_user_id: i64,
    pub create_name: String,
}

#[derive(<PERSON><PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
