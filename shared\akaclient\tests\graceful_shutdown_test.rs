use akaclient::akaclient::{AkaCacheOption, AkaClient};
use std::time::Duration;
use tokio::time::timeout;

#[tokio::test]
async fn test_aka_client_graceful_shutdown() {
    // Test configuration without cache (to avoid MQ dependencies in tests)
    let cache_option = AkaCacheOption {
        use_cache: false,
        mq_uri: String::new(),
        exchange: String::new(),
        routing_keys: String::new(),
    };

    // Create AkaClient instance
    let client = AkaClient::init(
        "http://127.0.0.1:50051", // Dummy URL for testing
        &cache_option,
        "test_client",
        "akaclient_test",
    )
    .await;

    // Test that shutdown completes within reasonable time
    let shutdown_result = timeout(Duration::from_secs(5), client.shutdown()).await;

    assert!(shutdown_result.is_ok(), "Shutdown should complete within 5 seconds");

    println!("AkaClient graceful shutdown test completed successfully");
}

#[tokio::test]
async fn test_aka_client_shutdown_signal_broadcast() {
    // Test configuration without cache
    let cache_option = AkaCacheOption {
        use_cache: false,
        mq_uri: String::new(),
        exchange: String::new(),
        routing_keys: String::new(),
    };

    // Create AkaClient instance
    let client = AkaClient::init("http://127.0.0.1:50051", &cache_option, "test_client", "akaclient_test").await;

    // Get shutdown sender for external coordination
    let shutdown_sender = client.get_shutdown_sender();
    let mut shutdown_receiver = shutdown_sender.subscribe();

    // Send shutdown signal externally
    let send_result = shutdown_sender.send(());
    assert!(send_result.is_ok(), "Should be able to send shutdown signal");

    // Verify signal is received
    let receive_result = timeout(Duration::from_millis(100), shutdown_receiver.recv()).await;
    assert!(receive_result.is_ok(), "Should receive shutdown signal");

    println!("AkaClient shutdown signal broadcast test completed successfully");
}

#[tokio::test]
async fn test_aka_client_with_cache_shutdown() {
    // Test configuration with cache enabled (will fail to connect but should still shutdown gracefully)
    let cache_option = AkaCacheOption {
        use_cache: true,
        mq_uri: "amqp://guest:guest@localhost:5672/%2f".to_string(),
        exchange: "test_exchange".to_string(),
        routing_keys: "test.routing.key".to_string(),
    };

    // Create AkaClient instance (will likely fail to connect to MQ, but that's ok for shutdown test)
    let client = AkaClient::init("http://127.0.0.1:50051", &cache_option, "test_client", "akaclient_test").await;

    // Wait a moment for background tasks to start
    tokio::time::sleep(Duration::from_millis(100)).await;

    // Test that shutdown completes even with cache tasks
    let shutdown_result = timeout(Duration::from_secs(5), client.shutdown()).await;

    assert!(shutdown_result.is_ok(), "Shutdown should complete within 5 seconds even with cache tasks");

    println!("AkaClient with cache graceful shutdown test completed successfully");
}
