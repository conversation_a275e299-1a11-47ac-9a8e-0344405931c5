//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_ord_stockorder_copy1")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    #[sea_orm(unique)]
    pub order_no: i64,
    pub sys_date: i32,
    pub unit_id: i64,
    pub stock_id: i64,
    pub stock_code: String,
    pub exchange_id: i32,
    pub order_direction: i32,
    #[sea_orm(column_type = "Decimal(Some((16, 8)))")]
    pub order_price: Decimal,
    pub order_amount: i32,
    pub order_type: i32,
    #[sea_orm(column_type = "Decimal(Some((12, 2)))")]
    pub pre_fee: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub pre_capital: Decimal,
    pub price_type: i32,
    pub deal_amount: i32,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub deal_value: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub deal_fee: Decimal,
    pub cancel_amount: i32,
    pub order_status: i32,
    pub last_deal_time: i64,
    pub relate_order: i64,
    pub trade_type: i32,
    pub order_memo: String,
    pub create_time: i64,
    pub modify_time: i64,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
