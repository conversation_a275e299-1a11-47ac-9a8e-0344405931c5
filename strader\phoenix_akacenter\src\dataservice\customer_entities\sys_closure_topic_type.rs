//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "sys_closure_topic_type")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub topic_type: Option<i32>,
    pub topic_type_name: Option<String>,
    pub topic_type_name_en: Option<String>,
    pub has_responsedays: Option<i8>,
    pub status: Option<i8>,
}

#[derive(<PERSON><PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
