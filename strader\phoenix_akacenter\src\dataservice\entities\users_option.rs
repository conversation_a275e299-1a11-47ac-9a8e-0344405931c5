//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_option")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub user_id: i64,
    pub account_no: String,
    pub contract_no: String,
    pub stock_id: i64,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub principal: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub open_price: Decimal,
    pub currency: String,
    pub trade_date: i32,
    pub time_limit: String,
    pub due_date: i32,
    pub option_style: i8,
    pub option_cate: i8,
    pub principal_currency: String,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub option_rate: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub option_fee: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub return_fee: Decimal,
    pub remark: String,
    pub status: i8,
    pub stop_type: i8,
    pub stop_date: i32,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub last_price: Decimal,
    pub last_price_currency: String,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub settle_price: Decimal,
    pub settle_price_currency: String,
    pub stop_mark: String,
    pub return_fee_currency: String,
    pub option_fee_currency: String,
    pub create_date: i64,
    pub r#struct: i8,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
