use crate::config::settings::Settings;
use crate::server::controller::OrderRouterController;
use crate::service::allchannel::ChannelsCached;
use akaclient::akaclient::AkaClient;
use async_channel::{unbounded, Receiver as async_receiver, Sender as async_sender};
use common::logclient::*;
use common::uidservice;
use dashmap::DashMap;
use futures::Stream;
use lazy_static::lazy_static;
use protoes::phoenixordermsg::{MsgType::*, RouterMsg};
// use protoes::MsgType::*;
use std::sync::Arc;
use std::{error::Error, io::ErrorKind, pin::Pin};
use tokio::sync::mpsc::Sender;
use tokio::sync::{mpsc, oneshot};
use tokio_stream::{wrappers::ReceiverStream, StreamExt};
use tonic::{Request, Response, Status, Streaming};
use tracing::*;
use utility::timeutil;
use utility::timeutil::current_timestamp;

type OrderStream = Pin<Box<dyn Stream<Item = Result<RouterMsg, Status>> + Send>>;
type StubType = Arc<OrderRouterController>;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

lazy_static! {
    pub static ref OUT_BP_CACHE: DashMap<i64, MapSender> = DashMap::new();
    pub static ref IN_BP_CACHE: DashMap<i64, MapSender> = DashMap::new();
}

// #[derive(Copy)]
#[allow(dead_code)]
pub struct RouterServerHandler {
    stub: StubType,
    pub settings: Settings,
    task_dispacther: mpsc::Sender<ControllerAction>,
    set_close: Option<oneshot::Sender<()>>,
    order_sender: async_sender<RouterMsg>,
    order_receiver: async_receiver<RouterMsg>,
}
#[derive(Clone, Debug)]
pub struct MapSender {
    pub sender_id: i64,
    pub sender_copy: Sender<Result<RouterMsg, Status>>,
}
pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);
impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

#[allow(dead_code)]
pub enum BpType {
    /// 空
    /// 通道类型 1 外盘, 2 内盘
    NUll = 0,
    /// 外盘
    Out = 1,
    /// 内盘
    In = 2,
}
impl RouterServerHandler {
    pub async fn new(settings: &Settings) -> Self {
        //创建通道
        let (tx, mut rx) = mpsc::channel(32);
        let (tx_close, mut rx_close) = oneshot::channel(); //创建一次性通道,用于发送单个值

        let (tx_s, rx_s) = unbounded::<RouterMsg>();

        let op = akaclient::akaclient::AkaCacheOption::default();
        let akacenterconn = AkaClient::init(settings.servers.akacenterserver.to_string(), &op).await;
        let mut channel_controller = ChannelsCached::new();
        let _ = channel_controller.init_channels(&akacenterconn, &IN_BP_CACHE, &OUT_BP_CACHE).await.expect("init channel error");

        let stub = OrderRouterController {};

        let stub = Arc::new(stub);
        let stub_for_dispatch = stub.clone();
        let ret = RouterServerHandler {
            stub,
            settings: settings.clone(),
            task_dispacther: tx,
            set_close: Some(tx_close),
            order_sender: tx_s,
            order_receiver: rx_s,
        };

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    may_task = rx.recv() => {
                        info!("task from client received...");
                        let task = may_task.expect("Server scheduler has unexpected exit");
                        task(stub_for_dispatch.clone()).await;
                    }
                    _ = &mut rx_close => {
                        info!("Server scheduler is notified to close");
                        rx.close();
                        break;
                    }
                }
            }

            while let Some(task) = rx.recv().await {
                task(stub_for_dispatch.clone()).await;
                info!("drain unhandled task received");
            }

            warn!("Server scheduler has exited");
        });

        ret
    }

    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

#[tonic::async_trait]
impl protoes::phoenixorderrouter::order_router_service_server::OrderRouterService for RouterServerHandler {
    type OrderRoutingStream = OrderStream;

    async fn order_routing(&self, request: Request<Streaming<RouterMsg>>) -> Result<Response<Self::OrderRoutingStream>, Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        let mut in_stream = request.into_inner();
        let (tx, rx) = mpsc::channel(128);
        let tx_c = tx.clone();

        let order_c = self.order_receiver.clone();
        let order_sender_copy = self.order_sender.clone();
        // let set = self.settings.clone();
        tokio::spawn(async move {
            while let Some(result) = in_stream.next().await {
                match result {
                    Ok(val) => {
                        info!("{:#?}", val);
                        let router_msg = val.clone();
                        if val.msg_type == (Order as i32) {
                            //下单
                            if let Some(msg) = val.msg_content {
                                if let Some(order_msg) = msg.order_msg {
                                    info!("解析出订单消息:{:#?}", order_msg);
                                    if order_msg.channel_type == BpType::Out as i32 {
                                        //  bp_cache = *OUT_BP_CACHE;
                                        if OUT_BP_CACHE.contains_key(&order_msg.channel_id) {
                                            let ret = OUT_BP_CACHE
                                                .get(&order_msg.channel_id)
                                                .expect("get channel_id value error")
                                                .sender_copy
                                                .send(Result::<RouterMsg, Status>::Ok(router_msg))
                                                .await;
                                            if ret.as_ref().is_err() {
                                                let ret_clone = &ret.err().unwrap().to_string();
                                                error!("send bp error:{:#?}", ret_clone);
                                                LogClient::get()
                                                    .expect("get log client error")
                                                    .push_error(&format!("send bp error:{:#?},channel_id:{:#?}", ret_clone, &order_msg.channel_id))
                                                    .await;
                                                OUT_BP_CACHE.remove(&order_msg.channel_id);
                                            } else {
                                                info!("订单消息转发成功:{:#?}", order_msg.channel_id);
                                            }
                                        } else {
                                            error!("can't find channel:{:?}", order_msg.channel_id);
                                        }
                                    } else if order_msg.channel_type == BpType::In as i32 {
                                        // bp_cache = *IN_BP_CACHE;
                                        // if IN_BP_CACHE.contains_key(&orderMsg.channel_id) {
                                        let ret = IN_BP_CACHE
                                            .get(&order_msg.channel_id)
                                            .expect("get channel_id value error")
                                            .sender_copy
                                            .send(Result::<RouterMsg, Status>::Ok(router_msg))
                                            .await;
                                        if ret.as_ref().is_err() {
                                            let ret_clone = &ret.err().unwrap().to_string();
                                            error!("send bp error:{:#?}", ret_clone);
                                            LogClient::get()
                                                .expect("get log client error")
                                                .push_error(&format!("send bp error:{:#?},channel_id:{:#?}", ret_clone, &order_msg.channel_id))
                                                .await;
                                            IN_BP_CACHE.get(&order_msg.channel_id).expect("get channel_id value error").sender_copy.closed().await;
                                            IN_BP_CACHE.remove(&order_msg.channel_id);
                                            let (tx, _) = mpsc::channel(128);
                                            IN_BP_CACHE.entry(order_msg.channel_id).or_insert(MapSender { sender_id: 0, sender_copy: tx.clone() });
                                        } else {
                                            info!("订单消息转发成功:{:#?}", order_msg.channel_id);
                                        }
                                    } else {
                                        //bp_cache = *OUT_BP_CACHE;
                                        //continue;
                                        if OUT_BP_CACHE.contains_key(&order_msg.channel_id) {
                                            let ret = OUT_BP_CACHE
                                                .get(&order_msg.channel_id)
                                                .expect("get channel_id value error")
                                                .sender_copy
                                                .send(Result::<RouterMsg, Status>::Ok(router_msg))
                                                .await;
                                            if ret.as_ref().is_err() {
                                                let msg = &ret.err().unwrap().to_string();
                                                error!("send bp error:{:#?}", msg);
                                                LogClient::get()
                                                    .expect("get log client error")
                                                    .push_error(&format!("send bp error:{:#?},channel_id:{:#?}", msg, &order_msg.channel_id))
                                                    .await;
                                                OUT_BP_CACHE.get(&order_msg.channel_id).expect("get channel_id value error").sender_copy.closed().await;
                                                OUT_BP_CACHE.remove(&order_msg.channel_id);
                                            } else {
                                                info!("订单消息转发成功, channel_id:{}", order_msg.channel_id);
                                            }
                                        } else {
                                            error!("can't find channel by id:{}", order_msg.channel_id);
                                        }
                                    }
                                    let send_err = tx
                                        .send(Result::<RouterMsg, Status>::Ok(RouterMsg {
                                            msg_type: Response as i32,
                                            msg_id: val.msg_id,
                                            msg_content: None,
                                            msg_time: current_timestamp(),
                                        }))
                                        .await;
                                    if send_err.as_ref().is_err() {
                                        error!("send bp error:{:?}", send_err.err().unwrap().to_string());
                                    }
                                }
                            }
                        } else {
                            info!("order type error :{:?}", val);
                        }
                    }
                    Err(err) => {
                        if let Some(io_err) = match_for_io_error(&err) {
                            if io_err.kind() == ErrorKind::BrokenPipe {
                                // here you can handle special case when client
                                // disconnected in unexpected way
                                error!("client disconnected: broken pipe");
                                break;
                            }
                        }

                        match tx.send(Err(err)).await {
                            Ok(_) => (),
                            Err(_err) => break, // response was droped
                        }
                    }
                }
            }
            info!("\tstream ended");
            return;
        });

        //接收报盘返回的数据
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    order_msg = order_c.recv()=>{
                        info!("received msg:{:?}",&order_msg);
                        if let Ok(msg) = order_msg{
                            let order_msg_copy = msg.clone();
                            let ret =  tx_c.send(Result::<RouterMsg, Status>::Ok(msg)).await;
                            if ret.as_ref().is_err(){
                                //send to log center
                                tx_c.closed().await;
                                error!("send to client error:{}",ret.as_ref().err().unwrap().to_string());
                                //消息发送失败后,重新去推送
                                let ret = order_sender_copy.send(order_msg_copy).await;
                                if ret.as_ref().is_err() {
                                    let msg =  &ret.err().unwrap().to_string();
                                    error!("send bp channel error:{:#?}", msg);
                                    LogClient::get().expect("get log client error").push_error(&format!("send bp channel error:{:#?}", msg)).await;
                                }
                                return ;
                            }
                        }
                    }
                }
            }
        });

        let out_stream = ReceiverStream::new(rx);
        Ok(Response::new(Box::pin(out_stream) as Self::OrderTransferStream))
    }

    type OrderTransferStream = OrderStream;
    async fn order_transfer(&self, request: tonic::Request<tonic::Streaming<RouterMsg>>) -> Result<tonic::Response<Self::OrderTransferStream>, tonic::Status> {
        info!("client connected from:{:?}", &request.remote_addr());

        let mut in_stream = request.into_inner();

        let (tx, rx) = mpsc::channel(128);
        let order_sender_copy = self.order_sender.clone();
        let mut snow = uidservice::UidgenService::new(1, 1);
        let uid = snow.get_uid();
        let sender_value = MapSender { sender_id: uid, sender_copy: tx.clone() };
        let sender_value_copy = sender_value.clone();

        tokio::spawn(async move {
            while let Some(result) = in_stream.next().await {
                match result {
                    Ok(val) => {
                        info!("router message: {:?}", val);
                        let mut router_msg = val.clone();
                        if val.msg_type == (Register as i32) {
                            //注册
                            if let Some(msg) = val.msg_content {
                                if let Some(reg) = msg.register_req {
                                    // if let Some (channel_ist) = {
                                    for i in &reg.channel_id {
                                        // info!("{:#?}", i);
                                        //如果内盘包含这个key,那就是内盘的
                                        // info!("注册到内盘");
                                        if IN_BP_CACHE.contains_key(&i) {
                                            match IN_BP_CACHE.get_mut(&i) {
                                                None => {
                                                    IN_BP_CACHE.entry(*i).or_insert(sender_value.clone());
                                                }
                                                Some(mut val) => {
                                                    val.clone_from(&sender_value);
                                                }
                                            }
                                            info!("内盘注册成功,channel id:{}", i);
                                        } else {
                                            //外盘通道
                                            info!("外盘注册成功,channle_id:{}, snow_id:{}", i, uid);
                                            OUT_BP_CACHE.insert(*i, sender_value.clone());
                                        }
                                        let send_err = tx
                                            .send(Result::<RouterMsg, Status>::Ok(RouterMsg {
                                                msg_type: Response as i32,
                                                msg_id: val.msg_id,
                                                msg_content: None,
                                                msg_time: current_timestamp(),
                                            }))
                                            .await;
                                        if send_err.as_ref().is_err() {
                                            let msg = &send_err.err().unwrap().to_string();
                                            error!("send msg error:{:#?}", msg);
                                            LogClient::get().expect("get log client error").push_error(&format!("send msg error:{:#?}", msg)).await;
                                        }
                                    }
                                    //}
                                }
                            }
                        } else if val.msg_type == (Exec as i32) {
                            //订单执行回报
                            info!("order exec :{:#?}", router_msg);
                            if router_msg.msg_id == 0 {
                                let mut snow = uidservice::UidgenService::new(1, 1);
                                let uid = snow.get_uid();
                                router_msg.msg_id = uid;
                                router_msg.msg_time = timeutil::current_timestamp();
                            }
                            let ret = order_sender_copy.send(router_msg).await;
                            if ret.as_ref().is_err() {
                                let msg = &ret.err().unwrap().to_string();
                                error!("send bp error:{:?}", msg);
                                LogClient::get().expect("get log client error").push_error(&format!("send bp error:{:#?}", msg)).await;
                            }
                        }
                    }
                    Err(err) => {
                        if let Some(io_err) = match_for_io_error(&err) {
                            if io_err.kind() == ErrorKind::BrokenPipe {
                                // here you can handle special case when client
                                // disconnected in unexpected way
                                error!("client disconnected: broken pipe, sender_id:{}", &sender_value_copy.sender_id);
                                //删除链接
                                sender_value.sender_copy.closed().await;
                                let mut flag_out = false;
                                let mut out_int: i64 = -1;
                                for value in OUT_BP_CACHE.iter() {
                                    // info!("=>key:{:#?}", value.key());
                                    // info!("=>snow_id:{:#?}", sender_value_copy.sender_id);
                                    // info!("=>snow_id:{:#?}", value.value().sender_id);
                                    if value.value().sender_id == sender_value_copy.sender_id {
                                        flag_out = true;
                                        out_int = *value.key();
                                        info!("查找到sender_id:{}, key:{},snow_id:{}", sender_value_copy.sender_id, out_int, uid);

                                        break;
                                    }
                                }
                                if flag_out {
                                    OUT_BP_CACHE.remove(&out_int);
                                    info!("删除成功OUT_BP_CACHE key:{},", out_int);
                                }
                                let mut flag_in = false;
                                let mut in_int: i64 = -1;
                                for value in IN_BP_CACHE.iter() {
                                    // info!("=>key:{:#?}", value.key());
                                    if value.value().sender_id == sender_value_copy.sender_id {
                                        //删除map中的值
                                        flag_in = true;
                                        in_int = *value.key();
                                        break;
                                    }
                                }
                                if flag_in {
                                    IN_BP_CACHE.remove(&in_int);
                                    info!("删除成功 IN_BP_CACHE key:{},", in_int);
                                    let (tx, _) = mpsc::channel(128);
                                    IN_BP_CACHE.entry(in_int).or_insert(MapSender { sender_id: 0, sender_copy: tx.clone() });
                                }
                                break;
                            }
                        }
                        match tx.send(Err(err)).await {
                            Ok(_) => (),
                            Err(_err) => break, // response was droped
                        }
                    }
                }
            }
            // info!("stream ended");
            return;
        });

        let out_stream = ReceiverStream::new(rx);
        Ok(Response::new(Box::pin(out_stream) as Self::OrderTransferStream))
    }
}
fn match_for_io_error(err_status: &Status) -> Option<&std::io::Error> {
    let mut err: &(dyn Error + 'static) = err_status;

    loop {
        if let Some(io_err) = err.downcast_ref::<std::io::Error>() {
            return Some(io_err);
        }

        // h2::Error do not expose std::io::Error with `source()`
        // https://github.com/hyperium/h2/pull/462
        if let Some(h2_err) = err.downcast_ref::<h2::Error>() {
            if let Some(io_err) = h2_err.get_io() {
                return Some(io_err);
            }
        }

        err = match err.source() {
            Some(err) => err,
            None => return None,
        };
    }
}
