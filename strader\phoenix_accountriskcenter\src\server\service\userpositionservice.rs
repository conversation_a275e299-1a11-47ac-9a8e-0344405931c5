use crate::dataview::userposition::UserPositionData;
use akaclient::akaclient::AkaClient;
use anyhow::Result;
use protoes::phoenixakacenter::Currency;
use protoes::phoenixnotification::ExchangeRateChange;
use protoes::{
    assetscenter::{PositionMarginRate, PositionPriceChangeItemReq},
    phoenixaccountriskcenter::PhoenixUserPositions,
};
use std::cmp::Ordering;
use std::{collections::HashMap, sync::Arc};
use tokio::sync::RwLock;
use tracing::*;

// use super::hqcenterservice::HqCenterServcie;

pub struct UserPositionService {
    user_positions_data: Arc<RwLock<Vec<RwLock<UserPositionData>>>>,
}

impl UserPositionService {
    pub fn new() -> Self {
        UserPositionService {
            user_positions_data: Arc::new(RwLock::new(Vec::new())),
        }
    }

    //初始化数据
    pub async fn init(&self, ret_data: &mut Vec<UserPositionData>, akasvc: &AkaClient) -> Result<()> {
        let mut ratemap = HashMap::new();
        self.user_positions_data.write().await.clear();

        for d in ret_data.iter_mut() {
            let flag = match d.current_amount.cmp(&0) {
                Ordering::Less => 2,
                Ordering::Equal | Ordering::Greater => 1,
            };
            if let Some(rate) = ratemap.get(&format!("{}_{}_{}", d.exchange_id, d.currency, flag)) {
                d.currency_rate = *rate;
            } else {
                let currency = match d.currency {
                    c if c == Currency::Hkd as i32 => "HKD",
                    c if c == Currency::Cny as i32 => "CNY",
                    c if c == Currency::Usd as i32 => "USD",
                    c if c == Currency::Cnh as i32 => "CNH",
                    _ => "",
                };
                d.currency_rate = match akasvc.query_exchange_rate(&akasvc.query_currency_by_exchangeid(d.exchange_id), currency).await {
                    Ok(rate) => match d.current_amount.cmp(&0) {
                        Ordering::Less => rate.buy_rate,
                        Ordering::Equal | Ordering::Greater => rate.sell_rate,
                    },
                    Err(err) => {
                        error!("query rate error:{:?}", err);
                        1.0
                    }
                };

                ratemap.insert(format!("{}_{}_{}", d.exchange_id, d.currency, flag), d.currency_rate);
            }
            self.set_unit_positions(&d).await;
        }
        // info!("初始化用户持仓{}条", ret_data.len());
        Ok(())
    }

    //更新用户持仓数据
    pub async fn update_user_positions(&self, posdata: &UserPositionData) {
        // let user_posinfo = UserPositionData::convert_positioninfo_to_userposition(posdata, akasvc).await;
        self.set_unit_positions(&posdata).await
    }

    //根据stockid查找所有的持仓信息
    pub async fn query_user_positions(&self, unitid: i64, user_id: i64, stockid: i64) -> Vec<UserPositionData> {
        let stock_rd = self.user_positions_data.read().await;
        let mut ret = Vec::new();
        for item in stock_rd.iter() {
            let i = item.read().await;
            if unitid > 0 && user_id == 0 && stockid == 0 && unitid == i.unit_id {
                ret.push(i.to_owned());
            }
            if user_id > 0 && unitid == 0 && stockid == 0 && user_id == i.user_id {
                ret.push(i.to_owned());
            }
            if stockid > 0 && unitid == 0 && user_id == 0 && stockid == i.stock_id {
                ret.push(i.to_owned());
            }

            if stockid > 0 && stockid == i.stock_id && unitid > 0 && unitid == i.unit_id && user_id == 0 {
                ret.push(i.to_owned());
            }
            if user_id > 0 && user_id == i.user_id && unitid > 0 && unitid == i.unit_id && stockid == 0 {
                ret.push(i.to_owned());
            }
            if stockid > 0 && stockid == i.stock_id && user_id > 0 && user_id == i.user_id && unitid == 0 {
                ret.push(i.to_owned());
            }

            if stockid > 0 && stockid == i.stock_id && user_id > 0 && user_id == i.user_id && unitid > 0 && unitid == i.unit_id {
                ret.push(i.to_owned());
            }

            if stockid == 0 && unitid == 0 && user_id == 0 {
                ret.push(i.to_owned());
            }
        }
        ret
    }

    pub async fn query_user_positioins_rpc(&self, unit_id: i64, user_id: i64, stock_id: i64) -> Vec<PhoenixUserPositions> {
        let mut ret = Vec::new();
        let list = self.query_user_positions(unit_id, user_id, stock_id).await;
        for i in list {
            ret.push(PhoenixUserPositions {
                unit_id: i.unit_id,
                stock_id: i.stock_id,
                stock_code: i.stock_code,
                exchange_id: i.channel_id,
                amount: i.current_amount as i64,
                frozen_amount: i.frozen_amount as i64 + i.temp_frozen_amount as i64,
                prebuy_amount: i.prebuy_amount as i64,
                qfii_amount: i.qfii_amount as i64,
                margin_ratio: i.margin_rate,
                total_value_hkd: i.total_value_hkd,
                last_price: i.last_price,
                stock_type: i.stock_type,
                securities_borrow_available: 0,
                presale_amount: i.presale_amount as i64,
                user_id: i.user_id,
            })
        }
        ret
    }

    //更新保证金比率
    pub async fn update_positions_margin_rate(&self, rate: f64, user_id: i64, stockid: i64) -> Vec<PositionMarginRate> {
        // info!("update_positions_margin_rate,rate:{},user_id:{},stockid:{}", rate, user_id, stockid);
        let mut ret_vec = Vec::new();
        let stock_rd = self.user_positions_data.read().await;

        for val in stock_rd.iter() {
            let mut flag = false;
            {
                let f_rd = val.read().await;
                let b1 = if user_id > 0 { f_rd.user_id == user_id } else { f_rd.user_id > 0 };
                let b2 = if stockid > 0 { f_rd.stock_id == stockid } else { f_rd.stock_id > 0 };
                if b1 && b2 {
                    if f_rd.current_amount != 0 {
                        ret_vec.push(PositionMarginRate {
                            user_id: f_rd.user_id,
                            stock_id: f_rd.stock_id,
                            margin_rate: rate,
                        });
                    }
                    flag = true;
                }
            }
            if flag {
                val.write().await.margin_rate = rate;
            }
        }

        ret_vec
    }

    //更新最新价
    pub async fn update_positions_last_price(&self, price: f64, stockid: i64) {
        if price <= 0.0 {
            return;
        }
        let stock_rd = self.user_positions_data.read().await;

        for val in stock_rd.iter() {
            let mut flag = false;
            {
                let f_rd = val.read().await;
                if f_rd.stock_id == stockid {
                    flag = true;
                }
            }
            if flag {
                val.write().await.last_price = price;
            }
        }
    }

    pub async fn query_position_price(&self) -> Vec<PositionPriceChangeItemReq> {
        let mut ret = Vec::new();

        let mut pricemap = HashMap::new();
        let stock_rd = self.user_positions_data.read().await;

        for val in stock_rd.iter() {
            let f_rd = val.read().await;
            if !pricemap.contains_key(&f_rd.stock_id) && f_rd.current_amount > 0 {
                pricemap.insert(f_rd.stock_id, f_rd.last_price);
            }
        }

        for i in pricemap {
            if i.1 > 0.0 {
                ret.push(PositionPriceChangeItemReq { stock_id: i.0, last_price: i.1 })
            }
        }
        ret
    }

    //更新汇率
    pub async fn update_positions_sell_rate(&self, req: &ExchangeRateChange) {
        let markets = match Currency::try_from(req.currency).unwrap_or_default() {
            Currency::Hkd => vec![103],
            Currency::Cny => vec![101, 102],
            _ => vec![],
        };

        let stock_rd = self.user_positions_data.read().await;
        for val in stock_rd.iter() {
            let mut val_wr = val.write().await;

            if !markets.contains(&val_wr.exchange_id) || val_wr.currency != req.base_currency {
                continue;
            }

            val_wr.currency_rate = match val_wr.current_amount.cmp(&0) {
                Ordering::Less => req.buy_rate,
                Ordering::Equal | Ordering::Greater => req.sell_rate,
            };
        }
    }

    async fn set_unit_positions(&self, posinfo_req: &UserPositionData) {
        // info!("set_unit_positions:{:?}", posinfo_req);
        let mut posinfo = posinfo_req.clone();
        let mut index: Option<usize> = None;
        {
            let mut num = 0;
            let mut flag = false;
            for item in self.user_positions_data.read().await.iter() {
                let d = item.read().await;
                if d.unit_id == posinfo_req.unit_id && d.stock_id == posinfo_req.stock_id {
                    flag = true;
                    break;
                }
                num += 1;
            }
            if flag {
                index = Some(num);
            }
        }
        {
            if index.is_none() {
                //insert
                //获取最新价
                // let last_price = hqcenter_svc.query_stock_last_price(posinfo.stock_code.clone(), posinfo.exchange_id as i32).await;
                let mut pos_wr = self.user_positions_data.write().await;
                pos_wr.push(RwLock::new(posinfo.to_owned()));
            } else {
                let pos_rd = self.user_positions_data.read().await;
                let index = index.unwrap();
                let get_val = pos_rd.get(index);
                if get_val.is_some() {
                    let gval = get_val.unwrap();
                    {
                        let price = gval.read().await.last_price;
                        if price > 0.0 {
                            posinfo.last_price = gval.read().await.last_price;
                        }
                        posinfo.margin_rate = gval.read().await.margin_rate;
                    }
                    let mut wr_val = gval.write().await;
                    *wr_val = posinfo.to_owned();
                }
            }
        }
    }
}
