use chrono::Utc;
// heartbeat-common/src/lib.rs
// use protoes::heartbeat::heartbeat_service_server::HeartbeatService;
// use protoes::protos::heartbeat::{HeartbeatRequest, HeartbeatResponse};
use protoes::{HeartbeatRequest, HeartbeatResponse, heartbeat_service_server::HeartbeatService};
use std::pin::Pin;
use tokio::time::{Duration, interval};
use tokio_stream::{Stream, StreamExt, wrappers::IntervalStream};
use tonic::{Request, Response, Status, Streaming};
use tracing::*;
// pub mod heartbeat; // 包含生成的 proto 代碼

pub struct HeartbeatServiceImpl {
    interval: u64,
}

impl Default for HeartbeatServiceImpl {
    fn default() -> Self {
        HeartbeatServiceImpl { interval: 10 } // Increase to 10 seconds for less network traffic
    }
}

#[tonic::async_trait]
impl HeartbeatService for HeartbeatServiceImpl {
    type HeartbeatStreamStream = Pin<Box<dyn Stream<Item = Result<HeartbeatResponse, Status>> + Send + 'static>>;

    async fn heartbeat_stream(&self, request: Request<Streaming<HeartbeatRequest>>) -> Result<Response<Self::HeartbeatStreamStream>, Status> {
        let mut client_stream = request.into_inner();
        let client_id = match client_stream.message().await? {
            Some(req) => req.client_id,
            None => return Err(Status::invalid_argument("Missing initial heartbeat request")),
        };
        info!(client_id, "💓 Client connected for heartbeat");

        // 創建服務端心跳流
        let interval = interval(Duration::from_secs(self.interval));
        let client_id_for_stream = client_id.clone();
        let output_stream = IntervalStream::new(interval).map(move |_| {
            let timestamp = Utc::now().timestamp();
            debug!(client_id_for_stream, timestamp, "Sending heartbeat to client");
            Ok(HeartbeatResponse { timestamp, status: "OK".to_string() })
        });

        // 處理客戶端心跳請求
        // tokio::spawn(async move {
        //     while let Some(result) = client_stream.next().await {
        //         match result {
        //             Ok(req) => {
        //                 debug!(client_id = req.client_id, timestamp = req.timestamp, "Received heartbeat from client");
        //             }
        //             Err(e) => {
        //                 warn!(client_id, "Error receiving heartbeat from client: {}", e);
        //                 break;
        //             }
        //         }
        //     }
        // });

        // 處理客戶端心跳（即使客戶端不發送也繼續運行）
        tokio::spawn(async move {
            while let Some(req) = client_stream.message().await.transpose() {
                match req {
                    Ok(req) => {
                        debug!(client_id = req.client_id, timestamp = req.timestamp, "Received heartbeat from client");
                    }
                    Err(e) => {
                        warn!(client_id, "Error receiving heartbeat from client: {}", e);
                        break;
                    }
                }
            }
            warn!(client_id, "Client heartbeat stream closed");
        });

        Ok(Response::new(Box::pin(output_stream)))
    }
}
