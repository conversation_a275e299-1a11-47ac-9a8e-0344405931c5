syntax = "proto3";

package phoenixomsapi;

enum SvcType {
  ALL = 0;
  FUTURES = 1; // 期货
  STOCK = 2;   // 证券
  DIGICCY = 3; // 数字货币
  FOREX = 4;   // 外汇、衍生品
}

// 请求类别
enum ReqType {
  UNDEF = 0;
  SYS_OP_INIT = 1; // 初始化
  // 期货
  FU_OP_ORDER = 2;
  FU_OP_CANCEL = 3;
  FU_MSG_STATE = 4;
  FU_MSG_ORDER = 5;
  FU_MSG_ASSET = 6;
  FU_MSG_POSITION = 7;
  // 证券
  ST_OP_ORDER = 12;     // 下单
  ST_OP_CANCEL = 13;    // 撤单
  ST_MSG_STATE = 14;    // 订单状态
  ST_MSG_ORDER = 15;    // 委托
  ST_MSG_ASSET = 16;    // 资产
  ST_MSG_POSITION = 17; // 持仓
  ST_MSG_RQEANBLE = 18;
  ST_MSG_MARGINRATE = 19; // 保证金比例
  ST_MSG_ORDER_EXEC = 20;
  ST_MSG_RQ_CHANGE = 21; // 融券额度变化通知

  // client注册
  SYS_CLIENT_REG = 30;

  SYS_HEART_BEAT = 0x99;
  // 结算通知
  SYS_SETTLE_NOTIC = 0xfe;
  // 系统公告
  SYS_MSG_NOTIC = 0xff;
}

// 请求状态
enum ReqState {
  SUCCESS = 0;
  MESSAGE_ERROR = 1;       // 消息包错误
  GATEWAY_FAULT = 2;       // 网关异常
  VERIFY_FAILED = 3;       // 认证失败
  CNN_UNINIT = 4;          // 未初始化连接
  REQUEST_TYPE_UNKNOW = 5; // 请求类型有误
  GRPC_REQUEST_FAILED = 6; // 交易服务请求失败
  SERVER_FAULT = 7;        // 交易服务出错

  GATEWAY_TS_FAULT = 30;

  CNN_DISCONNECT = 0xff;
}

// 初始化连接请求
message ConnRequest {
  int64 client_id = 1;    // 用户id(非account_id)
  int32 mode = 2;         // 1:USER(用户模式)  2.sdk(托管)  3.oas(托管)
  string token = 3;       // 用户token
  string api_key = 4;     // api key
  string client_type = 5; // 客户端类型
  string client_version = 6; // 客户端版本
  string client_ip = 7;      // 客户端ip
}

// 托管客户端注册
message ClientRegReq { int64 client_id = 1; }

/////////////////////////////////////////////////////////////////////
//----------------------------------下单请求----------------------------------
message OrderReq {
  int64 msg_id = 1;          // 消息ID
  int64 unit_id = 2;         // 用户id
  int64 stock_id = 3;        // 证券id
  int32 order_direction = 4; // 委托方向  1=买  2=卖
  int32 order_qty = 5;       // 订单数量
  int32 price_type = 6;      // 价格类型(市价限价)
  double order_price = 7;    // 委托价格
  int64 operator_no = 8;     // 操作员
  int32 order_type =
      9; // 委托类型 1:app下单  2:跟单  3:风控止盈止损平仓单,4:风控总资产预警平仓单 5:pc客户端单 6:结算平仓单 7:管理端强平仓单,8:app清仓,9:pc清仓,10,管理员平仓,11,合约到期日强平,12,算法单
  int32 trade_mode = 10;    // 1:USER(用户直连) 2:AGENT(代理托管)
  int64 agent_account = 11; // 代理账户
  int64 algorithm_id = 12;  // 非必填, 算法单id
  int64 user_id = 13;       // 子账户id
}

//  撤单请求数据类型
message CancelReq {
  int64 msg_id = 1;      // 消息ID
  int64 unit_id = 2;     // 用户id
  int64 order_id = 3;    // 订单id(撤单用)
  int64 operator_no = 4; // 操作员
  int32 cancel_type =
      5; // 撤单类型 1:app撤单  2:pc撤单  3:风控撤单  4:管理员撤单
  int32 trade_mode = 6;      // 1:USER(用户直连) 2:AGENT(代理托管)
  int64 agent_account = 7;   // 代理账户
  int32 internal_cancel = 8; // 非必传,内部撤单时非0
  int64 user_id = 9;         // 子账户id
}

//  请求响应
message OrderResp {
  int64 msg_id = 1; // 与请求消息ID对应
  int64 order_id = 2;
  int32 error_code = 3;
  string error_msg = 4;
}

//////////////////////////////////////////////////////////////////////
////////////主推消息//////////////

// 公告/通知
message NoticeMsg { string content = 1; }

message OrderStatus {
  int64 unit_id = 1;        // 用户id
  int64 order_id = 2;       // 订单ID
  int32 order_quantity = 3; // 发生数量
  double order_price = 4;
  string order_action = 5; // 1:新订单  2:成交  3:撤单
  int32 order_status = 6;  // 当前状态
  int64 deal_no = 7;       // 成交编号
  string memo = 8;         // 备注
  int64 user_id = 9;
}

// 资产信息
message UnitAsset {
  int64 unit_id = 1;               // 单元编号
  double current_cash = 2;         // 当前本金
  double frozen_capital = 3;       // 冻结资金
  double trade_frozen_capital = 4; // 交易临时冻结
  double begin_cash = 5;           // 期初本金
  double cash_in_transit = 6;      // 在途资金
  string currency_no = 7;          // 币种
  double credit_multiple = 8;      // 信用倍数
  int64 timestamp = 9;
  double today_deposit = 10;      // 今日入金
  double today_withdraw = 11;     // 今日出金
  double total_deposit = 12;      // 总入金
  double total_withdraw = 13;     // 总出金
  double today_total_value = 14;  // 昨日本金
  double gem_frozen_capital = 15; // 创业板挂单保证金占用
  int64 user_id = 16;
}

// 持仓信息
message UserPosition {
  int64 unit_id = 1;             // 单元编号
  int64 position_no = 2;         // 持仓编号
  string stock_code = 3;         // 证券代码
  int64 stock_id = 4;            // 证券id
  int64 exchange_id = 5;         // 市场ID
  int32 position_flag = 6;       // 1多 2空
  int32 begin_amount = 7;        // 期初数量
  int32 current_amount = 8;      // 当前数量
  int32 frozen_amount = 9;       // 冻结数量
  int32 temp_frozen_amount = 10; // 临时冻结数量
  int32 buy_amount = 11;         // 今买数量
  int32 sale_amount = 12;        // 今卖数量
  int32 prebuy_amount = 13;      // 预买数量
  int32 presale_amount = 14;     // 预卖数量
  int32 buy_in_transit = 15;     // 在途持仓数量(买)
  int32 sale_in_transit = 16;    // 在途持仓数量(卖)
  int64 channel_id = 17;         // 通道id
  int32 stock_type = 18;         // 股票类别
  double margin_rate = 19;       // 保证金比例
  double total_value = 20;       // 开仓成本;
  double total_value_hkd = 21;   // 港币开仓成本
  int32 qfii_amount = 22;        // qf持仓数量
  int64 timestamp = 23;
  double last_price = 24; // 最新价
  int64 user_id = 25;
}

// 委托信息
message OrderInfo {
  int64 unit_id = 1;          // 用户id
  int32 order_direction = 2;  // 委托方向
  string stock_code = 3;      // 证券代码
  double order_price = 4;     // 委托价格
  int64 order_no = 5;         // 委托编号
  int32 order_type = 6;       // 委托类别
  double deal_price = 7;      // 成交均价
  int32 order_amount = 8;     // 委托数量
  int32 deal_amount = 9;      // 成交数量
  int32 canceled_amount = 10; // 已撤数量
  string create_time = 11;    // 委托时间 2023-02-01 14:35:25
  string last_deal_time = 12; // 最后成交时间 2023-02-01 14:35:25
  int32 order_status = 13;    // 订单状态
  int64 stock_id = 14;        // 证券id
  string order_memo = 15;     // 废单原因
  int64 user_id = 16;
}

// 结算消息
message SettleMsg {
  SvcType busin_type = 1; // 结算消息类型  0: 全部, 1: 期货, 2: 证券;
}

// 融券消息
message RqEanble {
  int32 user_id = 1;     // 用户id
  int32 rq_eanble = 2;   // 融券数量
  int32 stock_code = 3;  // 证券编号
  int64 notice_time = 4; // 通知时间
}

// 保证金比例调整
message MarginRate {
  int32 stock_id = 1;
  double margin_rate = 2;
}

// 订单执行回报
message OrderExecMsg {
  int64 exec_id = 1;        // 成交编号
  int32 exec_type = 2;      // 1:新订单  2:成交  3:撤单
  int64 order_id = 3;       // 订单ID
  string stock_code = 4;    // 股票代码
  int32 quantity = 5;       // 发生数量
  double price = 6;         // 价格
  string transact_time = 7; // 时间 格式: 20230425-09:17:20
  string memo = 8;          // 备注
}

message RqChangeMsg {
  int64 stock_id = 1; // 品种id
  int32 rq_state = 2; // 1新增 2生效 3召回 4删除 5过期
  string stock_code = 3;
  string stock_name = 4;
}