//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "his_users_extend_info")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub user_id: i64,
    pub assets_source: String,
    pub assets_type: String,
    pub net_assets: i32,
    pub user_income: i32,
    pub bankruptcy_desc: String,
    pub career_type: i32,
    pub company_name: String,
    pub company_type: i32,
    pub company_address: String,
    pub user_position: i32,
    pub working_life: i32,
    pub postal_address: String,
    pub card_address: String,
    pub postal_province: String,
    pub postal_city: String,
    pub company_province: String,
    pub company_city: String,
    pub last_grada_risk_level: i8,
    pub last_revisit_id: i64,
    pub postal_country: String,
    pub family_surname: Option<String>,
    pub family_name: Option<String>,
    pub tax_declaration: Option<String>,
    pub position_desc: String,
    pub industry_type: i32,
    pub industry_desc: String,
    #[sea_orm(primary_key, auto_increment = false)]
    pub c_date: i64,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
