// use crate::config::settings::Settings;
use anyhow::{anyhow, Result};
use common::logclient::log_error;
use tonic::transport::Channel;
use tracing::*;
// use tokio_stream::StreamExt;
// use tokio::sync::mpsc::{Sender, Receiver};

use protoes::phoenixordercenter::order_center_service_client::OrderCenterServiceClient;
use protoes::phoenixordercenter::ReplenishOrderReq;

#[derive(Clone)]
pub struct OrderCenterClient {
    pub client: Option<OrderCenterServiceClient<Channel>>,
    uri: String,
}

impl OrderCenterClient {
    pub async fn new(uri: &String) -> Self {
        let mut or_client = Self { client: None, uri: uri.to_string() };
        match OrderCenterServiceClient::connect(uri.to_string()).await {
            Ok(client) => {
                info!("订单中心连接成功");
                or_client.client = Some(client)
            }
            Err(err) => {
                error!("connect to OrderCenter failed: {}", err);
                log_error("connect to Order<PERSON>enter failed").await;
            }
        }
        or_client
    }

    pub async fn init_client(&mut self) -> Result<OrderCenterServiceClient<Channel>> {
        if let Some(client) = &self.client {
            return Ok(client.clone());
        }
        match OrderCenterServiceClient::connect(self.uri.to_owned()).await {
            Ok(c) => {
                info!("订单中心连接成功....");
                self.client = Some(c.clone());
                Ok(c)
            }
            Err(err) => {
                error!("订单中心连接失败: {:?}", err);
                log_error(format!("connect to OrderCenter failed: {:?}", self.uri).as_str()).await;
                Err(anyhow!("connect to OrderCenter failed"))
            }
        }
    }

    pub async fn replenishment_order(&mut self, req: &ReplenishOrderReq) -> Result<()> {
        let mut client = match self.init_client().await {
            Ok(client) => client,
            Err(err) => return Err(anyhow!(err.to_string())),
        };

        match client.replenishment_order(req.to_owned()).await {
            Ok(val) => {
                info!("replenishment_order: {:?}", val);
                return Ok(());
            }
            Err(status) => {
                self.client = None;
                error!("order center status: {:?}", status);
                return Err(anyhow!("order center status: {:?}", status));
            }
        }
    }
}
