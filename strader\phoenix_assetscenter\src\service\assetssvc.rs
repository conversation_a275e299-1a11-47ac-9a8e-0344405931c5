use std::collections::HashMap;

use crate::app::constdata;
use crate::controller::PersistData;
// use crate::controller::NotificationData;
use crate::dataservice::dbsetup::DbConnection;
use crate::dataservice::entities::prelude::*;
use anyhow::{anyhow, Result};
use protoes::assetscenter::{PhoenixassetsResult, PhoenixassetsResultInfo, PhoenixassetscenterRequestInfo};
// use common::akaclient::AkaClient;
use common::constant;
use common::logclient::{LogClient, LogLevel};
use common::redisclient::redispool::RedisClient;
// use futures::channel::mpsc;
// use messagecenter::assetsclient::AssetsChangeClient;
use protoes::phoenixnotification::NotificationAsset;
// use rand::{random, Rng};
use rust_decimal::Decimal;
// use tokio::sync::{mpsc, RwLock};
// use tokio::sync::mpsc;
use super::assetsmodel::AssetsData;
use utility::timeutil;
// use super::akacenterclient::AkacenterClient;
use super::utils;
use tracing::*;

pub struct UnitAssetsService {}

//主要业务逻辑：
//1）初始化时，从数据库读取资产并保存到redis
//2) 查询时直接从redis读取（无锁）
//3) 资产调整时，从redis读取数据（加锁），更新后写回redis，并解锁
impl UnitAssetsService {
    pub fn new() -> Self {
        UnitAssetsService {}
    }

    pub async fn push_log(&self, log: String) {
        error!("{}", log);
        let logclient = LogClient::get();
        if let Ok(c) = logclient {
            _ = c.push(LogLevel::Error, log.as_str()).await;
        }
    }

    pub async fn init(&self, unit_id: i64, user_id: i64, currency: String, db: &DbConnection) -> Result<Option<PhoenixAstUnitasset>> {
        let ret: std::prelude::v1::Result<Option<PhoenixAstUnitasset>, anyhow::Error> = PhoenixAstUnitasset::find_by_userid(unit_id, user_id, currency, db).await;
        if let Err(err) = ret {
            self.push_log(format!("db查询资产失败:{:?}", err)).await;
            return Err(err);
        }
        let assets_result_ok = ret.unwrap();
        Ok(assets_result_ok)
    }

    pub async fn query_unit_assets(&self, unit_id: i64, user_id: i64, currency: String, db: &DbConnection) -> Result<Option<PhoenixAstUnitasset>> {
        self.init(unit_id, user_id, currency, db).await
    }

    //创建资产用户
    pub async fn phoenix_assets_crate(&self, req: &AssetsData, db: &DbConnection, init_date: i32) -> Result<NotificationAsset> {
        let ret = self.assets_create(init_date, req, db).await;
        if !ret.is_err() {
            return Result::Ok(ret.unwrap());
        }
        return Result::Ok(NotificationAsset::default());
    }

    //资产更新
    pub async fn phoenix_assets_change(&self, req: &AssetsData, db: &DbConnection, init_date: i32) -> Result<(NotificationAsset, Vec<PersistData>)> {
        info!("phoenix_assets_change入口,{:?}", req);
        //查询资产缓存
        let currency = req.currency.clone();
        let assets_data = self.query_unit_assets(req.unit_id, req.user_id, currency.clone(), db).await;
        if let Err(err) = assets_data {
            info!("query_assets_by_unitid失败,{}", req.user_id);
            return Err(err);
        }
        let mut assets_data_ok = assets_data.unwrap();
        if assets_data_ok.is_none() {
            if currency.eq("CNH") {
                //若为CNH账户，查询账户不存在，默认创建一个
                let assetdata = AssetsData {
                    user_id: req.user_id,
                    currency: currency.clone(),
                    assets: PhoenixassetscenterRequestInfo { ..Default::default() },
                    ..Default::default()
                };
                let ret = self.assets_create(init_date, &assetdata, db).await;
                if let Err(err) = ret {
                    return Err(err);
                }
                let assets_data1 = self.query_unit_assets(req.unit_id, req.user_id, currency.clone(), db).await;
                if let Err(err) = assets_data1 {
                    info!("query_assets_by_unitid失败,{}", req.user_id);
                    return Err(err);
                }
                assets_data_ok = assets_data1.unwrap();
            }
        }

        if assets_data_ok.is_none() {
            return Err(anyhow!("用户{},币种：{}不存在,更新资产失败!", req.user_id, req.currency));
        }

        let mut assets_data = assets_data_ok.unwrap();

        info!("phoenix_assets_change缓存数据1,{:?}", assets_data);
        let mut flow_vec = Vec::new();
        let mut operation_vec = Vec::new();
        let item = req.assets.clone();
        if item.op_type == constant::AssetChangeDirection::AddCapital as i32
            || item.op_type == constant::AssetChangeDirection::ReduceCapital as i32
            || item.op_type == constant::AssetChangeDirection::TransferInCapital as i32
            || item.op_type == constant::AssetChangeDirection::TransferOutCapital as i32
        {
            let ret = self.assets_change_cash(&mut assets_data, req, init_date).await;
            if let Err(err) = ret {
                return Err(err);
            }
            let retdata = ret.unwrap();
            flow_vec.push(retdata.0);
            operation_vec.push(retdata.1);
        } else if item.op_type == constant::AssetChangeDirection::FrozenCapital as i32 || item.op_type == constant::AssetChangeDirection::UnFrozenCapital as i32 {
            let ret = self.assets_change_frozen_capital(&mut assets_data, req, init_date).await;
            if let Err(err) = ret {
                return Err(err);
            }
            let retdata = ret.unwrap();
            flow_vec.push(retdata.0);
            operation_vec.push(retdata.1);
        } else if item.op_type == constant::AssetChangeDirection::FrozenTradeCapital as i32 || item.op_type == constant::AssetChangeDirection::UnFrozenTradeCapital as i32 {
            let ret = self.assets_change_trade_frozen_capital(&mut assets_data, req, init_date).await;
            if let Err(err) = ret {
                return Err(err);
            }
            let retdata = ret.unwrap();
            flow_vec.push(retdata.0);
            operation_vec.push(retdata.1);
        } else if item.op_type == constant::AssetChangeDirection::ModifyUnitCredit as i32 {
            let ret = self.assets_change_credit_multiple(&mut assets_data, req, init_date).await;
            if let Err(err) = ret {
                return Err(err);
            }
            let retdata = ret.unwrap();
            flow_vec.push(retdata.0);
            operation_vec.push(retdata.1);
        }

        info!("update_assets数据,{:?}", assets_data);

        let mut per_vec = Vec::new();

        _ = self.update_assets_model_data(&vec![assets_data.clone()], db).await;

        //插入流水记录
        if flow_vec.len() > 0 {
            per_vec.push(PersistData::AssetsFlow(Box::new(flow_vec)));
        }
        if operation_vec.len() > 0 {
            per_vec.push(PersistData::OperationDetail(Box::new(operation_vec)));
        }

        //返回需要推送的数据
        let ret = self.phoenix_query_assets_formatdata(&assets_data).await;

        let ret = (ret, per_vec);
        return Ok(ret);
    }

    //创建资产用户
    pub async fn assets_create(&self, init_date: i32, req: &AssetsData, db: &DbConnection) -> Result<NotificationAsset> {
        //查询用户是否存在
        let user_id = req.user_id;
        let currency = req.currency.clone();
        let assetsinfo = req.assets.to_owned();
        let assets_result = self.query_unit_assets(0, user_id, currency.clone(), db).await;
        if let Err(err) = assets_result {
            self.push_log(format!("查询get_assets_by_id异常{}", err)).await;
            return Err(err);
        }

        if assets_result.unwrap().is_some() {
            self.push_log(format!("创建用户资产失败,已存在该 用户{}", user_id)).await;
            return Err(anyhow!(constdata::PARAM_USER_EXISTS));
        }

        let mut unit_id: i64 = user_id;
        if currency.eq("CNH") {
            unit_id = unit_id + 1000000
        }
        let credit_multiple = assetsinfo.change_amount.to_string().parse::<Decimal>().unwrap();
        let update_assets = PhoenixAstUnitasset {
            credit_multiple: credit_multiple,
            unit_id: unit_id,
            user_id: user_id,
            sys_date: init_date,
            currency_no: currency,
            ..Default::default()
        };

        let last_insert_id = PhoenixAstUnitasset::insert(&update_assets, db).await;
        if let Err(err) = last_insert_id {
            self.push_log(format!("用户数据插入失败{:?}", err)).await;
            return Err(err);
        }
        let ret = self.phoenix_query_assets_formatdata(&update_assets).await;
        Ok(ret)
    }

    //资产调整
    pub async fn assets_change_cash(&self, assets_data: &mut PhoenixAstUnitasset, item: &AssetsData, system_date: i32) -> Result<(PhoenixOmsAssetflow, PhoenixAstOperationDetail)> {
        let assets = item.assets.clone();
        let mut cash = assets.change_amount;
        if assets.op_type == constant::AssetChangeDirection::ReduceCapital as i32 || assets.op_type == constant::AssetChangeDirection::TransferOutCapital as i32 {
            cash = 0.0 - cash;
        }
        //增加本金
        let d = utils::decimal_add(assets_data.current_cash, cash);
        if d.is_none() {
            self.push_log(format!("数据处理异常{},{}", assets_data.current_cash, assets.change_amount)).await;
            return Err(anyhow!(constdata::DATA_ERROR));
        }
        assets_data.current_cash = d.unwrap();

        if assets.op_type == constant::AssetChangeDirection::TransferInCapital as i32 {
            let today_deposit_op = utils::decimal_add(assets_data.transfer_capital, assets.change_amount);
            let total_deposit_op = utils::decimal_add(assets_data.total_transfer_capital, assets.change_amount);
            if today_deposit_op.is_none() || total_deposit_op.is_none() {
                self.push_log(format!("数据处理异常{},{}，{}", assets_data.today_deposit, assets_data.total_deposit, assets.change_amount)).await;
                return Err(anyhow!(constdata::DATA_ERROR));
            }
            assets_data.transfer_capital = today_deposit_op.unwrap();
            assets_data.total_transfer_capital = total_deposit_op.unwrap();
        } else if assets.op_type == constant::AssetChangeDirection::TransferOutCapital as i32 {
            let today_deposit_op = utils::decimal_sub(assets_data.transfer_capital, assets.change_amount);
            let total_deposit_op = utils::decimal_sub(assets_data.total_transfer_capital, assets.change_amount);
            if today_deposit_op.is_none() || total_deposit_op.is_none() {
                self.push_log(format!("数据处理异常{},{}，{}", assets_data.today_deposit, assets_data.total_deposit, assets.change_amount)).await;
                return Err(anyhow!(constdata::DATA_ERROR));
            }
            assets_data.transfer_capital = today_deposit_op.unwrap();
            assets_data.total_transfer_capital = total_deposit_op.unwrap();
        }

        if assets.flag == constant::YesOrNo::YES as i32 {
            //出入金处理
            if assets.op_type == constant::AssetChangeDirection::ReduceCapital as i32 {
                let today_withdraw_op = utils::decimal_add(assets_data.today_withdraw, assets.change_amount);
                let total_withdraw_op = utils::decimal_add(assets_data.total_withdraw, assets.change_amount);
                if total_withdraw_op.is_none() || today_withdraw_op.is_none() {
                    self.push_log(format!("数据处理异常{},{}，{}", assets_data.today_deposit, assets_data.total_deposit, assets.change_amount)).await;
                    return Err(anyhow!(constdata::DATA_ERROR));
                }
                assets_data.today_withdraw = today_withdraw_op.unwrap();
                assets_data.total_withdraw = total_withdraw_op.unwrap();
            } else if assets.op_type == constant::AssetChangeDirection::AddCapital as i32 {
                let today_deposit_op = utils::decimal_add(assets_data.today_deposit, assets.change_amount);
                let total_deposit_op = utils::decimal_add(assets_data.total_deposit, assets.change_amount);
                if today_deposit_op.is_none() || total_deposit_op.is_none() {
                    self.push_log(format!("数据处理异常{},{}，{}", assets_data.today_deposit, assets_data.total_deposit, assets.change_amount)).await;
                    return Err(anyhow!(constdata::DATA_ERROR));
                }
                assets_data.today_deposit = today_deposit_op.unwrap();
                assets_data.total_deposit = total_deposit_op.unwrap();
            }
        }

        return self.assets_change_create_flow(assets_data, item, system_date).await;
    }

    //资产冻结解冻操作
    pub async fn assets_change_frozen_capital(&self, assets_data: &mut PhoenixAstUnitasset, req: &AssetsData, system_date: i32) -> Result<(PhoenixOmsAssetflow, PhoenixAstOperationDetail)> {
        let item = req.assets.clone();
        let mut cash = item.change_amount;
        if item.op_type == constant::AssetChangeDirection::UnFrozenCapital as i32 {
            cash = 0.0 - cash;
        }
        let d = utils::decimal_add(assets_data.frozen_capital, cash);
        if d.is_none() {
            self.push_log(format!("数据处理异常{},{}", assets_data.frozen_capital, item.change_amount)).await;
            return Err(anyhow!(constdata::DATA_ERROR));
        }

        let mut d1 = d.unwrap();
        if d1.lt(&Decimal::new(0, 0)) {
            d1 = Decimal::new(0, 0);
        }
        assets_data.frozen_capital = d1;

        return self.assets_change_create_flow(assets_data, req, system_date).await;
    }

    //资产临时冻结解冻操作
    pub async fn assets_change_trade_frozen_capital(&self, assets_data: &mut PhoenixAstUnitasset, req: &AssetsData, system_date: i32) -> Result<(PhoenixOmsAssetflow, PhoenixAstOperationDetail)> {
        let item = req.assets.clone();
        let mut cash = item.change_amount;
        if item.op_type == constant::AssetChangeDirection::UnFrozenTradeCapital as i32 {
            cash = 0.0 - cash;
        }
        let d = utils::decimal_add(assets_data.trade_frozen_capital, cash);
        if d.is_none() {
            self.push_log(format!("数据处理异常{},{}", assets_data.frozen_capital, item.change_amount)).await;
            return Err(anyhow!(constdata::DATA_ERROR));
        }

        assets_data.trade_frozen_capital = d.unwrap();
        if item.flag == constant::YesOrNo::YES as i32 {
            let gem_frozen_capital_op = utils::decimal_add(assets_data.gem_frozen_capital, cash);
            if gem_frozen_capital_op.is_some() {
                assets_data.gem_frozen_capital = gem_frozen_capital_op.unwrap();
            }
        }
        return self.assets_change_create_flow(assets_data, req, system_date).await;
    }

    //资产信用倍数调整
    pub async fn assets_change_credit_multiple(&self, assets_data: &mut PhoenixAstUnitasset, req: &AssetsData, system_date: i32) -> Result<(PhoenixOmsAssetflow, PhoenixAstOperationDetail)> {
        let item = req.assets.clone();
        let d_op = Decimal::from_f64_retain(item.change_amount);
        if d_op.is_none() {
            self.push_log(format!("数据处理异常{}", item.change_amount)).await;
        }
        assets_data.credit_multiple = d_op.unwrap();
        return self.assets_change_create_flow(assets_data, req, system_date).await;
    }

    //创建流水记录
    pub async fn assets_change_create_flow(&self, assets_data: &PhoenixAstUnitasset, req: &AssetsData, system_date: i32) -> Result<(PhoenixOmsAssetflow, PhoenixAstOperationDetail)> {
        //创建资金流水表
        let item = req.assets.to_owned();
        let amount = Decimal::from_f64_retain(item.change_amount).unwrap();
        let mark = item.memo.clone();
        let flow = PhoenixOmsAssetflow {
            sys_date: system_date,
            unit_id: assets_data.unit_id,
            user_id: assets_data.user_id,
            busin_flag: req.business_flag,
            occur_capital: amount,
            post_capital: assets_data.current_cash,
            datetime: timeutil::current_timestamp(),
            remarks: item.to_owned().memo,
            op_type: item.op_type,
            currency_no: assets_data.currency_no.clone(),
            currency_rate: Decimal::from(1),
            ..Default::default()
        };

        //创建资金操作表记录
        let option_detail = PhoenixAstOperationDetail {
            sys_date: system_date,
            unit_id: assets_data.unit_id,
            user_id: assets_data.user_id,
            op_businflag: req.business_flag.to_string(),
            remark: mark,
            currency_no: assets_data.currency_no.clone(),
            create_time: timeutil::current_timestamp(),
            operator: req.operator_id as i32,
            op_type: item.op_type,
            occur_capital: amount,
            ..Default::default()
        };

        let ret = (flow, option_detail);
        Ok(ret)
    }

    pub async fn phoenix_query_assets_formatdata(&self, asset: &PhoenixAstUnitasset) -> NotificationAsset {
        let current_cash = asset.current_cash.to_string().parse::<f64>();
        let frozen_capital = asset.frozen_capital.to_string().parse::<f64>();
        let trade_frozen_capital = asset.trade_frozen_capital.to_string().parse::<f64>();
        let begin_cash = asset.begin_cash.to_string().parse::<f64>();
        let cash_in_transit = asset.cash_in_transit.to_string().parse::<f64>();
        let credit_multiple = asset.credit_multiple.to_string().parse::<f64>();
        let today_deposit = asset.today_deposit.to_string().parse::<f64>();
        let today_withdraw = asset.today_withdraw.to_string().parse::<f64>();
        let total_deposit = asset.total_deposit.to_string().parse::<f64>();
        let total_withdraw = asset.total_withdraw.to_string().parse::<f64>();
        let today_total_value = asset.today_total_value.to_string().parse::<f64>();
        let gem_frozen_capital = asset.gem_frozen_capital.to_string().parse::<f64>();
        let transfer_capital = asset.transfer_capital.to_string().parse::<f64>();
        let total_transfer_capital = asset.total_transfer_capital.to_string().parse::<f64>();

        let ret = NotificationAsset {
            unit_id: asset.unit_id,
            user_id: asset.user_id,
            current_cash: current_cash.unwrap_or_default(),
            frozen_capital: frozen_capital.unwrap_or_default(),
            trade_frozen_capital: trade_frozen_capital.unwrap_or_default(),
            begin_cash: begin_cash.unwrap_or_default(),
            cash_in_transit: cash_in_transit.unwrap_or_default(),
            currency_no: asset.currency_no.to_owned(),
            credit_multiple: credit_multiple.unwrap_or_default(),
            timestamp: timeutil::current_timestamp(),
            today_deposit: today_deposit.unwrap_or_default(),
            today_withdraw: today_withdraw.unwrap_or_default(),
            total_deposit: total_deposit.unwrap_or_default(),
            total_withdraw: total_withdraw.unwrap_or_default(),
            today_total_value: today_total_value.unwrap_or_default(),
            gem_frozen_capital: gem_frozen_capital.unwrap_or_default(),
            transfer_cash: transfer_capital.unwrap_or_default(),
            total_transfer_cash: total_transfer_capital.unwrap_or_default(),
        };
        return ret;
    }

    pub async fn convert_assets_to_resultinfo(&self, asset: &PhoenixAstUnitasset, lastday_frozen_capital: Decimal) -> Result<Option<PhoenixassetsResultInfo>> {
        let current_cash = asset.current_cash.to_string().parse::<f64>().unwrap_or_default();
        let frozen_capital = asset.frozen_capital.to_string().parse::<f64>().unwrap_or_default();
        let trade_frozen_capital = asset.trade_frozen_capital.to_string().parse::<f64>().unwrap_or_default();
        let begin_cash = asset.begin_cash.to_string().parse::<f64>().unwrap_or_default();
        let cash_in_transit = asset.cash_in_transit.to_string().parse::<f64>().unwrap_or_default();
        let credit_multiple = asset.credit_multiple.to_string().parse::<f64>().unwrap_or_default();
        let today_deposit = asset.today_deposit.to_string().parse::<f64>().unwrap_or_default();
        let total_deposit = asset.total_deposit.to_string().parse::<f64>().unwrap_or_default();
        let today_withdraw = asset.today_withdraw.to_string().parse::<f64>().unwrap_or_default();
        let total_withdraw = asset.total_withdraw.to_string().parse::<f64>().unwrap_or_default();
        let today_total_value = asset.today_total_value.to_string().parse::<f64>().unwrap_or_default();
        let gem_frozen_capital = asset.gem_frozen_capital.to_string().parse::<f64>().unwrap_or_default();
        let lastday_frozen_capital = lastday_frozen_capital.to_string().parse::<f64>().unwrap_or_default();
        let transfer_capital = asset.transfer_capital.to_string().parse::<f64>().unwrap_or_default();
        let total_transfer_capital = asset.total_transfer_capital.to_string().parse::<f64>().unwrap_or_default();
        let ret = PhoenixassetsResultInfo {
            unit_id: asset.unit_id,

            current_cash: current_cash,
            frozen_capital: frozen_capital,
            trade_frozen_capital: trade_frozen_capital,
            begin_cash: begin_cash,
            cash_in_transit: cash_in_transit,
            currency_no: asset.currency_no.clone(),
            credit_multiple: credit_multiple,
            today_deposit: today_deposit,
            today_withdraw: today_withdraw,
            total_deposit: total_deposit,
            total_withdraw: total_withdraw,
            last_cash: today_total_value,
            gem_frozen_capital: gem_frozen_capital,
            lastday_frozen_capital: lastday_frozen_capital,
            transfer_cash: transfer_capital,
            total_transfer_cash: total_transfer_capital,
        };
        return Ok(Some(ret));
    }

    pub async fn phoenix_query_all_units(&self, db: &DbConnection, _redis: &RedisClient) -> Result<Vec<PhoenixassetsResult>> {
        let mut ret = Vec::new();
        //查询所有用户信息
        let ret_units = PhoenixAstUnitasset::find_all(db).await;
        if let Err(err) = ret_units {
            self.push_log(format!("phoenix_query_all_units资产查询失败{:?},", err)).await;
            return Err(err);
        }
        let units_vec = ret_units.unwrap();

        let systemdate = PhoenixSysSystem::find(db).await;
        if let Err(err) = systemdate {
            self.push_log(format!("phoenix_query_all_units日期查询失败{:?},", err)).await;
            return Err(err);
        }
        let systemdate = systemdate.unwrap();
        if systemdate.is_none() {
            self.push_log(format!("phoenix_query_all_units日期查询失败,")).await;
            return Err(anyhow!(constdata::DATA_ERROR));
        }
        let systemdate = systemdate.unwrap();

        let list = PhoenixAstUnitassetHis::find_by_unitid_list(db, systemdate.preinit_date).await;
        if let Err(err) = list {
            self.push_log(format!("phoenix_query_all_units日期查询失败{:?},", err)).await;
            return Err(err);
        }
        let list = list.unwrap();

        let mut maps = HashMap::new();
        for item in list {
            maps.insert(item.unit_id, item.frozen_capital);
        }

        for item in units_vec {
            //查询历史资产
            let mut lastday_frozen_capital = Decimal::new(0, 0);

            if maps.contains_key(&item.unit_id) {
                let d1 = maps.get(&item.unit_id);
                if d1.is_some() {
                    lastday_frozen_capital = d1.unwrap().to_owned();
                }
                //lastday_frozen_capital = d1.to_owned();
            }

            let assets = self.convert_assets_to_resultinfo(&item, lastday_frozen_capital).await;
            if let Err(err) = assets {
                self.push_log(format!("phoenix_query_all_units资产查询失败{:?},", err)).await;
                return Err(err);
            }
            let result_item = PhoenixassetsResult {
                unit_id: item.unit_id,
                user_id: item.user_id,
                assets: assets.unwrap(),
                ..Default::default()
            };
            ret.push(result_item);
        }
        return Ok(ret);
    }

    //保存数据到db
    pub async fn update_assets_model_data(&self, assets_data: &Vec<PhoenixAstUnitasset>, db: &DbConnection) -> Result<()> {
        for item in assets_data.iter() {
            let ret = PhoenixAstUnitasset::update(item, db).await;
            if ret.is_err() {
                self.push_log(format!("update_assets_model_data插入失败{:?},", ret)).await;
            }
        }
        Ok(())
    }
    //保存流水到db
    pub async fn save_assets_flow_data(&self, flow_data: &Vec<PhoenixOmsAssetflow>, db: &DbConnection) -> Result<()> {
        let ret = PhoenixOmsAssetflow::insert_many(flow_data, db).await;
        if ret.is_err() {
            self.push_log(format!("save_assets_flow_data插入失败{:?},", ret)).await;
        }
        Ok(())
    }

    //保存操作记录到db
    pub async fn save_assets_operations_data(&self, operation_vec: &Vec<PhoenixAstOperationDetail>, db: &DbConnection) -> Result<()> {
        let ret = PhoenixAstOperationDetail::insert_many(&operation_vec, db).await;
        if ret.is_err() {
            self.push_log(format!("save_assets_operations_data插入失败{:?},", ret)).await;
        }
        Ok(())
    }

    //强制刷新用户缓存数据
    pub async fn refresh_unit_data(&self, user_id: i64, db: &DbConnection, _init_date: i32) -> Result<NotificationAsset> {
        let ret = self.query_unit_assets(0, user_id, "HKD".to_string(), db).await;
        if let Err(_err) = ret {
            return Err(anyhow!(constdata::DATA_ERROR));
        }

        let r_o = ret.unwrap();
        if r_o.is_none() {
            return Err(anyhow!(constdata::DATA_ERROR));
        }
        // let ret = self.phoenix_query_assets_formatdata(&r_o.unwrap()).await;

        let ret = self.query_unit_assets(0, user_id, "CNH".to_string(), db).await;
        if let Err(_err) = ret {
            return Err(anyhow!(constdata::DATA_ERROR));
        }

        let r_o = ret.unwrap();
        if r_o.is_none() {
            return Err(anyhow!(constdata::DATA_ERROR));
        }
        let ret = self.phoenix_query_assets_formatdata(&r_o.unwrap()).await;
        Ok(ret)
    }
}
