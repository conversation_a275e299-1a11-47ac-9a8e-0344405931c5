[package]
name = "phoenix_exchanger"
version = "0.2.0"
edition = "2021"
description = "虚拟交易所 build time: 2025-07-07"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
utility = { workspace = true }
messagecenter = { workspace = true }
common = { workspace = true }
akaclient = { workspace = true }
protoes = { workspace = true }
orderrouterclient = { workspace = true }
dbconnection = { workspace = true }
heartbeat-common = { workspace = true }

moka = { workspace = true }
config = { workspace = true }

tracing = { workspace = true }

serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
futures = { workspace = true }
lapin = { workspace = true }
tonic = { workspace = true }
prost = { workspace = true }
tokio-stream = { workspace = true }
rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }
async-stream = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
h2 = { workspace = true }
# log = { workspace = true }
dashmap = { workspace = true }
sea-orm = { workspace = true }
chrono = { workspace = true }
redis = { workspace = true }
redis_cluster_async = { workspace = true }
async-channel = { workspace = true }
rand = { workspace = true }

async-global-executor = { workspace = true }
# parking_lot = "0.12"

# [build-dependencies]
# # tonic-build = { workspace = true, features = ["prost"] }
# tonic-build.workspace = true


#[[bin]]
#name="test3"
#path= "src/test3.rs"
#[[bin]]
#name="test2"
#path= "src/test2.rs"
#[[bin]]
#name="test"
#path= "src/test.rs"
