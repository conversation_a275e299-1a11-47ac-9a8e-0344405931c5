use std::{collections::HashMap, i32, vec};
use tracing::*;

use crate::{
    dataservice::{
        dbsetup::DbConnection,
        entities::prelude::{
            PhoenixAstStockposition, PhoenixAstStockpositionHis, PhoenixAstUnitasset, PhoenixAstUnitassetHis, PhoenixOrdSettledetail, PhoenixOrdSettledetailHis, PhoenixOrdStockdealHis, PhoenixUserAssetsHis,
        },
    },
    results,
};
use anyhow::{anyhow, Result};
use common::constant::{MarketToCurrency, StockType};
use rust_decimal::Decimal;

//recalculate_user_assets 重算phoenix_user_assets_his 记录
pub async fn recalculate_user_assets(ldate: i32, predate: i32, sellrate: Decimal, buyreate: Decimal, db: &DbConnection) -> Result<()> {
    let assets;
    let positions;

    let sellrate = sellrate.to_string().parse::<f64>().unwrap_or_default();
    let buyreate = buyreate.to_string().parse::<f64>().unwrap_or_default();
    //查询持仓和资金
    if ldate == 0 {
        let curr_assets = results!(PhoenixAstUnitasset::query_many(db).await);
        let jsonstr = serde_json::to_value(&curr_assets).unwrap_or_default();
        assets = serde_json::from_value(jsonstr).unwrap_or_default();

        let curr_positions = results!(PhoenixAstStockposition::query_many(db).await);
        let jsonstr = serde_json::to_value(&curr_positions).unwrap_or_default();
        positions = serde_json::from_value(jsonstr).unwrap_or_default();
    } else {
        assets = results!(PhoenixAstUnitassetHis::query_many(ldate, db).await);
        positions = results!(PhoenixAstStockpositionHis::query_many(ldate, db).await);
    }

    //查询上个交易日的资金数据
    let last_assets = results!(PhoenixAstUnitassetHis::query_many(predate, db).await);
    let mut lastday_frozen_capital_map = HashMap::new();
    for item in last_assets {
        lastday_frozen_capital_map.insert(item.unit_id, item.frozen_capital.to_string().parse::<f64>().unwrap_or_default());
    }

    //查询上个交易日的user_asset_his数据
    let mut postion_map = HashMap::new();

    for item in positions.iter() {
        postion_map.entry(&item.unit_id).or_insert(vec![]).push(item);
    }

    //费用差值计算
    let deals = results!(PhoenixOrdStockdealHis::find_by_date(db, ldate).await);
    let mut settles: Vec<PhoenixOrdSettledetail> = results!(PhoenixOrdSettledetail::query_many(ldate, db).await);
    let settles_his = results!(PhoenixOrdSettledetailHis::query_many(ldate, db).await);
    info!("deals,{},{},{}", deals.len(), settles.len(), settles_his.len());

    if !settles_his.is_empty() {
        for item in settles_his {
            let jsonstr = serde_json::to_string(&item).unwrap_or_default();
            info!("jsonstr:{}", jsonstr);
            let settles_item = serde_json::from_str::<PhoenixOrdSettledetail>(&jsonstr);
            if settles_item.is_err() {
                error!("settles_item error{:?}", settles_item);
            } else {
                settles.push(settles_item.unwrap());
            }
        }
    }

    let mut fee_deal_map = HashMap::new();
    for item in deals.iter() {
        let mut fee = item.fee_total.to_string().parse::<f64>().unwrap_or_default();
        if item.order_direction == 1 {
            fee = fee * buyreate;
        } else {
            fee = fee * sellrate;
        }

        fee_deal_map
            .entry(item.unit_id)
            .and_modify(|f| {
                *f += fee;
            })
            .or_insert(fee);
    }

    let mut fee_settle_map = HashMap::new();
    for item in settles.iter() {
        let mut fee = item.fee_total.to_string().parse::<f64>().unwrap_or_default();
        if item.order_direction == 1 {
            fee = fee * buyreate;
        } else {
            fee = fee * sellrate;
        }
        info!("fee---{},{},{:?}", item.unit_id, fee, item);
        fee_settle_map
            .entry(item.unit_id)
            .and_modify(|f| {
                *f += fee;
            })
            .or_insert(fee);
    }
    let str = serde_json::to_string(&fee_settle_map).unwrap_or_default();
    info!("fee_settle_map data ---:{}", str);
    let str = serde_json::to_string(&fee_deal_map).unwrap_or_default();
    info!("fee_deal_map data ---:{}", str);

    let mut user_assets_vec = vec![];

    for item in assets.iter() {
        let mut user_his = PhoenixUserAssetsHis {
            sys_date: ldate,
            unit_id: item.unit_id,
            user_id: item.user_id,
            ..Default::default()
        };
        let mut tvalue = 0.0;
        //创业板市值
        let mut cybtvalue = 0.0;
        //保证金占用
        let mut real_margin = 0.0;
        //创业板保证金占用
        let mut cyb_real_margin = 0.0;
        //持仓盈亏
        let mut hold_yk = 0.0;
        //风险率
        let mut risk_rate = 0.0;

        if postion_map.contains_key(&item.unit_id) {
            let p_list = postion_map.get(&item.unit_id).unwrap();

            for i in p_list {
                let mut cal_rate = 1.0;
                if i.exchange_id != MarketToCurrency::XHKG as i32 {
                    cal_rate = sellrate;
                }
                let pvalue = i.current_amount as f64 * i.last_price.to_string().parse::<f64>().unwrap_or_default() * cal_rate;

                tvalue += pvalue;
                if i.stock_type == StockType::HSCY as i32 || i.stock_type == StockType::HSKC as i32 {
                    cybtvalue += pvalue;
                    cyb_real_margin += pvalue * i.margin_rate.to_string().parse::<f64>().unwrap_or_default();
                }
                real_margin += pvalue * i.margin_rate.to_string().parse::<f64>().unwrap_or_default();
                hold_yk += pvalue - i.total_value_hkd.to_string().parse::<f64>().unwrap_or_default();
            }
        }

        let current_cash = item.current_cash.to_string().parse::<f64>().unwrap_or_default();

        //费用处理

        let deal_total = fee_deal_map.get(&item.unit_id).unwrap_or(&0.0);
        let settle_total = fee_settle_map.get(&item.unit_id).unwrap_or(&0.0);
        info!("current_cash,{},{},{},{}", current_cash, deal_total, settle_total, (deal_total - settle_total));
        let current_cash = current_cash + (deal_total - settle_total);

        let frozen_capital = item.frozen_capital.to_string().parse::<f64>().unwrap_or_default();
        let trade_frozen_capital = item.trade_frozen_capital.to_string().parse::<f64>().unwrap_or_default();
        let total_asset = current_cash + hold_yk - frozen_capital;
        if total_asset != 0.0 {
            risk_rate = (real_margin + trade_frozen_capital) / total_asset;
        }
        let available_cash = total_asset - real_margin - trade_frozen_capital;
        let net_income = item.total_deposit.checked_sub(item.total_withdraw).unwrap_or_default();
        //计算今日盈亏
        let today_total_value = item.today_total_value.to_string().parse::<f64>().unwrap_or_default();
        let today_deposit = item.today_deposit.to_string().parse::<f64>().unwrap_or_default();
        let today_withdraw = item.today_withdraw.to_string().parse::<f64>().unwrap_or_default();
        let transfer_cash = item.transfer_capital.to_string().parse::<f64>().unwrap_or_default();
        let mut today_yk = total_asset - today_total_value - today_deposit + today_withdraw + frozen_capital - transfer_cash;
        if lastday_frozen_capital_map.contains_key(&item.unit_id) {
            today_yk = today_yk - lastday_frozen_capital_map.get(&item.unit_id).unwrap();
        }

        //计算总盈亏
        let total_deposit = item.total_deposit.to_string().parse::<f64>().unwrap_or_default();
        let total_withdraw = item.total_withdraw.to_string().parse::<f64>().unwrap_or_default();
        let total_transfer_cash = item.total_transfer_capital.to_string().parse::<f64>().unwrap_or_default();
        let total_yk = total_asset - total_deposit + total_withdraw + frozen_capital - total_transfer_cash;
        user_his.total_asset = Decimal::from_f64_retain(total_asset).unwrap_or_default();
        user_his.total_position_value = Decimal::from_f64_retain(tvalue).unwrap_or_default();
        user_his.gem_position_value = Decimal::from_f64_retain(cybtvalue).unwrap_or_default();
        user_his.real_margin = Decimal::from_f64_retain(real_margin).unwrap_or_default();
        user_his.real_cash = item.current_cash;
        user_his.hold_yk = Decimal::from_f64_retain(hold_yk).unwrap_or_default();
        user_his.risk_rate = Decimal::from_f64_retain(risk_rate).unwrap_or_default();
        user_his.available_cash = Decimal::from_f64_retain(available_cash).unwrap_or_default();
        user_his.net_income = net_income;
        user_his.today_yk = Decimal::from_f64_retain(today_yk).unwrap_or_default();
        user_his.total_yk = Decimal::from_f64_retain(total_yk).unwrap_or_default();
        user_his.draw_frozen = item.frozen_capital;
        user_his.trade_frozen_capital = item.trade_frozen_capital;
        user_his.gem_trade_frozen_capital = item.gem_frozen_capital;
        user_his.lastday_cash = item.today_total_value;
        user_his.today_deposit = item.today_deposit;
        user_his.today_withdrawal = item.today_withdraw;
        user_his.frozencash = item.frozen_capital;
        user_his.gem_margin_frozen_capital = Decimal::from_f64_retain(cyb_real_margin).unwrap_or_default();
        //查询当前或者前一天得风控记录,赋值给当前重算的这条风控记录。De
        let his_postion = results!(PhoenixUserAssetsHis::query_ltedate(item.unit_id, ldate, db).await);

        if his_postion.is_some() {
            let his_postion = his_postion.unwrap();
            user_his.warning_line = his_postion.warning_line;
            user_his.level_num = his_postion.level_num;
            user_his.close_line = his_postion.close_line;
            user_his.trade_state = his_postion.trade_state;
        }
        let str = serde_json::to_string(&user_his).unwrap_or_default();
        info!("PhoenixUserAssetsHis data ---:{}", str);
        user_assets_vec.push(user_his);
    }

    //先删除当天的数据
    _ = PhoenixUserAssetsHis::delete_many(ldate, db).await;

    _ = PhoenixUserAssetsHis::insert_many(&user_assets_vec, db).await;
    return Ok(());
}
