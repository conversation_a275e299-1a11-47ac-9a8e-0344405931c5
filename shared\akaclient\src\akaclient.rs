// #![allow(dead_code, unused_imports)]
use dashmap::mapref::one::Ref;
use heartbeatclient::HeartbeatClient;
// use rand::Rng;
use std::time::{Duration, SystemTime, UNIX_EPOCH};

use anyhow::{anyhow, Result};
use chrono::{Local, TimeZone};
use dashmap::DashMap;
use rust_decimal::prelude::ToPrimitive;
use std::sync::Arc;
use tokio::sync::{broadcast, RwLock};
use tracing::*;

use tonic::transport::Channel;

// use messagecenter::notificationclient::NotificationClient;
use protoes::phoenixnotification::*;

use common::constant;
use common::constant::AsyncLock;
use common::logclient::{log_error, LogClient, LogLevel};
use protoes::phoenixakacenter::phoenix_aka_center_client::PhoenixAkaCenterClient;
use protoes::phoenixakacenter::*;

// 汇率信息
#[derive(Debug, <PERSON>lone, Default)]
pub struct Rate {
    pub buy_rate: f64,
    pub sell_rate: f64,
    pub modify_time: i64,
}

// 交易时间
#[derive(Debug, <PERSON>lone, Default)]
pub struct TradeTime {
    pub op_type: i32,
    pub begin: String,
    pub end: String,
}

impl TradeTime {
    fn build(input: &StockTradeTime) -> Vec<TradeTime> {
        // let begintime: i64;
        // let endtime: i64;
        let mut tradetimes: Vec<TradeTime> = vec![];
        input.times.split(",").into_iter().for_each(|v| {
            let timestr: Vec<&str> = v.split("|").collect();
            if timestr.len() != 2 {
                panic!("err");
            }
            // timeutil
            tradetimes.push(TradeTime {
                op_type: input.op_type,
                begin: timestr[0].to_owned(),
                end: timestr[1].to_owned(),
            })
        });

        tradetimes
    }
}

#[derive(Clone)]
pub struct AkaClient {
    // url: String, // logcenter地址
    ccopt: AkaCacheOption,
    akacache: Arc<AkaCache>,
    aka_client: AsyncLock<Option<PhoenixAkaCenterClient<Channel>>>,
    heartbeat: HeartbeatClient,
    shutdown_tx: broadcast::Sender<()>,
    // cache_task_handle: Option<Arc<tokio::task::JoinHandle<()>>>,
    // reconnect_task_handle: Option<Arc<tokio::task::JoinHandle<()>>>,
}

#[derive(Debug, Clone)]
struct AkaCache {
    channels: DashMap<i64, ChannelInfo>,                               // 通道基础信息<通道id, 通道信息>
    markets: DashMap<i64, MarketInfo>,                                 // 市场信息<市场id, 市场信息>
    accounts: DashMap<i64, AccountInfo>,                               // 账户信息<用户id, 账户信息>
    stocks: DashMap<i64, StockInfo>,                                   // 股票基础信息<股票id, 股票信息>
    channel_hold_limit: DashMap<String, (i64, Vec<ChannelHoldLimit>)>, // 股票通道最大持仓量等信息
    stockcodes: DashMap<String, i64>,                                  // 股票 <股票代码, 股票id>
    // spaccounts: DashMap<i64, Vec<AccountInfo>>,
    rates: DashMap<String, ExchangeRate>,                                          // 汇率
    stocktradetimes: DashMap<i64, DashMap<i32, Vec<TradeTime>>>,                   // 股票交易时间
    marktetradetimes: DashMap<i64, DashMap<i32, Vec<TradeTime>>>,                  // 市场交易时间
    trade_dates: DashMap<String, Vec<TradeDateInfo>>,                              // 市场交易日
    fee_settings: DashMap<String, DashMap<i64, DashMap<String, Vec<FeeSetting>>>>, // 市场交易日
    stock_quota: DashMap<String, Option<StockQuota>>,                              // 持仓上下限
}

impl Default for AkaCache {
    fn default() -> Self {
        AkaCache {
            markets: DashMap::new(),
            channels: DashMap::new(),
            accounts: DashMap::new(),
            stocks: DashMap::new(),
            channel_hold_limit: Default::default(),
            stockcodes: DashMap::new(),
            rates: DashMap::new(),
            marktetradetimes: DashMap::new(),
            // spaccounts: DashMap::new(),
            stocktradetimes: DashMap::new(),
            trade_dates: DashMap::new(),
            fee_settings: DashMap::new(),
            stock_quota: DashMap::new(),
        }
    }
}

impl AkaCache {
    pub async fn init(&self, mq_uri: &str, aka_client: &mut PhoenixAkaCenterClient<Channel>) -> Result<()> {
        if mq_uri == "" {
            error!("启用缓存时,消息中心配置不能为空");
            return Err(anyhow!("启用缓存时,消息中心配置不能为空"));
        }
        info!("启用缓存，开始初始化缓存信息>>>>>>>>>>>>");
        let channelreq = ChannelInfoReq { channel_id: 0 };
        let channelresponse = aka_client.query_channel_info(channelreq).await;
        if channelresponse.is_err() {
            error!("init channel error");
            return Err(anyhow!("init channel error"));
        }
        channelresponse.unwrap().into_inner().data.into_iter().for_each(|v| {
            self.channels.insert(v.channel_id, v);
        });
        // reloadopt.channeldata_reload = false;
        //   // 股票信息
        //   let stockinforeq = StockInfoReq {
        //     stock_id: 0,
        //     exchange_id: 0,
        //     stock_code: "".to_owned(),
        //   };
        //   let stockinforesp = client.query_stock_info(stockinforeq).await.expect("init stockinfo error").into_inner();
        //   stockinforesp.data.into_iter().for_each(|v| {
        //     stocks.cache_set(v.stock_id, v);
        //   });

        // 账户信息
        //   let accountinforeq = AccountInfoReq { unit_id: 0 };
        //   let accountinforesp = client.query_account_info(accountinforeq).await.expect("init accountinfo error").into_inner();
        //   accountinforesp.data.into_iter().for_each(|v| {
        //     accounts.insert(v.unit_id, v);
        //   });

        //
        // 市场信息
        let marketinforeq = MarketInfoReq { market_id: 0 };
        let marketinforesp = aka_client.query_market_info(marketinforeq).await;
        if marketinforesp.is_err() {
            error!("init marketinfo error");
            return Err(anyhow!("init marketinfo error"));
        }
        let marketinforesp = marketinforesp.unwrap().into_inner();
        marketinforesp.data.clone().into_iter().for_each(|v| {
            self.markets.insert(v.market_id, v);
        });

        // 市场交易时间信息
        for m in marketinforesp.data {
            let tradetimereq = StockTradeTimeReq { stock_id: 0, exchange_id: m.market_id };
            let marketinforesp = aka_client.query_stock_trade_time(tradetimereq).await;
            if marketinforesp.is_err() {
                let errormsg = marketinforesp.unwrap_err().to_string();
                error!("init marktetradetimes req:{:?} error:{}", tradetimereq, &errormsg);
                return Err(anyhow!("init marktetradetimes req:{:?} error:{}", tradetimereq, &errormsg));
            }

            let buyselltimes: DashMap<i32, Vec<TradeTime>> = DashMap::new();
            let mut buytimes: Vec<TradeTime> = vec![];
            let mut selltimes: Vec<TradeTime> = vec![];
            marketinforesp.unwrap().into_inner().tradetimes.into_iter().for_each(|v| match v.op_type {
                1 => buytimes = TradeTime::build(&v),
                2 => selltimes = TradeTime::build(&v),
                _ => error!("unknow op_type:"),
            });
            if buytimes.is_empty() || selltimes.is_empty() {
                error!("market: {:?} trade time is empty", m.market_id);
                return Err(anyhow!("market: {:?} trade time is empty", m.market_id));
            }
            buyselltimes.insert(1, buytimes);
            buyselltimes.insert(2, selltimes);
            self.marktetradetimes.insert(m.market_id, buyselltimes);
        }
        info!("启用缓存，初始化缓存信息完成<<<<<<<<<<<<<<<<");
        Ok(())
    }
}

// akaclient缓存配置参数
#[derive(Debug, Clone)]
pub struct AkaCacheOption {
    pub use_cache: bool, // 是否缓存
    pub mq_uri: String,  // 消息中心uri, 格式:amqp://pp:<EMAIL>:5672/%2f
    pub exchange: String,
    pub routing_keys: String,
}
impl Default for AkaCacheOption {
    fn default() -> AkaCacheOption {
        AkaCacheOption {
            use_cache: false,
            mq_uri: "".to_owned(),
            exchange: "".to_owned(),
            routing_keys: "".to_owned(),
        }
    }
}

impl AkaClient {
    pub async fn init(url: &str, ccopt: &AkaCacheOption, client_id: &str, service_name: &str) -> Self {
        //初始化时，可以通过全部从后台获取数据进行缓存
        let akacache = Arc::new(AkaCache::default());

        // Create shutdown channel for graceful shutdown coordination
        let (shutdown_tx, _shutdown_rx) = broadcast::channel::<()>(1);

        let _innnercache = akacache.clone();
        let mqopt = ccopt.clone();
        let shutdown_rx_cache = shutdown_tx.subscribe();

        // let _cache_task_handle =
        if mqopt.use_cache {
            tokio::spawn(async move {
                let mut shutdown_rx = shutdown_rx_cache;
                let (tx_notification, mut rx_notification) = tokio::sync::mpsc::channel::<NotificationMessage>(1024);

                // let routingkey = "notification.aka.#";
                let queuename = format!("akaclient-{}", utility::timeutil::current_timestamp());
                // info!("akacache mq queue:{}", queuename);

                if mqopt.exchange.is_empty() || mqopt.routing_keys.is_empty() || mqopt.mq_uri.is_empty() {
                    panic!("未传入有效的mq订阅信息,exchange: {:?}, routing_keys: {:?}, mq_uri: {:?}", &mqopt.exchange, &mqopt.routing_keys, &mqopt.mq_uri);
                }

                // let notification_client = Arc::new(RwLock::new(
                //     NotificationClient::new(&mqopt.exchange, &queuename.to_owned(), mqopt.routing_keys, &mqopt.mq_uri, tx_notification).await,
                // ));

                // messagecenter::init::init_notification_client(notification_client.clone()).await;
                // messagecenter::init::init_notification_service(notification_client).await;
                let routing_keys = mqopt.routing_keys.split(",").map(|s| s.to_string()).collect::<Vec<String>>();
                if let Err(e) = messagecenter::NotificationClientV2::init_with_auto_management(&mqopt.exchange, &queuename, routing_keys, &mqopt.mq_uri, tx_notification).await {
                    error!("init aka cache notification client error: {:?}", e);
                    log_error(&format!("init aka cache notification client error: {:?}", e)).await;
                }

                loop {
                    // info!("akacache mq loop");
                    tokio::select! {
                        notification = rx_notification.recv() => {
                            if let Some(message) = notification {
                                let msg = message.msg_body.to_owned();
                                if msg.as_ref().is_none() {
                                    info!("msg_body is none");
                                    continue;
                                }
                                let msg = msg.unwrap();

                                // TODO:针对具体的key重置
                                match message.msg_type() {
                                    // 时间类消息: 交易时间,休市时间
                                    NotificationType::TimeChanged => {
                                        // info!("akacache clean tradetime cache");
                                        // stocktradetimes: 直接清空
                                        // marktetradetimes: 重新加载
                                        if let Some(datetimeinfo) = msg.msg_datetime {
                                            info!("akacache clean datetime({:?}) cache", &datetimeinfo);
                                            if datetimeinfo.stock_id == 0 {
                                                // _innnercache.marktetradetimes.clear();
                                                _innnercache.marktetradetimes.remove(&datetimeinfo.market_id);

                                            } else {
                                                // _innnercache.stocktradetimes.clear();
                                                _innnercache.stocktradetimes.remove(&datetimeinfo.stock_id);
                                            }
                                        } else {
                                            info!("akacache recv TimeChanged, msg is none");
                                        }
                                    },
                                    // 通道基础信息
                                    NotificationType::ChannelInfoChanged => {
                                        // info!("akacache clean channels cache");
                                        // _innnercache.channels.clear();
                                        if let Some(channelinfo) = msg.msg_channelinfo {
                                            info!("akacache clean channel({}) cache", &channelinfo.channel_id);
                                            _innnercache.channels.remove(&channelinfo.channel_id);
                                            _innnercache.channel_hold_limit.clear();
                                        } else {
                                            info!("akacache recv ChannelInfoChanged, msg is none");
                                        }
                                    },
                                    // 股票基本信息(含保证金比例)
                                    NotificationType::StockInfoChanged => {
                                        // info!("akacache clean stocks cache");
                                        // _innnercache.stocks.clear();
                                        if let Some(stockinfo) = msg.msg_stockinfo {
                                            // info!("akacache clean stock({}) cache", &stockinfo.stock_id);
                                            _innnercache.stocks.remove( &stockinfo.stock_id);
                                            _innnercache.channel_hold_limit.clear();
                                        } else {
                                            info!("akacache recv StockInfoChanged, msg is none");
                                        }
                                    },
                                    // 汇率
                                    NotificationType::ExchangeRateChanged => {
                                        // info!("akacache clean rates cache");
                                        _innnercache.rates.clear();
                                        // if let Some(exchangerate) = msg.msg_exchangerate {
                                        //   info!("akacache clean currency({}) cache", &exchangerate.currency);
                                        //   _innnercache.rates.remove( &exchangerate.currency);
                                        // } else {
                                        //   info!("akacache recv ExchangeRateChanged, msg is none");
                                        // }
                                    },
                                    // 账户基础信息(含融资合同信息,预警线,平仓线,交易状态,创业板限制)
                                    NotificationType::AccountInfoChanged => {
                                        // info!("akacache clean accounts cache");
                                        // _innnercache.accounts.clear();
                                        if let Some(accountinfo) = msg.msg_accountinfo {
                                            info!("akacache clean account({}) cache", &accountinfo.user_id);
                                            _innnercache.accounts.remove( &accountinfo.user_id);
                                        } else {
                                            info!("akacache recv AccountInfoChanged, msg is none");
                                        }
                                    },
                                    // 结算后清空交易日信息
                                    NotificationType::Settlement => {
                                        // info!("akacache clean trade_dates cache");
                                        _innnercache.trade_dates.clear();
                                    },
                                    // 费用变更
                                    NotificationType::FeeSettingChanged => {
                                        // info!("akacache clean fee_settings cache");
                                        _innnercache.fee_settings.clear();
                                    },
                                    // 持仓上下限变更
                                    NotificationType::UserStockMarginChanged => {
                                        // info!("akacache clean stock_quota cache");
                                        _innnercache.stock_quota.clear();
                                    },
                                    _=> { /*info!("unhandled message:{:?}", message);*/ }
                                }
                            // } else {
                            //     error!("message body is empty");
                            // }
                            } else {
                                error!("empty notification message...");
                            }
                        }
                        _ = shutdown_rx.recv() => {
                            info!("AkaClient cache task received shutdown signal");
                            break;
                        }
                    }
                }
                info!("AkaClient cache task has exited");
            });
        }

        let heartbeat = HeartbeatClient::new(url, client_id.to_string(), service_name.to_string(), true).await;

        let aka_client = Arc::new(RwLock::new(None::<PhoenixAkaCenterClient<Channel>>));
        if let Ok(channel) = tonic::transport::Channel::from_shared(url.to_string()) {
            if let Ok(channel) = channel.connect().await {
                let mut proto_client = PhoenixAkaCenterClient::new(channel);
                if ccopt.use_cache {
                    if let Err(e) = akacache.init(&ccopt.mq_uri, &mut proto_client).await {
                        error!("{}", e);
                    }
                }
                let mut wr = aka_client.write().await;
                *wr = Some(proto_client);
            } else {
                log_error(&format!("connect to AkaCenter failed: {:?}", url)).await;
            }
        }

        let client_clone = aka_client.clone();
        let endpoint_owned = url.to_string();
        let service_name_owned = service_name.to_string();
        let heartbeat_clone = heartbeat.clone();

        let use_cache = ccopt.use_cache;
        let mq_uri = ccopt.mq_uri.clone();
        let innnercache = akacache.clone();
        let shutdown_rx_reconnect = shutdown_tx.subscribe();

        // let _reconnect_task_handle = Some(
        tokio::spawn(async move {
            let mut shutdown_rx = shutdown_rx_reconnect;
            let retry_interval = 10;
            loop {
                tokio::select! {
                    _ = async {
                        if client_clone.read().await.is_none() {
                            info!(service = %service_name_owned, endpoint = %endpoint_owned, "Attempting to connect Service");
                            match tonic::transport::Channel::from_shared(endpoint_owned.clone()) {
                                Ok(channel) => match channel.connect().await {
                                    Ok(channel) => {
                                        {
                                            let mut proto_client = PhoenixAkaCenterClient::new(channel);
                                            info!("是否开启缓存:{}", use_cache);
                                            ////缓存开启
                                            if use_cache {
                                                if let Err(e) = innnercache.init(&mq_uri, &mut proto_client).await {
                                                    error!("{}", e);
                                                }
                                            }
                                            let mut wr = client_clone.write().await;
                                            *wr = Some(proto_client);
                                            info!(service = %service_name_owned, "Service connected");
                                        }
                                        // 等待 HeartbeatClient 報告不健康，然後重試
                                        while heartbeat_clone.is_healthy() {
                                            tokio::time::sleep(Duration::from_secs(1)).await;
                                        }
                                        {
                                            let mut wr = client_clone.write().await;
                                            *wr = None::<PhoenixAkaCenterClient<tonic::transport::Channel>>;
                                            error!(service = %service_name_owned, "Service client disconnected due to unhealthy state");
                                        }
                                    }
                                    Err(e) => {
                                        error!(service = %service_name_owned, "Service client connection failed: {}", e);
                                    }
                                },
                                Err(e) => {
                                    error!(service = %service_name_owned, "Invalid endpoint: {}", e);
                                }
                            }
                            warn!(service = %service_name_owned, "Retrying Service client connection in {retry_interval} seconds");
                            tokio::time::sleep(Duration::from_secs(retry_interval)).await;
                        }
                    } => {}
                    _ = shutdown_rx.recv() => {
                        info!("AkaClient reconnect task received shutdown signal");
                        break;
                    }
                }
            }
            info!("AkaClient reconnect task has exited");
        });

        Self {
            // url,
            ccopt: ccopt.clone(),
            // reloadopt,
            akacache,
            aka_client,
            heartbeat,
            shutdown_tx,
            // cache_task_handle: cache_task_handle.map(|h| Arc::new(h)),
            // reconnect_task_handle: reconnect_task_handle.map(|h| Arc::new(h)),
        }
    }

    /// Gracefully shutdown the AkaClient and all background tasks
    pub async fn shutdown(&self) {
        info!("AkaClient: 开始关闭akaclient");

        self.heartbeat.stop();

        // Send shutdown signal to all background tasks
        if let Err(_) = self.shutdown_tx.send(()) {
            warn!("AkaClient: Failed to send shutdown signal - receivers may already be closed");
        }

        // Note: Since we can't access the task handles from a &self reference,
        // we rely on the shutdown signal to coordinate graceful shutdown.
        // In a real implementation, you might want to store the handles
        // in a way that allows waiting for them.

        // Give background tasks time to complete
        tokio::time::sleep(Duration::from_millis(100)).await;

        // Close the gRPC client connection
        {
            let mut client = self.aka_client.write().await;
            if client.is_some() {
                info!("AkaClient: Closing gRPC connection");
                *client = None;
            }
        }

        info!("AkaClient: 成功关闭akaclient");
    }

    pub async fn check_client(&self) -> Result<AsyncLock<Option<PhoenixAkaCenterClient<Channel>>>> {
        if !self.heartbeat.is_healthy() {
            error!("{} is unhealthy, please try again later", self.heartbeat.service_name);
            log_error(&format!("{} is unhealthy, please try again later", self.heartbeat.service_name)).await;
            return Err(anyhow!("akacenter未连接"));
        }
        if self.aka_client.read().await.is_none() {
            if let Ok(log_client) = LogClient::get() {
                log_client.push(LogLevel::Error, "AkaCenter is not connected").await;
            }
            return Err(anyhow!("AkaCenter is not connected"));
        }

        Ok(self.aka_client.clone())
    }

    //股票通道最大持仓量等信息
    pub async fn query_channel_hold_limit(&self, stock_id: i64, channel_id: i64) -> Result<(i64, Vec<ChannelHoldLimit>)> {
        // debug!("query_channel_hold_limit stock_id:{:?} channel_id:{:?}", stock_id, channel_id);

        let key = format!("{}_{}", stock_id, channel_id);
        match self.akacache.channel_hold_limit.get(&key) {
            None => {
                let client = self.check_client().await?;
                let response = client.write().await.as_mut().unwrap().query_channel_hold_limit(ChannelHoldLimitReq { stock_id, channel_id }).await;
                if response.as_ref().is_err() {
                    error!("failed:{:?}", response.unwrap_err());
                    return Err(anyhow!("failed"));
                }
                let resp = response.unwrap().into_inner();
                if resp.ret_code != 0 {
                    error!("query_stock_channel failed: code:{:?} msg:{:?}", resp.ret_code, resp.ret_msg);
                    return Err(anyhow!("failed"));
                }

                self.akacache.channel_hold_limit.insert(key, (resp.total_max_hold, resp.data.clone()));

                Ok((resp.total_max_hold, resp.data))
            }
            Some(channel_hold_limit) => Ok(channel_hold_limit.to_owned()),
        }
    }

    //通道基础信息
    pub async fn query_channel_info(&self, channel_id: i64) -> Result<ChannelInfo> {
        // debug!("query_channel_info {:?}", channel_id);
        if channel_id == constant::VALUE_ALL {
            return Err(anyhow!("error"));
        }
        let request = ChannelInfoReq { channel_id };

        let channelinfo = self.akacache.channels.get(&request.channel_id);

        if channelinfo.is_none() {
            let client = self.check_client().await?;
            let response = client.write().await.as_mut().unwrap().query_channel_info(request.to_owned()).await;

            if response.as_ref().is_err() {
                error!("failed:{:?}", response.unwrap_err());
                return Err(anyhow!("failed"));
            }
            let ret = response.unwrap().into_inner();
            if ret.ret_code != 0 {
                error!("query_channel_info failed: code:{:?} msg:{:?}", ret.ret_code, ret.ret_msg);
                return Err(anyhow!("failed"));
            }

            ret.data.into_iter().for_each(|data| {
                self.akacache.channels.insert(data.channel_id, data);
            });
        }

        let value = self.akacache.channels.get(&request.channel_id);
        if value.as_ref().is_none() {
            return Err(anyhow!("can't find channel info by id:{}", &request.channel_id));
        }
        Ok(value.unwrap().to_owned())
    }

    //查全部通道基础信息
    pub async fn query_all_channel_info(&self) -> Result<Vec<ChannelInfo>> {
        // debug!("query_all_channel_info ");

        let client = self.check_client().await?;
        let request = ChannelInfoReq { channel_id: 0 };

        let response = client.write().await.as_mut().unwrap().query_channel_info(request.to_owned()).await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }
        let ret = response.unwrap().into_inner();
        if ret.ret_code != 0 {
            error!("query_all_channel_info failed: code:{:?} msg:{:?}", ret.ret_code, ret.ret_msg);
            return Err(anyhow!("failed"));
        }
        self.akacache.channels.clear();

        ret.data.into_iter().for_each(|data| {
            self.akacache.channels.insert(data.channel_id, data);
        });

        let res: Vec<ChannelInfo> = self.akacache.channels.iter().map(|val| async_global_executor::block_on(async { val.to_owned() })).collect();
        Ok(res)
    }

    pub fn query_currency_by_exchangeid(&self, exchangeid: i64) -> String {
        let ret = match exchangeid {
            101 | 102 => "CNY".to_string(),
            103 => "HKD".to_string(),
            _ => "".to_string(),
        };
        ret
    }

    pub fn query_exchangeid_by_currency(&self, currency: i32) -> Vec<i64> {
        match currency {
            c if c == Currency::Hkd as i32 => vec![103],
            c if c == Currency::Cny as i32 => vec![101, 102],
            c if c == Currency::Usd as i32 => vec![],
            c if c == Currency::Cnh as i32 => vec![],
            _ => vec![],
        }
    }

    //股票通道配置优先级信息
    pub async fn query_stock_channel(&self, request: StockChannelReq) -> Result<Vec<ChannelConfig>> {
        // debug!("query_stock_channel request: {:?}", request);

        let client = self.check_client().await?;
        let response = client.write().await.as_mut().unwrap().query_stock_channel(request).await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }
        let resp = response.unwrap().into_inner();
        if resp.ret_code != 0 {
            error!("query_stock_channel: code:{:?} msg:{:?}", resp.ret_code, resp.ret_msg);
            return Ok(vec![]);
        }
        Ok(resp.data)
    }

    //
    pub async fn query_stock_info_by_code(&self, stock_code: &str, market_code: &str) -> Option<StockInfo> {
        // debug!("query_stock_info_by_code  stock_code:{:?}  market_code:{:?}", stock_code, market_code);
        if stock_code.is_empty() || market_code.is_empty() {
            error!("stock_code or market_code is empty");
            return None;
        }

        let market = self.query_market_info_by_code(market_code).await;
        if market.as_ref().is_none() {
            error!("query market_id by code:{:?} error", market_code);
            return None;
        }
        let market_id = market.unwrap().market_id;
        let mut findflag: bool = false;
        let stockcode = format!("{}_{}", stock_code, market_code);

        let mut stock_id: i64 = 0;
        {
            let stockid = self.akacache.stockcodes.get(&stockcode);
            if !stockid.is_none() {
                findflag = true;
                stock_id = stockid.unwrap().to_owned();
            }
        };

        if !findflag {
            let request = StockInfoReq {
                stock_id: -1,
                exchange_id: market_id,
                stock_code: stock_code.to_owned(),
            };
            let ret = self.check_client().await;
            if ret.is_err() {
                error!("{}", ret.unwrap_err());
                return None;
            }
            let client = ret.unwrap();
            let response = client.write().await.as_mut().unwrap().query_stock_info(request).await;
            if response.as_ref().is_err() {
                error!("failed:{:?}", response.unwrap_err());
                return None;
            }

            let ret = response.unwrap().into_inner();
            if ret.ret_code != 0 {
                error!("query_stock_channel failed: code:{:?} msg:{:?}", ret.ret_code, ret.ret_msg);
                return None;
            }

            {
                ret.data.into_iter().for_each(|data| {
                    self.akacache.stocks.insert(data.stock_id, data.to_owned());
                    self.akacache.stockcodes.insert(stockcode.clone(), data.stock_id);
                });
                let stockid = self.akacache.stockcodes.get(&stockcode);
                if stockid.is_none() {
                    error!("query query_stock_info_by_code get none");
                    return None;
                }
                stock_id = stockid.unwrap().to_owned();
                let stockinfo = self.akacache.stocks.get(&stock_id);
                if stockinfo.is_none() {
                    error!("query query_stock_info_by_code get none");
                    return None;
                }
                return Some(stockinfo.unwrap().to_owned());
            }
        }
        let stockinfo = self.query_stock_info(stock_id).await;
        if stockinfo.as_ref().is_err() {
            error!("query query_stock_info_by_code get none");
            None
        } else {
            let stockinfo = stockinfo.unwrap();
            if stockinfo.is_none() {
                None
            } else {
                Some(stockinfo.unwrap())
            }
        }
    }

    //股票基础信息以及交易时间段
    pub async fn query_stock_info(&self, stock_id: i64) -> Result<Option<StockInfo>> {
        // info!("query_stock_info  stock_id:{:?}", stock_id);
        if stock_id == constant::VALUE_ALL {
            return Err(anyhow!("error"));
        }

        let request = StockInfoReq {
            stock_id,
            exchange_id: 0,
            stock_code: "".to_owned(),
        };

        let mut findflag: bool = false;
        let mut value = StockInfo { ..Default::default() };
        {
            let stockinfo = self.akacache.stocks.get(&stock_id);
            if !stockinfo.is_none() {
                // return Ok(stockinfo.unwrap().to_owned());
                findflag = true;
                value = stockinfo.unwrap().to_owned();
            }
        };

        if !findflag {
            let client = self.check_client().await?;
            //   let client = PhoenixAkaCenterClient::connect(self.url.clone()).await;
            let response = client.write().await.as_mut().unwrap().query_stock_info(request).await;
            if response.as_ref().is_err() {
                error!("failed:{:?}", response.unwrap_err());
                return Err(anyhow!("failed"));
            }

            let ret = response.unwrap().into_inner();
            if ret.ret_code != 0 {
                error!("query_stock_info failed: code:{:?} msg:{:?}", ret.ret_code, ret.ret_msg);
                return Ok(None);
            }

            {
                ret.data.into_iter().for_each(|data| {
                    self.akacache.stocks.insert(data.stock_id, data);
                });
                let stockinfo = self.akacache.stocks.get(&stock_id);
                if stockinfo.is_none() {
                    return Err(anyhow!("failed"));
                }
                value = stockinfo.unwrap().to_owned();
            }
        }
        // info!("stock info:{:?}", &value);
        Ok(Some(value))
    }

    //删除stock_info 缓存信息
    pub async fn delete_stock_info_cache(&self, stock_id: i64) -> Result<()> {
        self.akacache.stocks.remove(&stock_id);
        Ok(())
    }

    //全部交易对手方账号和特殊账号信息
    pub async fn query_special_account(&self, account_type: i32) -> Result<Vec<SpecialAccount>> {
        // debug!("query_special_account  account_type:{:?}", account_type);

        let client = self.check_client().await?;
        let response = client.write().await.as_mut().unwrap().query_special_account(SpecialAccountInfoReq { account_type }).await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }
        let resp = response.unwrap().into_inner();
        if resp.ret_code != 0 {
            error!("query_special_account failed: code:{:?} msg:{:?}", resp.ret_code, resp.ret_msg);
            return Err(anyhow!("failed"));
        }
        Ok(resp.accounts)
    }

    //市场信息以及交易日信息
    pub async fn query_market_info(&self, market_id: i64) -> Result<MarketInfo> {
        // debug!("query_market_info market_id:{:?}", market_id);
        if market_id == constant::VALUE_ALL {
            return Err(anyhow!("error"));
        }
        let request = MarketInfoReq { market_id };
        //优先从缓存取
        let marketinfo = self.akacache.markets.get(&request.market_id);
        if marketinfo.is_none() {
            let client = self.check_client().await?;
            let response = client.write().await.as_mut().unwrap().query_market_info(request.to_owned()).await;
            // client_wr.downgrade();

            if response.as_ref().is_err() {
                error!("failed:{:?}", response.unwrap_err());
                return Err(anyhow!("failed"));
            }
            let ret = response.unwrap().into_inner();
            if ret.ret_code != 0 {
                error!("query_market_info failed: code:{:?} msg:{:?}", ret.ret_code, ret.ret_msg);
                return Err(anyhow!("failed"));
            }

            ret.data.into_iter().for_each(|data| {
                self.akacache.markets.insert(data.market_id, data);
            });
        }

        let value = self.akacache.markets.get(&request.market_id);
        if value.as_ref().is_none() {
            return Err(anyhow!("can't find market info by id:{}", &request.market_id));
        }
        Ok(value.unwrap().to_owned())
    }

    //根据市场代码查询 市场信息以及交易日信息
    pub async fn query_market_info_by_code(&self, market_code: &str) -> Option<MarketInfo> {
        // debug!("query_market_info_by_code market_code:{:?}", market_code);

        if !self.ccopt.use_cache {
            let ret = self.check_client().await;
            if ret.is_err() {
                error!("{}", ret.unwrap_err());
                return None;
            }
            let client = ret.unwrap();
            let response = client.write().await.as_mut().unwrap().query_market_info(MarketInfoReq { market_id: 0 }).await;
            if response.as_ref().is_err() {
                error!("failed:{:?}", response.unwrap_err());
                return None;
            }
            let ret = response.unwrap().into_inner();
            if ret.ret_code != 0 {
                error!("query_market_info_by_code failed: code:{:?} msg:{:?}", ret.ret_code, ret.ret_msg);
                return None;
            }

            self.akacache.markets.clear();

            ret.data.into_iter().for_each(|data| {
                self.akacache.markets.insert(data.market_id, data);
            });
        }
        let res = self.akacache.markets.iter().find(|m| m.market_code == market_code);
        if res.is_none() {
            return None;
        }
        Some(res.unwrap().to_owned())
    }

    //查询全部市场信息
    pub async fn query_all_market_info(&self, _market_id: i64) -> Result<Vec<MarketInfo>> {
        // debug!("query_all_market_info ");

        let client = self.check_client().await?;
        let request = MarketInfoReq { market_id: 0 };

        let response = client.write().await.as_mut().unwrap().query_market_info(request.to_owned()).await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }
        let ret = response.unwrap().into_inner();
        if ret.ret_code != 0 {
            error!("query_all_market_info failed: code:{:?} msg:{:?}", ret.ret_code, ret.ret_msg);
            return Err(anyhow!("failed"));
        }

        self.akacache.markets.clear();

        ret.data.into_iter().for_each(|data| {
            self.akacache.markets.insert(data.market_id, data);
        });

        let res: Vec<MarketInfo> = self.akacache.markets.iter().map(|val| async_global_executor::block_on(async { val.to_owned() })).collect();
        Ok(res)
    }

    //临时休市信息
    pub async fn query_market_close_info(&self) -> Result<Vec<MarketCloseInfo>> {
        // debug!("query_market_close_info");

        let client = self.check_client().await?;

        let response = client.write().await.as_mut().unwrap().query_market_close_info(MarketCloseInfoReq {}).await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }
        let resp = response.unwrap().into_inner();
        if resp.ret_code != 0 {
            error!("query_market_close_info failed: code:{:?} msg:{:?}", resp.ret_code, resp.ret_msg);
            return Err(anyhow!("failed"));
        }
        Ok(resp.data)
    }

    // 汇率查询
    pub async fn query_exchange_rate(&self, currency: &str, base_currency: &str) -> Result<Rate> {
        // info!("query_exchange_rate currency:{:?} -> {:?}", base_currency, currency);
        let mut req = ExchangeRateReq::default();

        if currency.eq(base_currency) {
            return Ok(Rate {
                buy_rate: 1.0,
                sell_rate: 1.0,
                modify_time: Local::now().timestamp(),
            });
        } else {
            let f_str = |currency: &str| -> Result<i32> {
                let c = match currency.to_uppercase().as_str() {
                    "HKD" => Currency::Hkd as i32,
                    "CNY" => Currency::Cny as i32,
                    "USD" => Currency::Usd as i32,
                    "CNH" => Currency::Cnh as i32,
                    &_ => return Err(anyhow!("unknow currency type")),
                };

                Ok(c)
            };

            req.currency = f_str(currency)?;
            req.base_currency = f_str(base_currency)?;
        }

        if self.ccopt.use_cache {
            let exch_rate = self.akacache.rates.get(&format!("{}_{}", &req.currency, &req.base_currency));
            if exch_rate.is_some() {
                // info!("query_exchange_rate find in cache");

                let rate = exch_rate.unwrap();
                return Ok(Rate {
                    buy_rate: rate.buy_rate,
                    sell_rate: rate.sell_rate,
                    modify_time: rate.modify_time,
                });
            }
        };

        // info!("query_exchange_rate query from server");

        let client = self.check_client().await?;
        let response = client.write().await.as_mut().unwrap().query_exchange_rate(req.to_owned()).await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }

        let ret = response.unwrap().into_inner();
        if ret.ret_code != 0 {
            error!("query_exchange_rate failed: code:{:?} msg:{:?}", ret.ret_code, ret.ret_msg);
            return Err(anyhow!("failed"));
        }

        let value = ret.data.ok_or(anyhow!("no rate"))?;

        self.akacache.rates.insert(format!("{}_{}", &req.currency, &req.base_currency), value.to_owned());

        Ok(Rate {
            buy_rate: value.buy_rate,
            sell_rate: value.sell_rate,
            modify_time: value.modify_time,
        })
    }

    // 账户信息查询
    pub async fn query_account_info(&self, user_id: i64) -> Result<AccountInfo> {
        // debug!("query_account_info user_id:{:?}", user_id);
        if user_id == constant::VALUE_ALL {
            return Err(anyhow!("error"));
        }
        let request = AccountInfoReq { user_id };

        let mut findflag: bool = false;
        let mut value = AccountInfo { ..Default::default() };
        {
            let accountinfo = self.akacache.accounts.get(&user_id);
            if !accountinfo.is_none() {
                // return Ok(accountinfo.unwrap().to_owned());
                findflag = true;
                value = accountinfo.unwrap().to_owned();
            }
        };

        if !findflag {
            let client = self.check_client().await?;
            let response = client.write().await.as_mut().unwrap().query_account_info(request).await;
            if response.as_ref().is_err() {
                error!("failed:{:?}", response.as_ref().unwrap_err());
                return Err(anyhow!("failed:{:?}", response.unwrap_err()));
            }

            let ret = response.unwrap().into_inner();
            if ret.ret_code != 0 {
                // error!("query_account_info failed: code:{:?} msg:{:?}", ret.ret_code, ret.ret_msg);
                return Err(anyhow!("failed"));
            }

            {
                ret.data.into_iter().for_each(|data| {
                    self.akacache.accounts.insert(data.user_id, data);
                });
                let accountinfo = self.akacache.accounts.get(&user_id);
                if accountinfo.is_none() {
                    return Err(anyhow!("failed"));
                }
                value = accountinfo.unwrap().to_owned();
            }
        }

        Ok(value)
    }

    //删除用户缓存
    pub async fn delete_account_info_cache(&self, user_id: i64) -> Result<()> {
        _ = self.akacache.accounts.remove(&user_id);
        Ok(())
    }

    // 查询全部账户信息查询
    pub async fn query_all_account_info(&self) -> Result<Vec<AccountInfo>> {
        // debug!("query_all_account_info",);

        let client = self.check_client().await?;
        let response = client.write().await.as_mut().unwrap().query_account_info(AccountInfoReq { user_id: 0 }).await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }
        let resp = response.unwrap().into_inner();
        if resp.ret_code != 0 {
            error!("query_all_account_info failed: code:{:?} msg:{:?}", resp.ret_code, resp.ret_msg);
            return Err(anyhow!("failed"));
        }
        Ok(resp.data)
    }

    // 交易日信息查询
    pub async fn query_trade_date(&self, market_id: i64, query_date: i32, query_type: i32, date_offset: i32) -> Result<Vec<TradeDateInfo>> {
        // debug!("query_trade_date market_id:{:?} query_date:{:?} query_type:{:?} date_offset:{:?}", market_id, query_date, query_type, date_offset);

        let key = format!("{}_{}_{}_{}", market_id, query_date, query_type, date_offset);

        if market_id != 0 && self.ccopt.use_cache {
            if let Some(value) = self.akacache.trade_dates.get(&key) {
                info!("缓存中读取到交易日信息:{:?}", value.value());
                return Ok(value.value().to_vec());
            }
        }

        let client = self.check_client().await?;
        let response = client
            .write()
            .await
            .as_mut()
            .unwrap()
            .query_trade_date(TradeDateReq {
                market_id,
                query_date,
                query_type,
                date_offset,
            })
            .await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }

        let resp = response.unwrap().into_inner();
        if resp.ret_code != 0 {
            error!("query_trade_date failed: code:{:?} msg:{:?}", resp.ret_code, resp.ret_msg);
            return Err(anyhow!("failed"));
        }

        if market_id != 0 && self.ccopt.use_cache {
            info!("将交易日信息加入缓存:{:?}", &resp.data);
            self.akacache.trade_dates.insert(key, resp.data.clone());
        }

        Ok(resp.data)
    }

    //查询用户品种的保证金
    pub async fn query_unit_stock_margin(&self, user_id: i64, stock_id: i64) -> Result<f64> {
        // debug!("query_unit_stock_margin user_id:{:?} stock_id:{:?}", user_id, stock_id);

        let client = self.check_client().await?;
        let response = client.write().await.as_mut().unwrap().query_unit_stock_margin(UserStockMarginReq { user_id, stock_id }).await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }
        let resp = response.unwrap().into_inner();
        if resp.ret_code != 0 {
            error!("query_unit_stock_margin failed: code:{:?} msg:{:?}", resp.ret_code, resp.ret_msg);
            return Err(anyhow!("failed"));
        }
        Ok(resp.margin_rate)
    }

    //查询用户品种的持仓上下限
    pub async fn query_unit_stock_quota(&self, user_id: i64, stock_id: i64) -> Result<Option<StockQuota>> {
        // debug!("query_unit_stock_quota user_id:{:?} stock_id:{:?}", user_id, stock_id);

        // 优先从缓存中获取
        let key = format!("{}_{}", user_id, stock_id);
        if self.ccopt.use_cache {
            let stock_quota = self.akacache.stock_quota.get(&key);
            if !stock_quota.is_none() {
                let value = stock_quota.unwrap().to_owned();
                info!("缓存中读取到持仓上下限信息:{:?}", &value);
                return Ok(value);
            }
        };

        let client = self.check_client().await?;
        let response = client.write().await.as_mut().unwrap().query_stock_channel_quota(StockQuotaReq { user_id, stock_id }).await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }
        let resp = response.unwrap().into_inner();
        if resp.ret_code != 0 {
            error!("query_unit_stock_margin failed: code:{:?} msg:{:?}", resp.ret_code, resp.ret_msg);
            return Err(anyhow!("failed"));
        }

        if self.ccopt.use_cache {
            self.akacache.stock_quota.entry(key).and_modify(|value| *value = resp.stock_quota.clone()).or_insert(resp.stock_quota.clone());
        }

        Ok(resp.stock_quota.clone())
    }

    //查询品种通道保证金比例
    pub async fn query_stock_channel_margin(&self, stock_id: i64, channel_id: i64) -> Result<f64> {
        // debug!("query_stock_channel_margin stock_id:{:?} channel_id:{:?}", stock_id, channel_id,);

        let client = self.check_client().await?;
        let response = client.write().await.as_mut().unwrap().query_stock_channel_margin(StockMarginReq { stock_id, channel_id }).await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }
        let resp = response.unwrap().into_inner();
        if resp.ret_code != 0 {
            error!("query_stock_channel_margin failed: code:{:?} msg:{:?}", resp.ret_code, resp.ret_msg);
            return Err(anyhow!("failed"));
        }
        Ok(resp.stock_margin.unwrap_or(StockMargin { ..Default::default() }).margin_rate)
    }

    // 查询费用设置
    pub async fn query_fee_setting(&self, fee_type: String, exchange_id: i64, order_direction: i32, user_id: i64, channel_id: i64, stock_type: i32) -> Result<Vec<FeeSetting>> {
        // debug!(
        //     "query_fee_setting fee_type:{:?} exchange_id:{:?} order_direction:{:?} user_id:{:?} channel_id:{:?} stock_type:{:?}",
        //     fee_type, exchange_id, order_direction, user_id, channel_id, stock_type
        // );

        let key = format!("{}_{}_{}_{}", order_direction, user_id, channel_id, stock_type);
        if self.ccopt.use_cache {
            if let Some(value) = self.akacache.fee_settings.get(&fee_type) {
                if let Some(v) = value.get(&exchange_id) {
                    if let Some(fee_settings) = v.get(&key) {
                        info!("缓存中读取到费用信息:{:?}", fee_settings.value());
                        return Ok(fee_settings.value().to_vec());
                    }
                }
            }
        }

        let client = self.check_client().await?;
        let response = client
            .write()
            .await
            .as_mut()
            .unwrap()
            .query_fee_setting(FeeSettingReq {
                fee_type: fee_type.clone(),
                exchange_id,
                order_direction,
                channel_id,
                stock_type,
                user_id,
            })
            .await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }
        let resp = response.unwrap().into_inner();
        if resp.ret_code != 0 {
            error!("query_fee_setting failed: code:{:?} msg:{:?}", resp.ret_code, resp.ret_msg);
            return Err(anyhow!("failed"));
        }

        if self.ccopt.use_cache {
            // info!("将费用信息加入缓存");
            self.akacache
                .fee_settings
                .entry(fee_type.clone())
                .and_modify(|value| {
                    value
                        .entry(exchange_id)
                        .and_modify(|v| {
                            v.insert(key.clone(), resp.fee_settings.clone());
                        })
                        .or_insert({
                            let map = DashMap::new();
                            map.insert(key.clone(), resp.fee_settings.clone());
                            map
                        });
                })
                .or_insert({
                    let map = DashMap::new();
                    map.insert(key, resp.fee_settings.clone());
                    let map_ = DashMap::new();
                    map_.insert(exchange_id, map);
                    map_
                });
        }

        Ok(resp.fee_settings)
    }

    //查询股票停牌信息
    pub async fn query_stock_suspension_info(&self) -> Result<Vec<StockSuspension>> {
        // debug!("query_stock_suspension_info");

        let client = self.check_client().await?;
        let response = client.write().await.as_mut().unwrap().query_stock_suspension_info(StockSuspensionReq {}).await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }
        let resp = response.unwrap().into_inner();
        if resp.ret_code != 0 {
            error!("query_stock_suspension_info failed: code:{:?} msg:{:?}", resp.ret_code, resp.ret_msg);
            return Err(anyhow!("failed"));
        }
        Ok(resp.stock_suspension)
    }

    //查询股票交易时间
    pub async fn query_stock_trade_time(&self, stock_id: i64, order_direction: i32) -> Result<Vec<TradeTime>> {
        // debug!("query_stock_trade_time stock_id:{:?}  order_direction:{:?}", stock_id, order_direction);

        // 优先从缓存中获取
        if self.ccopt.use_cache {
            let sttradetimes = self.akacache.stocktradetimes.get(&stock_id);
            if !sttradetimes.is_none() {
                // return Ok(accountinfo.unwrap().to_owned());
                let sttradetimes = sttradetimes.unwrap();
                let sttradetime = sttradetimes.get(&order_direction);
                if !sttradetime.is_none() {
                    let value = sttradetime.unwrap().to_owned();
                    let fv: Vec<TradeTime> = value.into_iter().filter(|v| v.op_type == order_direction).collect();
                    if fv.len() > 0 {
                        return Ok(fv);
                    }
                }
            }
        };

        let mut buytimes: Vec<TradeTime> = vec![];
        let mut selltimes: Vec<TradeTime> = vec![];

        //缓存中不存在 则重新获取
        let client = self.check_client().await?;
        let response = client.write().await.as_mut().unwrap().query_stock_trade_time(StockTradeTimeReq { stock_id: stock_id, exchange_id: 0 }).await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }
        let resp = response.unwrap().into_inner();
        if resp.ret_code != 0 {
            error!("query_stock_trade_time failed: code:{:?} msg:{:?}", resp.ret_code, resp.ret_msg);
            return Err(anyhow!("failed"));
        }

        resp.tradetimes.into_iter().for_each(|v| match v.op_type {
            1 => buytimes = TradeTime::build(&v),
            2 => selltimes = TradeTime::build(&v),
            _ => error!("unknow op_type:"),
        });

        {
            if buytimes.is_empty() || selltimes.is_empty() {
                // 不存在则取市场的交易时间
                let stockinfo = self.query_stock_info(stock_id).await;
                if stockinfo.as_ref().is_err() {
                    error!("query_stock_trade_time failed:query stock-info err");
                    return Err(anyhow!("query stock-info err"));
                }
                let stock = stockinfo.unwrap();
                if stock.is_none() {
                    return Ok(vec![]);
                }

                let markettradetime = self.get_market_trade_time(stock.unwrap().market_id).await;
                if markettradetime.as_ref().is_none() {
                    error!("query_stock_trade_time failed:get market trade time err");
                    return Err(anyhow!("get market trade time err"));
                }
                let markettradetime = markettradetime.unwrap();
                let marketbuytimes = markettradetime.value().get(&1);
                if marketbuytimes.as_ref().is_none() {
                    error!("query_stock_trade_time failed:marketbuytimes is none");
                    return Err(anyhow!("marketbuytimes is none"));
                }
                let marketselltimes = markettradetime.value().get(&2);
                if marketselltimes.as_ref().is_none() {
                    error!("query_stock_trade_time failed:marketselltimes is none");
                    return Err(anyhow!("marketselltimes is none"));
                }

                if buytimes.is_empty() {
                    buytimes = marketbuytimes.unwrap().value().clone();
                }
                if selltimes.is_empty() {
                    selltimes = marketselltimes.unwrap().value().clone();
                }
            }
            let stocktradetime = DashMap::new();
            stocktradetime.insert(1, buytimes);
            stocktradetime.insert(2, selltimes);
            self.akacache.stocktradetimes.insert(stock_id, stocktradetime);
        }

        let tradetimes = self.akacache.stocktradetimes.get(&stock_id);
        if tradetimes.as_ref().is_none() {
            error!("query_stock_trade_time failed:查询股票交易时间为空");
            return Err(anyhow!("failed"));
        }
        let tradetimes = tradetimes.unwrap();
        let tradetime = tradetimes.value().get(&order_direction);
        if tradetime.as_ref().is_none() {
            error!("query_stock_trade_time failed:查询交易时间为空");
            return Err(anyhow!("failed"));
        }
        let fv = tradetime.unwrap().value().to_owned();
        if fv.len() == 0 {
            error!("query_stock_trade_time failed:查询交易时间结果为空");
            return Err(anyhow!("failed"));
        }
        Ok(fv)
    }

    async fn get_market_trade_time(&self, market_id: i64) -> Option<Ref<i64, DashMap<i32, Vec<TradeTime>>>> {
        // debug!("get_market_trade_time market_id:{:?}", market_id);

        // 在启用缓存时,缓存中存在则取缓存
        if self.ccopt.use_cache {
            let markettradetime = self.akacache.marktetradetimes.get(&market_id);
            if !markettradetime.as_ref().is_none() {
                return markettradetime;
            }
        }
        // debug!("get_market_trade_time request api");

        let tradetimereq = StockTradeTimeReq { stock_id: 0, exchange_id: market_id };
        let ret = self.check_client().await;
        if ret.is_err() {
            error!("{}", ret.unwrap_err());
            return None;
        }
        let client = ret.unwrap();
        let response = client.write().await.as_mut().unwrap().query_stock_trade_time(tradetimereq).await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return None;
        }
        let marketinforesp = response.unwrap().into_inner();
        if marketinforesp.ret_code != 0 {
            error!("load_market_trade_time failed: code:{:?} msg:{:?}", marketinforesp.ret_code, marketinforesp.ret_msg);
            return None;
        }
        let buyselltimes: DashMap<i32, Vec<TradeTime>> = DashMap::new();
        let mut buytimes: Vec<TradeTime> = vec![];
        let mut selltimes: Vec<TradeTime> = vec![];
        marketinforesp.tradetimes.into_iter().for_each(|v| match v.op_type {
            1 => buytimes = TradeTime::build(&v),
            2 => selltimes = TradeTime::build(&v),
            _ => error!("unknow op_type:"),
        });
        if buytimes.is_empty() || selltimes.is_empty() {
            error!("load_market_trade_time failed: market:{:?} 开仓时间或平仓时间为空", market_id);
            return None;
        }
        buyselltimes.insert(1, buytimes);
        buyselltimes.insert(2, selltimes);
        self.akacache.marktetradetimes.insert(market_id, buyselltimes);
        return self.akacache.marktetradetimes.get(&market_id);
    }

    // 交易时间检查
    pub async fn trade_time_check(&self, stock_id: i64, order_direction: i32) -> bool {
        let mut check_pass: bool = false;
        let tradetimes = self.query_stock_trade_time(stock_id, order_direction).await;
        if tradetimes.as_ref().is_err() {
            error!("trade_time_check: stock_id({:?}) order_direction({:?}) 查询交易时间出错", stock_id, order_direction);
            return false;
        }
        let current_time = SystemTime::now().duration_since(UNIX_EPOCH).expect("get_current_unix_err").as_secs().to_i64().unwrap();
        let tradetimes = tradetimes.unwrap();
        for tt in tradetimes {
            let tradebegin = time_parser(&tt.begin);
            let tradeend = time_parser(&tt.end);
            if tradebegin.as_ref().is_err() || tradeend.as_ref().is_err() {
                error!("trade_time_check: stock_id({:?}) order_direction({:?}) time_parser error", stock_id, order_direction);
                return false;
            }

            if current_time >= tradebegin.unwrap() && current_time < tradeend.unwrap() {
                check_pass = true;
                break;
            }
        }
        check_pass
    }

    ///查询融券额度
    pub async fn query_securities_borrow_limit(&self, stock_id: i64) -> Result<Vec<SecuritiesBorrowLimit>> {
        // info!("query_securities_borrow_limit stock_id:{:?}", stock_id);

        let client = self.check_client().await?;
        let response = client.write().await.as_mut().unwrap().query_securities_borrow_limit(SecuritiesBorrowLimitReq { stock_id }).await;
        // info!("查找融券信息结果:{:?}", &response);
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }
        let resp = response.unwrap().into_inner();
        if resp.ret_code != 0 {
            error!("query_securities_borrow_limit failed: code:{:?} msg:{:?}", resp.ret_code, resp.ret_msg);
            return Err(anyhow!("failed"));
        }
        Ok(resp.securities_borrow_limit)
    }

    ///查询融券优先级
    pub async fn query_securities_borrow_level(&self, user_ids: String) -> Result<Vec<SecuritiesBorrowLevel>> {
        // info!("query_securities_borrow_level user_ids:{:?}", &user_ids);

        let client = self.check_client().await?;
        let response = client.write().await.as_mut().unwrap().query_securities_borrow_level(SecuritiesBorrowLevelReq { user_ids }).await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }
        let resp = response.unwrap().into_inner();
        if resp.ret_code != 0 {
            error!("query_securities_borrow_level failed: code:{:?} msg:{:?}", resp.ret_code, resp.ret_msg);
            return Err(anyhow!("failed"));
        }
        Ok(resp.securities_borrow_level)
    }

    pub async fn query_market_code(&self, stock_code: String) -> Result<(String, i32)> {
        // info!("query_market_code stock_code:{:?}", &stock_code);

        let client = self.check_client().await?;
        let response = client.write().await.as_mut().unwrap().query_market_code(MarketCodeReq { stock_code }).await;
        if response.as_ref().is_err() {
            error!("failed:{:?}", response.unwrap_err());
            return Err(anyhow!("failed"));
        }
        let resp = response.unwrap().into_inner();
        if resp.ret_code != 0 {
            error!("query_market_code failed: code:{:?} msg:{:?}", resp.ret_code, resp.ret_msg);
            return Err(anyhow!("failed"));
        }
        Ok((resp.market_code, resp.exchange_id))
    }

    /// Get a clone of the shutdown sender for external coordination
    pub fn get_shutdown_sender(&self) -> broadcast::Sender<()> {
        self.shutdown_tx.clone()
    }
}

// 根据币种代码获取币种类别
pub fn get_currency_by_code(currency: &str) -> Currency {
    match currency.to_uppercase().as_str() {
        "HKD" => Currency::Hkd,
        "CNY" => Currency::Cny,
        "USD" => Currency::Usd,
        "CNH" => Currency::Cnh,
        &_ => Currency::Undef,
    }
}

pub fn time_parser(time_without_date: &str) -> Result<i64> {
    let timewithdate: String;
    let nowdate = Local::now().format("%Y-%m-%d");
    let c = time_without_date.matches(":").count() as i32;
    if c == 1 {
        timewithdate = format!("{} {}:00.000", nowdate, time_without_date);
    } else if c == 2 {
        timewithdate = format!("{} {}.000", nowdate, time_without_date);
    } else {
        return Err(anyhow!("time format error:{:?}", time_without_date));
    }
    // info!("{} {}", c, timewithdate);

    let ret = utility::timeutil::build_naive_date_time(&timewithdate);
    let local_dt = Local.from_local_datetime(&ret);
    // info!("{:?}", local_dt);

    Ok(local_dt.unwrap().timestamp())

    // let datetime = DateTime::parse_from_str(&timewithdate, "%Y%m%d %H:%M:%S");
    // info!("{:?}", datetime);
    // if datetime.is_err() {
    //     return Err(anyhow!("time parser error:{:?}", timewithdate));
    // }
    // Ok(datetime.unwrap().to_owned().timestamp())
}
