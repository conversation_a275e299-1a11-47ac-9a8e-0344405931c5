//行情相关接口文件
syntax = "proto3";

package hqcenter;

service SvrPostSubscribeHqMsg{
  rpc PostSubscribeHqMsg(SubscribeHqMsgReq) returns (ResultMsg) {}	//行情订阅接口
  rpc PostHistoryKLineHq(KLineHqReq) returns(KLineHqResp){};			//历史K线请求
  rpc PostCurrentKlineHq(KLineHqReq) returns(KLineHqResp){};			//当前K线
  rpc PostHistoryFenShiHq (KLineHqReq) returns(KLineHqResp){};		//历史分时
  rpc PostCurrentFenShiHq (KLineHqReq) returns(KLineHqResp){};		//当前分时
  rpc PostTickHq(TickHqReq) returns (TickHqResp) {};				//最新TICK数据

  rpc get_last_price(LastPriceMsgReq) returns (LastPriceMsgResp) {} //最新价获取
	rpc get_pre_deal_amount(PreDealNumReq) returns (PreDealAmount) {}
}

message SubscribeHqMsgReq {
	int32		Action = 1;				//动作类型 1：订阅，0：取消订阅。
	string		Goods = 2;			    //品种代码
	string		ExchangeNO = 3;			//市场代码
	string		Contract = 4;			//合约编号
	string		ID = 5;					//Redis中对应ID
	string    	CommodityType= 6;		//商品类型，期货为F
}

// 回复的数据包
message ResultMsg {
	string errMsg = 1;
	int32  errCode = 2;                 //0 代表成功，其他错误码自行定义
}

message TickHqReq {
	string  strcontractno = 1;		//合约编号可选
	int32	iticktype = 2;			//请求行情类型 0:1档行情(默认) 1:5档行情
	int64   ticktime = 3;        //请求tick时间 unix时间
	int32   realtime = 4;            //0 延迟行情  1实时行情 
}

message TickHqResp {
	repeated YsHqInfo tick_hq_info = 1;
}

//备注:历史K线请求必传：合约编号,结束时间, 条数。当前K线请求：传条数 ,合约 
//历史分时请求：只传合约编号拼接日期 合约编号值为：合约|日期。 当前分时请求:合约+日期   例如:CL1810|20180910 
message KLineHqReq {
	string  strcontractno = 1;		//合约编号
	string	strklinetype = 2;		//1:一分钟 5:5分钟, 10:10分钟,30: 30分钟, 60：60分钟,24:日线
	string  strendtime = 3;			//结束时间
	int32	limit = 4;				//条数
	int32   realtime = 5;            //0 延迟行情  1实时行情  
}

message KLineHqResp {
	repeated KLineHqInfo klineinfo = 1; 
}

message KLineHqInfo {
	string strkline = 1;
}

message LastPriceMsgReq 
{
    string	StockCode 		= 1;		//证券代码 60001
    int32	ExchangeId 	= 2;		//市场 沪深:101 102 港:103 ...
}

message LastPriceMsgResp
{
    int32  ErrCode = 1;
    string ErrMsg = 2;
    LastPriceInfo Data = 3;
}

message LastPriceInfo
{
    double  LastPrice = 1;             //最新价
    double  Change_Rate = 2;            //涨幅
    double  Change_Value = 3;           //涨跌
}

message HqMsgReq{
    CtpHqInfo ctphqinfo = 1; 				//ctp行情
    YsHqInfo  yshqinfo	= 2;				//易盛行情
}

message CtpHqInfo{
    string		trading_day = 1;				//交易日
    string		instrument_id = 2;			//合约代码
    string		exchange_id = 3;				//市场代码
    string		exchange_inst_id = 4;			//合约在交易所的代码
    double     	last_price = 5;				//最新价
    double		pre_settlement_price = 6;		//上次结算价
    double 		pre_close_price = 7;			//昨收盘
    double		pre_open_interest	 = 8;		//昨持仓量
    double		open_price	 = 9;			//今开盘
    double		highest_price	= 10;       //最高价
    double		lowest_price = 11;			//最低价
    int32		volume	= 12;				//数量
    double 		turnover	= 13;			//成交金额
    double		open_interest = 14;			//持仓量
    double		close_price	= 15;			//今收盘
    double		settlement_price   = 16;	//本次结算价
    double		upper_limit_price = 17;		//涨停板价
    double		lower_limit_price = 18;		//跌停板价
    double	    pre_delta = 19;   			//昨虚实度
    double 		curr_delta = 20;     		//今虚实度
    string 		update_time = 21;			//最后修改时间
    int32 		update_millisec = 22;		//最后修改毫秒
    double 		bid_price1 = 23;     		//申买价一
    int32		bid_volume1	= 24;			//申买量一
    double 		ask_price1 = 25;     		//申卖价一
    int32		ask_volume1	= 26;			//申卖量一
    double 		bid_price2 = 27;     		//申买价二
    int32		bid_volume2	= 28;			//申买量二
    double 		ask_price2 = 29;     		//申卖价二
    int32		ask_volume2	=30;			//申卖量二
    double 		bid_price3   =31;     		//申买价三
    int32		bid_volume3	=32;			//申买量三
    double 		ask_price3   =33;     		//申卖价三
    int32		ask_volume3	=34;			//申卖量三
    double 		bid_price4   =35;     		//申买价四
    int32		bid_volume4	=36;			//申买量四
    double 		ask_price4   =37;     		//申卖价四
    int32		ask_volume4	=38;			//申卖量四
    double 		bid_price5   =39;     		//申买价五
    int32		bid_volume5	=40;			//申买量五
    double 		ask_price5  = 41;     		//申卖价五
    int32		ask_volume5 = 42;			//申卖量五
    double 		average_price = 43;			//当日均价
    string		action_day =44;				//业务日期
}

message YsHqInfo
{
    string        exchange_id          = 1;             //市场代码
    string        commodity_no         = 2;             //品种编号
    string        contract_no1         = 3;             //合约代码
    string        currency_no          = 4;             //币种编号
    string        TAPIDTSTAMP         = 5;              //时间戳
    double        q_pre_settle_price     = 6;           //昨结算价
    int64         q_pre_position_qty     = 7;		    //昨持仓量
    double        q_opening_price       = 8;            //开盘价
    double        q_last_price          = 9;            //最新价
    double        q_high_price          = 10;           //最高价
    double        q_low_price           = 11;           //最低价
    double        q_limit_up_price       = 12;          //涨停价
    double        q_limit_down_price  = 13;          //跌停价
    int64         q_total_qty         = 14;          //当日总成交量
    double        q_total_turnover	  = 15;			 //当日成交金额
    int64		  q_position_qty		= 16;	     //持仓量
    double        q_average_price       = 17;        //均价
    double        q_closing_price       = 18;	     //收盘价
    double        q_last_qty            = 19;        //最新成交量
    repeated  double q_bid_price        = 20;        //买价1-5档
    repeated  int64  q_bid_qty          = 21;        //买量1-5档
    repeated  double q_ask_price        = 22;        //卖价1-5档
    repeated  int64  q_ask_qty        = 23;          //卖量1-5档
    double      q_change_rate         = 24;          //涨幅
    double      q_change_value        = 25;          //涨跌值
    double      q_pre_closing_price   = 26;			 //昨收价
    int64		q_total_bid_qty		= 27;			 //委买总量
    int64		q_total_ask_qty		= 28;			 //委卖总量
    //新加
    double		q_turnover_ratio	= 29;			//换手率
    double		q_amplitude			= 30;			//振幅
    double		q_pe_rate			= 31;			//市盈率
    double		q_dyn_pb_rate		= 32;		    //市净率
    double		q_vol_ratio			= 33;			//量比
    int64		q_circulation_amount	= 34;		//流通股
    int64		q_total_shares		= 35;			//总股本
    double		q_MarketValue		= 36;			//总市值
    string		q_money_type		= 37;		    //货币
    string		q_industry_info		= 38;			//行业信息
    double		q_last_turnover		= 39;			//最新成交额
    double		q_entrust_rate		= 40;			//委比

    repeated  double  q_bid_qty2         = 41;          //买量1-5档
    repeated  double  q_ask_qty2         = 42;          //卖量1-5档
    double        q_total_qty2           = 43;          //当日总成交量
}

message PreDealNumReq
{
	string  StockCode       = 1;    //证券代码 60001
	int32   ExchangeId  = 2;        //市场 沪深:101 102 港:103 ...
	string  Time = 3;               //20230830142500
}

message PreDealAmount {
	int32 PrevPeriodAmount  = 1;
}
