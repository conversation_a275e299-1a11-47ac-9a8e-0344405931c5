//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_dividend_info_his")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub dividend_no: i64,
    pub sys_date: i32,
    pub stock_code: String,
    pub exchange_id: i32,
    pub dividend_flag: i32,
    pub ex_date: i32,
    pub deal_date: i32,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub dividend_price: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub stock_rate: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 6)))")]
    pub tax_rate: Decimal,
    pub lastupdate_date: i32,
    pub operator_no: i32,
    pub status: i32,
    pub record_date: i32,
    pub currency_no: String,
    pub register_flag: i32,
    pub register_date: i32,
    pub remark: String,
    pub modify_time: i64,
    pub create_time: i64,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
