use crate::dataservice::old_stock_entities::prelude::{Tstockposition, Tunitasset};
use crate::dataservice::old_stock_entities::tstockposition;
use crate::dataservice::{
    dbsetup::DbConnection,
    stock_entities::{phoenix_ast_unitasset, prelude::*},
};
use anyhow::{anyhow, Result};
use rust_decimal::prelude::{ToPrimitive, Zero};
use rust_decimal::Decimal;
use sea_orm::{ActiveModelTrait, ColumnTrait, Condition, ConnectionTrait, DbErr, EntityTrait, QueryFilter, QuerySelect};
use serde_json::json;

#[allow(dead_code)]
impl phoenix_ast_unitasset::Model {
    pub async fn find_by_condition(db: &DbConnection, condition: Condition) -> Result<Option<PhoenixAstUnitasset>> {
        let ret_data: Result<Option<PhoenixAstUnitasset>, DbErr> = PhoenixAstUnitassetEntity::find().filter(condition).one(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(format!("PhoenixAstUnitasset: {:?}", ret_data.err())));
        }

        Ok(ret_data.unwrap())
    }

    pub async fn find_id_by_unit_id<'a, 'async_trait, C>(db: &'a C, unit_id: i64) -> Result<Option<i64>>
    where
        C: ConnectionTrait,
        'a: 'async_trait,
        C: 'async_trait,
    {
        let ret_data: Result<Option<i64>, DbErr> = PhoenixAstUnitassetEntity::find()
            .filter(phoenix_ast_unitasset::Column::UnitId.eq(unit_id))
            .select_only()
            .column(phoenix_ast_unitasset::Column::Id)
            .into_tuple()
            .one(db)
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(format!("PhoenixAstUnitasset: {:?}", ret_data.err())));
        }

        Ok(ret_data.unwrap())
    }

    pub async fn save_many<'a, 'async_trait, C>(info: &mut Vec<PhoenixAstUnitasset>, db: &'a C) -> Result<i64>
    where
        C: ConnectionTrait,
        'a: 'async_trait,
        C: 'async_trait,
    {
        if info.len() <= 0 {
            return Err(anyhow!("empty data"));
        }

        let mut insert_values: Vec<phoenix_ast_unitasset::ActiveModel> = Vec::new();
        let mut update_values: Vec<phoenix_ast_unitasset::ActiveModel> = Vec::new();
        for val in info.iter_mut() {
            let opt = PhoenixAstUnitasset::find_id_by_unit_id(db, val.unit_id).await?;
            match opt {
                None => {}
                Some(id) => {
                    val.id = id;
                }
            }

            let ret = phoenix_ast_unitasset::ActiveModel::from_json(json!(val));
            if ret.as_ref().is_err() {
                return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
            }
            if val.id <= 0 {
                insert_values.push(ret.unwrap());
            } else {
                update_values.push(ret.unwrap());
            }
        }

        let mut last_insert_id = 0;

        // do insert
        if insert_values.len() > 0 {
            let ret = PhoenixAstUnitassetEntity::insert_many(insert_values).exec(db).await;
            if ret.as_ref().is_err() {
                return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
            }

            let ids = ret.unwrap();
            last_insert_id = ids.last_insert_id;
        }

        //do update
        if update_values.len() > 0 {
            for val in update_values {
                let ret = val.update(db).await;
                if ret.as_ref().is_err() {
                    return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
                }
            }
        }

        Ok(last_insert_id)
    }

    pub async fn insert_many<'a, 'async_trait, C>(info: &Vec<PhoenixAstUnitasset>, db: &'a C) -> Result<i64>
    where
        C: ConnectionTrait,
        'a: 'async_trait,
        C: 'async_trait,
    {
        if info.len() <= 0 {
            return Err(anyhow!("empty data"));
        }

        let mut insert_values: Vec<phoenix_ast_unitasset::ActiveModel> = Vec::new();
        for val in info {
            let opt = PhoenixAstUnitasset::find_id_by_unit_id(db, val.unit_id).await?;
            match opt {
                None => {}
                Some(_) => {
                    return Ok(0);
                }
            }

            let ret = phoenix_ast_unitasset::ActiveModel::from_json(json!(val));
            if ret.as_ref().is_err() {
                return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
            }
            insert_values.push(ret.unwrap());
        }

        let mut last_insert_id = 0;

        // do insert
        if insert_values.len() > 0 {
            let ret = PhoenixAstUnitassetEntity::insert_many(insert_values).exec(db).await;
            if ret.as_ref().is_err() {
                return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
            }

            let ids = ret.unwrap();
            last_insert_id = ids.last_insert_id;
        }

        Ok(last_insert_id)
    }

    pub async fn covert_form_tunitasset(src_db: &DbConnection, src: &Tunitasset, rate: f64) -> Result<Self> {
        let condition = Condition::all().add(tstockposition::Column::LUnitId.eq(src.l_unit_id));
        let stock_positions = Tstockposition::find_all_by_condition(src_db, condition).await?;
        let current_cash = src.en_current_cash - src.en_credit_cash + stock_positions.iter().map(|model| model.en_total_value_hkd).sum::<Decimal>();

        let today_total_value = current_cash
            + stock_positions
                .iter()
                .map(|model| model.en_last_price.unwrap_or_default() * Decimal::from(model.l_current_amount) * Decimal::from_f64_retain(rate).unwrap_or_default() - model.en_total_value_hkd)
                .sum::<Decimal>()
            - src.en_frozen_capital;

        let enable_cash = today_total_value - stock_positions.iter().map(|model| model.en_total_value_hkd / (Decimal::from(1) + src.en_credit_multiple)).sum::<Decimal>();

        Ok(PhoenixAstUnitasset {
            id: 0,
            sys_date: src.l_date.to_i32().unwrap_or_default(),
            unit_id: src.l_unit_id.to_i64().unwrap_or_default(),
            begin_cash: current_cash,
            current_cash,
            frozen_capital: src.en_frozen_capital,
            trade_frozen_capital: Decimal::zero(),
            cash_in_transit: src.en_cash_in_transit,
            currency_no: "HKD".to_string(),
            credit_multiple: src.en_credit_multiple,
            enable_cash,
            today_total_value,
            today_deposit: Decimal::zero(),
            today_withdraw: Decimal::zero(),
            total_deposit: src.en_into_cash_total,
            total_withdraw: src.en_withdraw_cash_total,
            gem_frozen_capital: Decimal::zero(),
        })
    }
}
