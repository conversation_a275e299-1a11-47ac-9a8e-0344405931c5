use crate::server::service::userpositionservice::UserPositionService;
use akaclient::akaclient::AkaClient;
use anyhow::{anyhow, Result};
use common::constant;
// use itertools::Itertools;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::*;

type StockId = i64;

#[derive(Debug, <PERSON>lone)]
struct UserLimit {
    user_id: i64,
    available_amount: i32,
    used_amount: i32,
}

#[derive(Debug, <PERSON><PERSON>)]
struct UserTotalLimit {
    total_amount: i32, //分配数量
    total_used_amount: i32,
    user_limit: Vec<UserLimit>,
}

#[derive(Debug, Clone)]
pub struct SecuritiesBorrowPool {
    securities_borrow_pool: Arc<RwLock<HashMap<StockId, UserTotalLimit>>>,
}

impl SecuritiesBorrowPool {
    pub fn new() -> Self {
        SecuritiesBorrowPool {
            securities_borrow_pool: Arc::new(RwLock::new(HashMap::<StockId, UserTotalLimit>::new())),
        }
    }

    pub async fn reset(&self, aka_client: &AkaClient, user_position_svc: &UserPositionService) -> Result<()> {
        let mut wr = self.securities_borrow_pool.write().await;
        wr.clear();
        drop(wr);

        self.init(aka_client, user_position_svc).await
    }

    pub async fn init(&self, aka_client: &AkaClient, user_position_svc: &UserPositionService) -> Result<()> {
        info!("初始化融券池...");
        let vec_infos = aka_client.query_securities_borrow_limit(constant::VALUE_ALL).await?;
        info!("查到融券信息: {:?}", &vec_infos);
        // info!("查询到{}条融券信息", vec_infos.len());
        for info in vec_infos {
            let user_positions = user_position_svc.query_user_positions(constant::VALUE_ALL, constant::VALUE_ALL, info.stock_id).await;
            let mut user_datas = vec![];
            let (total_amount, total_used_amount): (Vec<_>, Vec<_>) = info
                .user_securities_borrow_limit
                .iter()
                .map(|f| {
                    let used_amount = user_positions.iter().find_map(|item| if item.unit_id == f.user_id { Some(item.use_credit) } else { None }).unwrap_or_default();
                    user_datas.push(UserLimit {
                        user_id: f.user_id,
                        available_amount: f.available_amount,
                        used_amount,
                    });
                    (f.available_amount, used_amount)
                })
                .unzip();

            info!("stock_id: {}, user_datas: {:?}", info.stock_id, &user_datas);

            let (total_amount, total_used_amount): (i32, i32) = (total_amount.iter().sum(), total_used_amount.iter().sum());

            info!("total_amount: {}, total_used_amount: {}", total_amount, total_used_amount);

            let mut wr = self.securities_borrow_pool.write().await;
            wr.entry(info.stock_id)
                .and_modify(|amount| {
                    *amount = UserTotalLimit {
                        total_amount,
                        user_limit: user_datas.clone(),
                        total_used_amount,
                    }
                })
                .or_insert(UserTotalLimit {
                    total_amount,
                    user_limit: user_datas,
                    total_used_amount,
                });
        }

        Ok(())
    }

    ///获取指定股票全部可用融券额
    pub async fn get_total_amount(&self, stock_id: i64) -> Option<i32> {
        let rd = self.securities_borrow_pool.read().await;
        rd.get(&stock_id).map(|amount| amount.total_amount)
    }

    pub async fn get_total_used_amount(&self, stock_id: i64) -> Option<i32> {
        let rd = self.securities_borrow_pool.read().await;
        rd.get(&stock_id).map(|amount| amount.total_used_amount)
    }

    pub async fn get_amount(&self, stock_id: i64, user_id: i64) -> (i64, i64, i32, i32) {
        let rd = self.securities_borrow_pool.read().await;
        if let Some(item) = rd.get(&stock_id) {
            match item
                .user_limit
                .iter()
                .find_map(|v| if v.user_id == user_id { Some((stock_id, user_id, v.available_amount, v.used_amount)) } else { None })
            {
                None => (stock_id, user_id, 0, 0),
                Some(amount) => amount,
            }
        } else {
            (stock_id, user_id, 0, 0)
        }
    }

    pub async fn get_amounts(&self, stock_id: i64, unit_id: i64) -> Vec<(i64, i64, i32, i32)> {
        if stock_id == 0 && unit_id == 0 {
            self.get_amount_all().await
        } else if stock_id == 0 {
            self.get_amount_by_user_id(unit_id).await
        } else if unit_id == 0 {
            self.get_amount_by_stock_id(stock_id).await
        } else {
            vec![self.get_amount(stock_id, unit_id).await]
        }
    }

    pub async fn get_amount_all(&self) -> Vec<(i64, i64, i32, i32)> {
        let rd = self.securities_borrow_pool.read().await;
        let mut data = vec![];
        for (key, value) in rd.iter() {
            let res_data: Vec<_> = value.user_limit.iter().map(|item| (*key as i64, item.user_id, item.available_amount, item.used_amount)).collect();
            data.extend(res_data);
        }

        data
    }

    pub async fn is_exit(&self, stock_id: i64) -> bool {
        let rd = self.securities_borrow_pool.read().await;
        rd.contains_key(&stock_id)
    }

    pub async fn user_is_exit(&self, stock_id: i64, user_id: i64) -> bool {
        let rd = self.securities_borrow_pool.read().await;
        if let Some(item) = rd.get(&stock_id) {
            if let Some(_) = item.user_limit.iter().position(|v| v.user_id == user_id) {
                true
            } else {
                false
            }
        } else {
            false
        }
    }

    pub async fn get_amount_by_user_id(&self, user_id: i64) -> Vec<(i64, i64, i32, i32)> {
        let rd = self.securities_borrow_pool.read().await;
        let res: Vec<(i64, i64, i32, i32)> = rd
            .iter()
            .filter_map(|(key, value)| {
                value.user_limit.iter().find_map(|item| {
                    if item.user_id == user_id {
                        Some((*key as i64, item.user_id, item.available_amount, item.used_amount))
                    } else {
                        None
                    }
                })
            })
            .collect();

        res
    }

    pub async fn get_amount_by_stock_id(&self, stock_id: i64) -> Vec<(i64, i64, i32, i32)> {
        let rd = self.securities_borrow_pool.read().await;
        let res = rd.iter().find_map(|(key, value)| {
            if *key == stock_id {
                let res_data: Vec<_> = value.user_limit.iter().map(|item| (stock_id, item.user_id, item.available_amount, item.used_amount)).collect();
                Some(res_data)
            } else {
                None
            }
        });

        res.unwrap_or(vec![])
    }

    pub async fn _get_user_info(&self) -> Vec<(i64, Vec<i64>)> {
        self.securities_borrow_pool
            .read()
            .await
            .iter()
            .map(|(stock_id, value)| {
                let users_id: Vec<_> = value.user_limit.iter().map(|item| item.user_id).collect();
                (*stock_id as i64, users_id)
            })
            .collect()
    }

    pub async fn update_total_used_amount(&self, stock_id: i64, total_used_amount: i32) -> Result<()> {
        let mut wr = self.securities_borrow_pool.write().await;
        if let Some(item) = wr.get_mut(&stock_id) {
            item.total_used_amount += total_used_amount;
            Ok(())
        } else {
            Err(anyhow!(format!("该票的融券额不存在: {}", stock_id)))
        }
    }

    pub async fn set_use_credit(&self, stock_id: i64, user_id: i64, use_credit: i32) -> Result<()> {
        let mut wr = self.securities_borrow_pool.write().await;
        if let Some(item) = wr.get_mut(&stock_id) {
            if let Some(user_info) = item.user_limit.iter_mut().find(|v| v.user_id == user_id) {
                user_info.used_amount = use_credit;
            } else {
                return Err(anyhow!(format!("该票的融券用户不存在: stock_id: {} user_id: {}", stock_id, user_id)));
            }
            Ok(())
        } else {
            Err(anyhow!(format!("该票的融券额不存在: {}", stock_id)))
        }
    }

    pub async fn clear_amount(&self, stock_id: i64) -> Result<()> {
        let mut wr = self.securities_borrow_pool.write().await;
        wr.remove(&stock_id);

        Ok(())
        // if let Some(item) = wr.get_mut(&stock_id) {
        //   item.total_amount = 0;
        //   item.total_used_amount = 0;
        //   item.user_limit
        //       .iter_mut()
        //       .for_each(|v| {
        //         v.available_amount = 0;
        //       });
        //   Ok(())
        // } else {
        //   Err(anyhow!(format!("该票的融券额不存在: {}", stock_id)))
        // }
    }
}
