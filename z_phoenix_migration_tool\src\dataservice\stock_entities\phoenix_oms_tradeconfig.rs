//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_oms_tradeconfig")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub exchange_id: i32,
    pub order_direction: i32,
    pub clear_speed: String,
    pub market_cash_add_type: i32,
    pub deal_cash_frozen_type: i32,
    pub deal_stock_frozen_type: i32,
    pub asset_settle_point: i32,
    pub asset_settle_date: i32,
}

#[derive(<PERSON><PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
