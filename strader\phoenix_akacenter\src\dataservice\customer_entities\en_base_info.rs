//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "en_base_info")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub t_id: i64,
    pub t_type: i32,
    pub t_json: String,
    pub tran_type: i8,
    pub lan_type: i8,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
