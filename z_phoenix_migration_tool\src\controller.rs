use std::sync::Arc;

use crate::config::settings::Settings;
use anyhow::{anyhow, Result};
use common::uidservice::UidgenService;
use sea_orm::TransactionTrait;
use tokio::sync::RwLock;
// use rust_decimal::{prelude::*, Decimal};
// use tonic::{self};
use crate::dataservice::customer_entities::prelude::UsersTradeAccount;
use crate::dataservice::dbsetup::DbConnection;
use crate::dataservice::old_stock_entities::prelude::Tunitasset;
use crate::protofiles::phoenixmigrationtool::{MigrationDataReq, MigrationDataResp};
use crate::result;
use crate::service::proccess::migration_data;
use common::redisclient::redispool::RedisClient;
// use utility::{constant, errors, errors::ErrorCode};

// #[derive(Clone)]
#[allow(dead_code)]
pub struct ServerController {
    pub settings: Arc<RwLock<Settings>>,
    pub src_dbconn: Arc<DbConnection>,
    pub dst_dbconn: Arc<DbConnection>,
    pub customer_dbconn: Arc<DbConnection>,
    pub uidsvc: Arc<RwLock<UidgenService>>,
    pub redis: Arc<RedisClient>,
}

//处理业务逻辑
impl ServerController {
    pub async fn migration_data(&self, req: &MigrationDataReq) -> Result<MigrationDataResp> {
        log::info!("migration_data 数据迁移开始");
        let ctx = &self.dst_dbconn.get_connection();
        let mut err_flag = false;

        let account_nos: Vec<&str> = req.account_no.split(",").collect();
        let account_nos: Vec<i64> = account_nos
            .iter()
            .filter_map(|account_no| if let Ok(account_no) = account_no.parse::<i64>() { Some(account_no) } else { None })
            .collect();

        let account_num = account_nos.len();
        let rate = self.redis.get_value_by_get("migration_rate").await;
        let rate = match rate.parse::<f64>() {
            Ok(value) => value,
            Err(e) => {
                log::error!("请在redis设置正确的汇率: {}", e);
                return Err(anyhow!("请在redis设置正确的汇率: {}", e));
            }
        };
        log::info!("配置的rate: {}", rate);

        if account_num == 0 {
            return Err(anyhow!("请求账户id解析失败"));
        } else if account_num > 1 && account_nos.iter().position(|&x| x == 0).is_some() {
            return Err(anyhow!("账户id存在 0"));
        } else if account_num == 1 && account_nos[0] == 0 {
            log::info!("全部账户开始迁移, 是否覆盖操作：{}", req.f_fill);
            let old_unit_assets = result!(Tunitasset::query_many(0, &self.src_dbconn).await, "数据库查询失败");

            for old_unit_asset in old_unit_assets {
                log::info!("迁移账户：{}...", old_unit_asset.l_unit_id);
                let txn = result!(ctx.begin().await, "事务开启失败");

                let result = migration_data(&txn, &self.src_dbconn, &old_unit_asset, rate, req.f_fill).await;
                if result.is_err() {
                    err_flag = true;
                    let err = result.unwrap_err();
                    log::error!("{}, 数据回滚", &err);
                    result!(txn.rollback().await, "数据库回滚失败");
                } else {
                    result!(txn.commit().await, "数据库提交失败");
                }
            }
        } else {
            log::info!("共有{}个账户，是否覆盖操作：{}", account_nos.len(), req.f_fill);

            let mut user_ids = Vec::new();
            for account_no in account_nos {
                let user_id = result!(UsersTradeAccount::find_user_id_by_account_no(&self.customer_dbconn, account_no).await, "数据库查询错误");
                match user_id {
                    None => {
                        log::warn!("account_no:{} 对应 user_id不存在", account_no);
                        err_flag = true;
                    }
                    Some(user_id) => user_ids.push(user_id),
                }
            }

            for unit_id in user_ids.iter() {
                log::info!("迁移账户：{}...", unit_id);
                let old_unit_assets = result!(Tunitasset::query_many(unit_id.to_owned(), &self.src_dbconn).await, "数据库查询失败");

                if old_unit_assets.is_empty() {
                    log::warn!("Tunitasset中 unit_id为{}账户不存在", unit_id);
                    err_flag = true;
                    continue;
                }

                let txn = result!(ctx.begin().await, "事务开启失败");

                let result = migration_data(&txn, &self.src_dbconn, &old_unit_assets[0], rate, req.f_fill).await;
                if result.is_err() {
                    err_flag = true;
                    let err = result.unwrap_err();
                    log::error!("{}, 数据回滚", &err);
                    result!(txn.rollback().await, "数据库回滚失败");
                } else {
                    result!(txn.commit().await, "数据库提交失败");
                }
            }
        }

        log::info!("migration_data 数据迁移结束");

        if err_flag {
            Ok(MigrationDataResp {
                ret_code: 222,
                ret_msg: String::from("有部分数据存在异常"),
            })
        } else {
            Ok(MigrationDataResp {
                ret_code: 111,
                ret_msg: String::from("success"),
            })
        }
    }
}
