use super::controller::{Per<PERSON><PERSON><PERSON>, PhoenixController};
use super::service::accountassetsservice::PhoenixAccountAssetsService;
use super::service::accountstockpositionservice::PhoenixAccountStockPositionService;

use super::service::basiccacheservice::BasicCacheService;
use super::service::userassetsservice::UserAssetsService;
use super::service::userpositionservice::UserPositionService;

use crate::client::satclient::SatClient;
use crate::config::settings::Settings;
use crate::dataservice::{dbsetup::DbConnection, entities::prelude::*};
use crate::server::service::securities_borrow_pool::SecuritiesBorrowPool;
use akaclient::akaclient::{AkaCacheOption, AkaClient};
use assetscenterclient::AssetsCenterClient;
use common::redisclient::redispool::{RedisClient, RedisConfig};
use common::uidservice::UidgenService;
use manageclient::Manageclient;
use protoes::phoenixaccountriskcenter::account_risk_center_server::AccountRiskCenter;
use protoes::phoenixaccountriskcenter::{
    AccountAvailableAmountRequest, AccountAvailableAmountResponse, MarginRatioReq, MarginRatioResp, PhoenixAccountQueryRequest, PhoenixAccountResetRequest, PhoenixAssetsResponse, PhoenixAssetscomputeRequest,
    PhoenixAssetscomputeResponse, PhoenixStockPositionRequest, PhoenixStockPositionResponse, PhoenixTransferRequest, UserAssetsReq, UserAssetsResp, UserPositionReq, UserPositionResp, UserRqReq, UserRqResp,
};

use messagecenter::notificationclient::NotificationClient;
use messagecenter::quotationclient::QuotationClient;
use protoes::hqmsg::YsHqInfo;
use protoes::phoenixnotification::{NotificationMessage, NotificationType, OrderExecType};

use rust_decimal::Decimal;
use std::collections::HashMap;
use std::sync::atomic::{AtomicBool, Ordering};
use tracing::*;

use std::{pin::Pin, sync::Arc};
use tokio::sync::{mpsc, oneshot, RwLock};

use tonic::{self, Request, Response, Status};
use utility::errors::{self, ErrorCode};
use utility::timeutil;

// type StubType = Arc<RwLock<PhoenixController>>;
type StubType = Arc<PhoenixController>;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

pub struct ServerHandler {
    stub: StubType,
    task_dispacther: mpsc::Sender<ControllerAction>,
    set_close: Option<oneshot::Sender<()>>,
}

pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);

impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

impl ServerHandler {
    pub async fn new(config: &Settings, dbconn: &DbConnection) -> Self {
        let mut persist_interval = tokio::time::interval(std::time::Duration::from_secs(config.system.persist_interval));
        let mut position_interval = tokio::time::interval(std::time::Duration::from_secs(config.system.position_interval));

        // let starttime = Instant::now() + Duration::from_secs(timeutil::get_first_interval_time(&config.system.unitrisk_interval_start));
        // let mut unitsrisk_interval = tokio::time::interval_at(starttime, std::time::Duration::from_secs(24 * 60 * 60));

        let (tx, mut rx) = mpsc::channel(1024);
        let (tx_persist, mut rx_persist) = mpsc::channel::<PersistData>(1024);
        let (tx_close, mut rx_close) = oneshot::channel();
        let (tx_quotation, mut rx_quotation) = tokio::sync::broadcast::channel::<YsHqInfo>(1024);
        let (tx_last_price, mut rx_last_price) = tokio::sync::mpsc::channel::<(String, f64)>(1024);
        let (tx_notification, mut rx_notification) = tokio::sync::mpsc::channel::<NotificationMessage>(1024);
        let (tx_assets, mut rx_assets) = tokio::sync::mpsc::channel::<(i64, i64)>(16);
        let (tx_rq, mut rx_rq) = tokio::sync::mpsc::channel::<(i64, i64, i32)>(16);
        // let (tx_user_notify, _rx_user_notify) = tokio::sync::mpsc::channel::<NotificationMessage>(1024);
        let mut price_cache: HashMap<String, f64> = HashMap::new();
        let atomic_last_price = Arc::new(AtomicBool::new(false));
        let mut atomic_assets_persist = AtomicBool::new(false);
        let opt = AkaCacheOption {
            use_cache: true,
            mq_uri: format!("{}{}", &config.mq.amqpaddr, &config.notification.vhost),
            exchange: config.notification.notification_exchanger.clone(),
            routing_keys: config.notification.accountriskcenter_routing_key.to_owned(),
        };
        let aka_svc = AkaClient::init(config.servers.akacenterserver.to_string(), &opt).await;
        let sat_client = SatClient::init(config.servers.satcenterserver.to_string()).await;
        let mut rdscfg = RedisConfig::default();
        rdscfg.urls = config.redis.uri.to_owned();
        rdscfg.prefix = config.redis.prefix.to_owned();
        rdscfg.max_size = 10;
        rdscfg.min_idle = 10;
        let redis_client = RedisClient::new(&rdscfg).expect("redis cant connect");
        let uidsvc = Arc::new(RwLock::new(UidgenService::new(config.application.machineid, config.application.nodeid)));
        let account_position_svc = PhoenixAccountStockPositionService::new();
        let account_assets_svc = PhoenixAccountAssetsService::new();
        let securities_borrow_pool = SecuritiesBorrowPool::new();
        let sys_info = PhoenixSysSystem::query(&dbconn).await.expect("query phoenix_sys_system info error");
        let ignore_accounts: Vec<i64> = config.system.ignore_user_account.split(',').map(|f| f.parse::<i64>().unwrap_or_default()).collect();
        // let ignore_fee_accounts: Vec<i64> = config.system.ignore_fee_account.split(',').map(|f| f.parse::<i64>().unwrap_or_default()).collect();
        let userassetssvc = UserAssetsService::new();
        let userpositionsvc = UserPositionService::new();
        let basic_cache_svc = Arc::new(BasicCacheService::new());

        let manager_svc = Arc::new(Manageclient::init(config.servers.managerserver.to_string()).await);
        let assetscenter_svc = Arc::new(AssetsCenterClient::init(config.servers.assetscenterserver.to_string()).await);

        //连接消息中心服务
        // 推送key: "notification.accountrisk.client.asseets.*,notification.accountrisk.client.positions.*,notification.accountrisk.client.marginrate"
        let queue_name = format!("phoenix_account_riskcenter_notification_{}", timeutil::current_timestamp());
        let notification_client = NotificationClient::new(
            &config.notification.notification_exchanger.as_str(),
            &queue_name,
            config.notification.accountriskcenter_routing_key.clone(),
            &format!("{}{}", &config.mq.amqpaddr, &config.notification.vhost),
            tx_notification,
        )
        .await;

        // let mut notification_client_clone = notification_client.clone();
        messagecenter::init::init_notification_client(Arc::new(RwLock::new(notification_client.clone()))).await;
        messagecenter::init::init_notification_listen(Arc::new(RwLock::new(notification_client.clone()))).await;

        // old code
        // //推送mq信息给用户客户端消息
        // let queue_name = format!("phoenix_user_notify_client_{}", std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs());
        // let user_notify_client = NotificationClient::new(
        //     &config.messagecenter.exchanger,
        //     &queue_name,
        //     config.messagecenter.userclientkey.to_string(),
        //     &format!("{}{}", &config.mq.amqpaddr, &config.messagecenter.vhost),
        //     tx_user_notify,
        // )
        // .await;

        // //连接消息中心服务
        // let queue_name = format!("phoenix_account_risk_client_queue_{}", std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs());
        // // let queue_name = format!("phoenix_accountrisk_center-{}", rand::thread_rng().sample_iter(&rand::distributions::Alphanumeric).take(10).map(char::from).collect::<String>());
        // // info!("accountrisk mq queue:{}", &queue_name);
        // let notification_client = NotificationClient::new(
        //     config.messagecenter.exchanger.as_str(),
        //     &queue_name,
        //     config.messagecenter.key.clone(),
        //     &format!("{}{}", &config.mq.amqpaddr, &config.messagecenter.vhost),
        //     tx_notification,
        // )
        // .await;

        // // let mut notification_client_clone = notification_client.clone();
        // messagecenter::init::init_notification_client(Arc::new(RwLock::new(notification_client.clone()))).await;
        // messagecenter::init::init_notification_listen(Arc::new(RwLock::new(notification_client.clone()))).await;

        let stub = PhoenixController {
            dbconn: Arc::new(dbconn.to_owned()),
            // basedata_svc: Arc::new(basedata_svc),
            account_position_svc: Arc::new(account_position_svc),
            account_assets_svc: Arc::new(account_assets_svc),
            aka_svc: Arc::new(aka_svc),
            sat_client: Arc::new(sat_client),
            redis_client: Arc::new(redis_client),
            uidsvc,
            setting: Arc::new(RwLock::new(config.clone())),
            sys_info: Arc::new(RwLock::new(sys_info)),
            tx_persist,
            ignore_accounts,
            // ignore_fee_accounts,
            user_assets_svc: Arc::new(userassetssvc),
            user_position_svc: Arc::new(userpositionsvc),
            securities_borrow_pool: Arc::new(securities_borrow_pool),
            basic_cache_svc,
            tx_assets,
            tx_rq,
            assetscenter_svc,
            manager_svc,
            notify: Arc::new(notification_client),
        };

        //初始化数据
        stub.init(true).await.expect("init main controller error");

        //连接行情服务
        let routingkeys: HashMap<String, i32> = stub.get_stock_positions_quotation_key().await;
        // let mut routingkeys: HashMap<String, i32> = HashMap::new();
        // routingkeys.insert("stock.#".to_string(), 1);
        let quotation_queue_name = format!("phoenix_account_riskcenter_quotaion_{}", timeutil::current_timestamp());
        let quotation_client = QuotationClient::new(
            config.quotation.stocklive_exchanger.as_str(),
            &quotation_queue_name,
            routingkeys,
            &format!("{}{}", &config.mq.amqpaddr, &config.quotation.vhost),
            tx_quotation,
        )
        .await;
        //clone one, for rest if necessary
        let mut quotion_clone_client = quotation_client.clone();
        messagecenter::init::init_quotation_listen(quotation_client).await;

        let stub = Arc::new(stub);

        let ret = ServerHandler {
            task_dispacther: tx,
            set_close: Some(tx_close),
            stub: stub.clone(),
        };

        let stub_for_dispatch = stub.clone();
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    may_task = rx.recv() => {
                        // info!("my task received...");
                        if let Some(task) = may_task{
                                // info!("my task received...{:#?}",&task);
                                task(stub_for_dispatch.clone()).await;
                        }
                    }
                    _ = &mut rx_close => {
                        info!("Server scheduler is notified to close");
                        rx.close();
                        break;
                    }
                }
            }

            // drain unhandled task
            while let Some(task) = rx.recv().await {
                task(stub_for_dispatch.clone()).await;
                info!("drain unhandled task received");
            }

            warn!("Server scheduler has exited");
        });

        let stub_clone = stub.clone();
        let atomic_last_price_tmp = atomic_last_price.clone();
        tokio::spawn(async move {
            persist_interval.tick().await; //skip first tick
            position_interval.tick().await;
            loop {
                tokio::select! {
                    _ = position_interval.tick()=> {
                      //定时计算浮动盈亏
                        if atomic_last_price_tmp.load(Ordering::Relaxed) {
                            //计算浮动盈亏，交易账户和分帐户
                            // info!("持仓最新价发生变化,计算持仓市值和盈亏等数据");
                            if let Ok(()) = stub_clone.update_assets_by_position().await{
                                atomic_last_price_tmp.store(false, Ordering::Relaxed);
                                if !*atomic_assets_persist.get_mut(){
                                    atomic_assets_persist.store(true, Ordering::Relaxed);//可以保存到数据库
                                }
                            }
                        }
                    }
                    _ = persist_interval.tick() => {
                         // 定时持久化数据,包括用户账户，分帐户的资产和持仓
                         if *atomic_assets_persist.get_mut() {
                            atomic_assets_persist.store(false, Ordering::Relaxed);//数据持久化完成
                            // info!("持仓市值和盈亏等数据已经发生变化,定时保存到数据库");
                            if let Err(e) = stub_clone.persist_data_interval().await{
                                error!("{e}");
                            }
                            //定时持久化持仓最新价
                            _=stub_clone.phoenix_positions_price_change().await;
                        }
                    }
                    // _=unitsrisk_interval.tick()=>{
                    //   info!("定时16:30持久化风控动态数据");
                    //   _=stub_clone.interval_save_unit_risk_data().await;
                    // }
                }
            }
        });

        let stub_clone = stub.clone();
        let atomic_last_price_tmp = atomic_last_price.clone();
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    rx_unitid = rx_assets.recv() => {
                      //重算用户资产信息
                        if let Some((unit_id, user_id))=rx_unitid{
                            info!("重算用户资产信息 rx_assets.recv,unitid:{} userid:{}",unit_id, user_id);
                            _=stub_clone.re_calculate_user_assets(unit_id, user_id, true).await;
                        }
                    }
                    rx_info = rx_rq.recv() => {
                        if let Some((stock_id, unit_id, use_credit))=rx_info{
                            info!("更新信息更新,stockid:{} unitid:{} use credit:{}",stock_id, unit_id, use_credit);
                            if let Err(e) = stub_clone.update_rq_and_check(stock_id, unit_id, use_credit).await {
                            error!("{}", e);
                            }
                        }
                    }
                    rx_info = rx_last_price.recv() => {
                        if let Some((stock_id, last_price))=rx_info{
                            // info!("rx_last_price.recv,{} {}",&stock_id, last_price);

                            //计算用户账户的数据
                            let stockid = stub_clone.basic_cache_svc.get_stockid(&stock_id, &stub_clone.aka_svc).await;
                            if stockid.is_ok() {
                                // stub_clone.set_stock_price(&quotation.commodity_no,&quotation.exchange_id,quotation.q_last_price).await;
                                //更新最新价，包括分帐户的用户账户
                                if let Err(e) = stub_clone.update_stock_positions_lastprice_by_quotation(stockid.unwrap(), last_price).await {
                                    error!("{e}");
                                }
                                if !atomic_last_price_tmp.load(Ordering::Relaxed) {
                                    atomic_last_price_tmp.store(true, Ordering::Relaxed); //数据已经更新,可以重算资产
                                }
                            } else {
                                error!("{}", stockid.unwrap_err());
                            }
                        }
                    }
                }
            }
        });

        let stub_clone = stub.clone();
        tokio::spawn(async move {
            loop {
                if let Some(persist_data) = rx_persist.recv().await {
                    debug!("persist command received *************:{:?}", &persist_data);
                    let _ = stub_clone.persist_data(&persist_data).await;
                }
            }
        });

        // let stub_clone = stub.clone();
        tokio::spawn(async move {
            loop {
                // 接收到行情，需要处理：
                // 1)更新所有用户持仓的最新价，并计算浮动盈亏
                // 2)更新所有分账户持仓数据的最新价和浮动盈亏（主要是根据stockid和channelid）
                if let Ok(quotation) = rx_quotation.recv().await {
                    let newval = quotation.q_last_price;
                    let mut val_price = 0.0;
                    let val_price_cache = price_cache.get(&quotation.contract_no1);
                    if !val_price_cache.is_none() {
                        val_price = val_price_cache.unwrap().to_owned();
                    }
                    //覆盖缓存最新价
                    price_cache.insert((&quotation.contract_no1).clone(), newval);

                    if newval != val_price {
                        // info!("股票代码: {} 变更最新价: {}", &quotation.contract_no1, newval);

                        if let Err(e) = tx_last_price.send((quotation.contract_no1, newval)).await {
                            error!("{e}");
                        }
                    }
                }
            }
        });

        let atomic_last_price_tmp = atomic_last_price.clone();
        let stub_clone = stub.clone();

        tokio::spawn(async move {
            loop {
                //包括：订单信息，资金调整消息
                // 1) 更新用户账户的资产数据和持仓数据
                // 2) 更新分账户的资产数据和持仓数据
                if let Some(message) = rx_notification.recv().await {
                    info!("notification received with message:{:?}", &message);
                    if let Some(message_body) = message.msg_body.to_owned() {
                        match message.msg_type() {
                            NotificationType::AssetChanged => {
                                if let Some(msg_asset) = &message_body.msg_asset {
                                    info!("资产变化（变化后的结果数据）,{:?}", msg_asset);
                                    stub_clone.update_user_assets_by_notification(&msg_asset).await;
                                }
                            }
                            NotificationType::PositionChanged => {
                                if let Some(msg_position) = &message_body.msg_position {
                                    info!("持仓发生变化:{:?}", msg_position);
                                    let time1 = timeutil::current_timestamp_mills();
                                    stub_clone.update_user_position_by_notification(&msg_position).await;
                                    let time2 = timeutil::current_timestamp_mills();
                                    info!("持仓变化处理时间{}毫秒", (time2 - time1));
                                }
                            }
                            NotificationType::StockInfoChanged => {
                                if let Some(msg_stockinfo) = &message_body.msg_stockinfo {
                                    info!("品种信息发生变化,{:?}", msg_stockinfo);
                                    stub_clone.set_stock_margin_rate(msg_stockinfo.stock_id).await;
                                }
                            }
                            NotificationType::UserStockMarginChanged => {
                                if let Some(msg_userstockmargin) = &message_body.msg_userstockmargin {
                                    info!("用户品种保证金发生变化,{:?}", msg_userstockmargin);
                                    stub_clone.set_unit_stock_margin_rate(&msg_userstockmargin).await;
                                }
                            }
                            NotificationType::AccountInfoChanged => {
                                if let Some(msg_accountinfo) = &message_body.msg_accountinfo {
                                    info!("用户信息发生变化,{:?}", msg_accountinfo);

                                    let flag = stub_clone.is_changed(&msg_accountinfo).await;
                                    if flag {
                                        stub_clone.set_user_trade_flag(&msg_accountinfo).await;
                                        stub_clone.set_unit_credit_multiple(&msg_accountinfo).await;
                                    } else {
                                        info!("不需要变化");
                                    }
                                }
                            }
                            NotificationType::ExchangeRateChanged => {
                                if let Some(msg_exchangerate) = &message_body.msg_exchangerate {
                                    info!("汇率发生变化,{:?}", msg_exchangerate);
                                    stub_clone.set_stock_position_sell_rate(&msg_exchangerate).await;
                                }
                            }
                            NotificationType::StockSuspensionChanged => {
                                if let Some(msg_stocksuspension) = &message_body.msg_stocksuspension {
                                    info!("停牌信息发生变化,{:?}", msg_stocksuspension);
                                    stub_clone.set_stock_suspension(&msg_stocksuspension).await;
                                }
                            }
                            NotificationType::OrderExecMsg => {
                                if let Some(order) = &message_body.msg_orderexec {
                                    info!("订单执行回报消息,{:?}", order);
                                    if order.exec_type != OrderExecType::UndefExecType as i32 {
                                        if order.channel_type != 2 {
                                            //过滤兜底通道
                                            //更新资产和持仓信息
                                            let ret = stub_clone.handle_assets_by_dealinfo(&order).await;
                                            if ret.is_ok() {
                                                if order.exec_type == OrderExecType::NewOrder as i32 {
                                                    //新订单，需要处理行情
                                                    let new_keys = stub_clone.get_stock_positions_quotation_key().await;
                                                    let _ = quotion_clone_client.update_bindings(&new_keys).await;
                                                }

                                                if !atomic_last_price_tmp.load(Ordering::Relaxed) {
                                                    atomic_last_price_tmp.store(true, Ordering::Relaxed);
                                                    //数据已经更新,可以重算资产
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            NotificationType::Settlement => {
                                info!("收到结算消息，开始进行分帐户结算");
                                //结算完毕重新刷新系统时间
                                if let Some(data) = &message_body.msg_settlement {
                                    if data.settle_status == 1 {
                                        info!("结算开始，将分账户缓存保存到数据库");
                                        if let Err(e) = stub_clone.persist_data_interval().await {
                                            error!("{e}");
                                        }
                                    } else if data.settle_status == 2 {
                                        info!("从数据库重制缓存");
                                        if let Err(e) = stub_clone.init(false).await {
                                            error!("重新初始化缓存数据失败：{}", e);
                                        }
                                        info!("刷新系统时间");
                                        stub_clone.refresh_system_date().await;
                                        info!("开始分账户结算");
                                        let _ = stub_clone.resettle_assets().await;
                                        info!("持久化风控记录");
                                        _ = stub_clone.interval_save_unit_risk_data().await;
                                    }
                                }
                            }
                            NotificationType::DvdSettle => {
                                if let Some(data) = &message_body.msg_dvdsettle {
                                    info!("收到权益分派消息");
                                    if data.status == 2 {
                                        info!("权益分派结束，重新初始化缓存数据");
                                        if let Err(e) = stub_clone.init(true).await {
                                            error!("重新初始化缓存数据失败：{}", e);
                                        }
                                    }
                                }
                            }
                            NotificationType::RqNotify => {
                                if let Some(_msg_rq) = &message_body.msg_rq {
                                    info!("收到融券额度发生变化的消息通知，重新初始化融券额度");
                                    if let Err(e) = stub_clone.reset_securities_borrow_pool().await {
                                        error!("重制融券池失败：{}", e);
                                    }
                                }
                            }
                            NotificationType::PositionChannelInitNotify => {
                                if let Some(_msg_rq) = &message_body.msg_postionchannel_init {
                                    info!("分帐户持仓需要重新初始化");
                                    if let Err(e) = stub_clone.init_position_channel().await {
                                        error!("重制融券池失败：{}", e);
                                    }
                                }
                            }
                            //收到：用户品种保证金比率变化消息，品种保证金变化消息，停牌消息，融资杠杆变化消息
                            //重新计算用户保证金变化
                            // NotificationType::用户品种保证金比率 =>{
                            //   stub_clone.check_margin_rate(unitid, stockid, channelid)
                            // }
                            _ => {
                                error!("unhandled message type:{}", message.msg_type,);
                            }
                        }
                    } else {
                        error!("message body is empty");
                    }
                }
            }
        });

        ret
    }
    pub fn on_leave(&mut self) -> ServerLeave {
        // self.stub.persist_to_database(constant::VALUE_ALL).await;
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

//这里实现grpc的接口
#[tonic::async_trait]
impl AccountRiskCenter for ServerHandler {
    //查询用户风险率
    async fn query_margin_ratio(&self, request: tonic::Request<MarginRatioReq>) -> Result<tonic::Response<MarginRatioResp>, tonic::Status> {
        let req = request.into_inner();
        info!("query_margin_ratio 收到的请求:{:?}", &req);

        let ret = self.stub.query_margin_ratio(&req).await?;
        info!("返回请求:{:?}", &ret);
        let r = Response::new(ret);
        Ok(r)
    }

    async fn query_user_positions(&self, request: tonic::Request<UserPositionReq>) -> Result<tonic::Response<UserPositionResp>, tonic::Status> {
        let mut req = request.into_inner();
        info!("query_user_positions 收到的请求:{:?}", &req);

        let ret = self.stub.query_user_positions(&mut req).await?;
        info!("返回请求:{:?}", &ret);
        let r = Response::new(ret);
        Ok(r)
    }

    //查询用户资产
    async fn query_user_assets(&self, request: tonic::Request<UserAssetsReq>) -> Result<tonic::Response<UserAssetsResp>, tonic::Status> {
        let req = request.into_inner();
        info!("query_user_assets 收到的请求:{:?}", req);
        let ret = self.stub.query_user_asset(&req).await?;
        info!("返回请求:{:?}", &ret);
        let r = Response::new(ret);
        Ok(r)
    }

    async fn query_stock_positions(&self, request: tonic::Request<PhoenixStockPositionRequest>) -> Result<tonic::Response<PhoenixStockPositionResponse>, tonic::Status> {
        let req = request.into_inner();
        info!("query_stock_positions 收到的请求:{:#?}", &req);

        let ret = self.stub.query_stock_positions(&req).await;
        match ret {
            Ok(v) => {
                let res = PhoenixStockPositionResponse {
                    ret_code: errors::get_error_code(ErrorCode::CodeOk).0,
                    ret_msg: errors::get_error_code(ErrorCode::CodeOk).1,
                    data: v,
                    user_total_positions: 0, // 闲置
                };
                info!("返回股票持仓信息:{:?}", &res);
                Ok(Response::new(res))
            }
            Err(e) => {
                let res = PhoenixStockPositionResponse {
                    ret_code: errors::get_error_code(ErrorCode::CodeUnknown).0,
                    ret_msg: e.to_string(),
                    data: vec![],
                    user_total_positions: 0,
                };
                info!("返回股票持仓信息:{:#?}", &res);
                Ok(Response::new(res))
            }
        }
    }

    async fn query_account_assets(&self, request: tonic::Request<PhoenixAccountQueryRequest>) -> Result<tonic::Response<PhoenixAssetsResponse>, tonic::Status> {
        let req = request.into_inner();
        info!("查找总账户风控信息收到的请求......{:?}", &req);
        // info!("query request: {:#?}", &req);
        let ret = self.stub.query_account_assets(&req).await;
        // info!("result of PhoenixAccountAssetsInfo:{:?}", &ret);
        match ret {
            Ok(v) => {
                let res = PhoenixAssetsResponse {
                    ret_code: errors::get_error_code(ErrorCode::CodeOk).0,
                    ret_msg: errors::get_error_code(ErrorCode::CodeOk).1,
                    data: v,
                };
                info!("返回查找总账户资产的请求:{:#?}", &res);
                Ok(Response::new(res))
            }
            Err(e) => {
                let res = PhoenixAssetsResponse {
                    ret_code: errors::get_error_code(ErrorCode::CodeUnknown).0,
                    ret_msg: e.to_string(),
                    data: vec![],
                };
                info!("返回查找总账户资产的请求:{:#?}", &res);
                Ok(Response::new(res))
            }
        }
    }

    ///资金划转
    async fn transfer_fund(&self, request: tonic::Request<PhoenixTransferRequest>) -> Result<tonic::Response<PhoenixAssetsResponse>, tonic::Status> {
        let req = request.into_inner();
        info!("资金划转收到的请求:{:#?}", &req);
        match self.stub.transfer_fund(&req).await {
            Ok(res) => {
                info!("返回请求:{:#?}", &res);
                Ok(Response::new(res))
            }
            Err(err) => {
                let res = PhoenixAssetsResponse {
                    ret_code: errors::get_error_code(ErrorCode::CodeUnknown).0,
                    ret_msg: err.to_string(),
                    data: vec![],
                };
                info!("返回请求:{:#?}", &res);
                Ok(Response::new(res))
            }
        }
    }

    ///分帐户reset动作
    async fn reset_profit(&self, request: tonic::Request<PhoenixAccountResetRequest>) -> Result<tonic::Response<PhoenixAssetsResponse>, tonic::Status> {
        let req = request.into_inner();

        info!("reset_profit 收到的请求:{:?}", &req);

        match self.stub.reset_profit(req.account_id, &req).await {
            Ok(_) => {
                let res = PhoenixAssetsResponse {
                    ret_code: errors::get_error_code(ErrorCode::CodeOk).0,
                    ret_msg: errors::get_error_code(ErrorCode::CodeOk).1,
                    data: vec![],
                };
                info!("返回reset结果:{:?}", &res);
                Ok(Response::new(res))
            }
            Err(e) => {
                let res = PhoenixAssetsResponse {
                    ret_code: errors::get_error_code(ErrorCode::CodeUnknown).0,
                    ret_msg: e.to_string(),
                    data: vec![],
                };
                info!("返回reset结果:{:?}", &res);
                Ok(Response::new(res))
            }
        }
    }

    async fn query_user_rq(&self, request: Request<UserRqReq>) -> Result<Response<UserRqResp>, Status> {
        let req = request.into_inner();

        info!("query_user_rq 收到的请求: {:?}", &req);

        match self.stub.query_user_rq(&req).await {
            Ok(res) => {
                info!("返回返回融券额度结果:{:?}", &res);
                Ok(Response::new(res))
            }
            Err(e) => {
                let res = UserRqResp {
                    ret_code: errors::get_error_code(ErrorCode::CodeUnknown).0,
                    ret_msg: e.to_string(),
                    rqlist: vec![],
                };
                error!("返回返回融券额度结果:{:?}", &res);
                Ok(Response::new(res))
            }
        }
    }

    async fn compute_assets_from_deal(&self, request: Request<PhoenixAssetscomputeRequest>) -> Result<Response<PhoenixAssetscomputeResponse>, Status> {
        let req = request.into_inner();
        match self
            .stub
            .compute_assets_from_deal(
                req.unit_id,
                req.cur_date as i32,
                req.before_date as i32,
                Decimal::from_f64_retain(req.rate_buy).unwrap_or_default(),
                Decimal::from_f64_retain(req.rate_sell).unwrap_or_default(),
            )
            .await
        {
            Ok(_) => {
                let res = PhoenixAssetscomputeResponse {
                    ret_code: errors::get_error_code(ErrorCode::CodeOk).0,
                    ret_msg: errors::get_error_code(ErrorCode::CodeOk).1,
                };
                info!("返回请求:{:#?}", &res);
                Ok(Response::new(res))
            }
            Err(err) => {
                let res = PhoenixAssetscomputeResponse {
                    ret_code: errors::get_error_code(ErrorCode::CodeUnknown).0,
                    ret_msg: err.to_string(),
                };
                info!("返回请求:{:#?}", &res);
                Ok(Response::new(res))
            }
        }
    }

    async fn query_account_available_amount(&self, request: Request<AccountAvailableAmountRequest>) -> Result<Response<AccountAvailableAmountResponse>, Status> {
        let req = request.into_inner();

        info!("query_account_available_amount 收到的请求: {:?}", &req);

        match self.stub.query_account_available_amount(req.stock_id).await {
            Ok(res) => {
                info!("返回可卖量为:{:?}", &res);
                Ok(Response::new(AccountAvailableAmountResponse {
                    ret_code: errors::get_error_code(ErrorCode::CodeOk).0,
                    ret_msg: errors::get_error_code(ErrorCode::CodeOk).1,
                    account_available_amount: res,
                }))
            }
            Err(e) => {
                let res = AccountAvailableAmountResponse {
                    ret_code: errors::get_error_code(ErrorCode::CodeUnknown).0,
                    ret_msg: e.to_string(),
                    account_available_amount: 0,
                };
                error!("返回返回融券额度结果:{:?}", &res);
                Ok(Response::new(res))
            }
        }
    }

    // async fn compute_dvd_register(&self, request: Request<PhoenixAssetscomputeRequest>) -> Result<Response<PhoenixAssetscomputeResponse>, Status> {
    //   let req = request.into_inner();
    //   match self.stub.compute_dvd_register(req.before_date as i32, req.cur_date as i32,
    //                                            Decimal::from_f64_retain(req.rate_buy).unwrap_or_default(),
    //                                            Decimal::from_f64_retain(req.rate_sell).unwrap_or_default()).await {
    //     Ok(_) => {
    //       let res = PhoenixAssetscomputeResponse {
    //         ret_code: errors::get_error_code(ErrorCode::CodeOk).0,
    //         ret_msg: errors::get_error_code(ErrorCode::CodeOk).1,
    //       };
    //       info!("返回请求:{:#?}", &res);
    //       Ok(Response::new(res))
    //     }
    //     Err(err) => {
    //       let res = PhoenixAssetscomputeResponse {
    //         ret_code: errors::get_error_code(ErrorCode::CodeUnknown).0,
    //         ret_msg: err.to_string(),
    //       };
    //       info!("返回请求:{:#?}", &res);
    //       Ok(Response::new(res))
    //     }
    //   }
    // }
}
