[package]
name = "phoenix_assetscenter"
version = "0.2.0"
edition = "2021"
description = "资产中心，包括用户资产，分帐户资产 build time: 2025-07-07"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html


[dependencies]
akaclient = { workspace = true }
common = { workspace = true }
utility = { workspace = true }
messagecenter = { workspace = true }
protoes = { workspace = true }

tonic = { workspace = true }
prost = { workspace = true }

futures-core = { workspace = true }
futures-util = { workspace = true }
tokio = { workspace = true }
tokio-stream = { workspace = true }
futures = { workspace = true }
# async-stream = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
rust_decimal = { workspace = true }

# log = { workspace = true }
# log4rs = { workspace = true }
sea-orm = { workspace = true }

rand = { workspace = true }

anyhow = { workspace = true }
lazy_static = { workspace = true }
config = { workspace = true }
moka = { workspace = true }

tracing = { workspace = true }

# [build-dependencies]
# # tonic-build = { workspace = true, features = ["prost"] }
# tonic-build.workspace = true
