use crate::dataservice::{
    dbsetup::DbConnection,
    entities::{
        prelude::{SysCommodityChannel, SysCommodityChannelEntity},
        sys_commodity_channel,
    },
};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, Condition, DbErr, EntityTrait, QueryFilter};

impl sys_commodity_channel::Model {
    pub async fn find_by_user_id_and_commodity_id(db: &DbConnection, user_id: i64, commodity_id: i64) -> Result<Option<SysCommodityChannel>> {
        let ret_data: Result<Option<SysCommodityChannel>, DbErr> = SysCommodityChannelEntity::find()
            .filter(sys_commodity_channel::Column::UserId.eq(user_id))
            .filter(sys_commodity_channel::Column::CommodityId.eq(commodity_id))
            .filter(sys_commodity_channel::Column::ChannelId.ne(16))
            .one(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        let data = ret_data.unwrap();
        Ok(data)
    }

    pub async fn find_by_user_id_and_commodity_id_(db: &DbConnection, user_id: i64, commodity_id: i64) -> Result<Option<SysCommodityChannel>> {
        let ret_data: Result<Option<SysCommodityChannel>, DbErr> = SysCommodityChannelEntity::find()
            .filter(sys_commodity_channel::Column::UserId.eq(user_id))
            .filter(sys_commodity_channel::Column::CommodityId.eq(commodity_id))
            .filter(sys_commodity_channel::Column::ChannelId.eq(0))
            .one(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        let data = ret_data.unwrap();
        Ok(data)
    }

    // 通道保证金设置
    pub async fn find_by_commodity_id_and_channel(db: &DbConnection, commodity_id: i64, channel_id: i64) -> Result<Option<SysCommodityChannel>> {
        let ret_data: Result<Option<SysCommodityChannel>, DbErr> = SysCommodityChannelEntity::find()
            .filter(sys_commodity_channel::Column::UserId.eq(0))
            .filter(sys_commodity_channel::Column::CommodityId.eq(commodity_id))
            .filter(sys_commodity_channel::Column::ChannelId.eq(channel_id))
            .one(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        let data = ret_data.unwrap();
        Ok(data)
    }

    pub async fn find_by_condition(db: &DbConnection, condition: Condition) -> Result<Vec<SysCommodityChannel>> {
        let ret_data: Result<Vec<SysCommodityChannel>, DbErr> = SysCommodityChannelEntity::find()
            .filter(sys_commodity_channel::Column::UserId.eq(0))
            .filter(condition)
            .all(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }
}
