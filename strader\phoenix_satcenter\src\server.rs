pub use super::controller::*;

// use crate::client::AccountRiskClient;
use crate::config::settings::Settings;
use crate::dataservice::dbsetup;
use crate::service::modify_rq;
use accountriskcenterclient::AccountRiskClient;
use akaclient::akaclient::{AkaCacheOption, AkaClient};
use common::logclient::*;
use messagecenter::notificationclient::NotificationClient;
use protoes::phoenixnotification::{MessageBody, NotificationMessage, NotificationType, RqChange, RqNotifyChange};
use protoes::phoenixsatcenter::sat_center_server::SatCenter;
use protoes::phoenixsatcenter::{ModifyRqReq, ModifyRqResp};
use std::pin::Pin;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::{mpsc, oneshot, RwLock};
use tokio::time::Instant;
use tonic::{self, Request, Response, Status};
use tracing::*;
use utility::errors;
use utility::errors::ErrorCode;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;
type StubType = Arc<ServerController>;
pub struct ServerHandler {
    stub: StubType,
    task_dispacther: mpsc::Sender<ControllerAction>,
    set_close: Option<oneshot::Sender<()>>,
}

pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);

impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

impl ServerHandler {
    pub async fn new(settings: &Settings) -> Self {
        info!("start to init server handler......");
        // 定时器  每天早上9点开始启动 去生效合约
        let start_time = Instant::now() + Duration::from_secs(utility::timeutil::get_first_interval_time(&settings.system.unitrisk_interval_start));
        let mut persist_interval = tokio::time::interval_at(start_time, std::time::Duration::from_secs(settings.system.timedtask));
        // 定时器  每天早上9点05开始启动 去判断合约是否需要召回
        let start_time = Instant::now() + Duration::from_secs(utility::timeutil::get_first_interval_time(&settings.system.unitrisk_interval_start_re_call));
        let mut persist_interval_re_call = tokio::time::interval_at(start_time, std::time::Duration::from_secs(settings.system.timedtask));

        let (tx, mut rx) = mpsc::channel::<ControllerAction>(16);
        let (tx_close, mut rx_close) = oneshot::channel();
        let (tx_notification, mut rx_notification) = tokio::sync::mpsc::channel::<NotificationMessage>(1024);
        let (tx_rq_notify, mut rx_rq_notify) = tokio::sync::mpsc::channel::<Vec<RqChange>>(1024);
        //连接消息中心服务

        let queue_name = format!("phoenix_satcenter_notification_{}", utility::timeutil::current_timestamp());
        let notification_client = Arc::new(RwLock::new(
            NotificationClient::new(
                &settings.notification.notification_exchanger,
                &queue_name,
                settings.notification.satcenter_routing_key.clone(),
                &format!("{}{}", &settings.mq.amqpaddr, &settings.notification.vhost),
                tx_notification,
            )
            .await,
        ));
        // info!("start to init notification......");
        messagecenter::init::init_notification_client(notification_client.clone()).await;
        messagecenter::init::init_notification_listen(notification_client.clone()).await;
        let notification_client_clone = notification_client.clone();

        let mut akacache = AkaCacheOption::default();
        akacache = AkaCacheOption {
            use_cache: settings.system.cacheflag,
            mq_uri: format!("{}{}", &settings.mq.amqpaddr, &settings.notification.vhost),
            exchange: settings.notification.notification_exchanger.to_string(),
            routing_keys: settings.notification.satcenter_routing_key.to_string(),
            ..akacache.clone()
        };
        info!("缓存开关:{:?}", &akacache);
        let akacenterconn = AkaClient::init(settings.servers.akacenterserver.to_string(), &akacache).await;
        let aka_copy = akacenterconn.clone();
        let aka_copy2 = akacenterconn.clone();
        // let account_risk_client = AccountRiskClient::new(&settings.servers.accountriskserver).await;

        let account_riskcenter_url = settings.servers.accountriskserver.clone();
        // let account_risk_client = AccountRiskClient::init(account_riskcenter_url).await;
        // let mut account_copy = account_risk_client.clone();
        let mut account_copy = AccountRiskClient::init(account_riskcenter_url).await;

        // info!("start to init database connection......");
        let finances_dbconn = dbsetup::DbConnection::new(&settings.database.finances_uri).await;
        let stock_dbconn = dbsetup::DbConnection::new(&settings.database.stock_uri).await;
        let finances_dbconn_rq = finances_dbconn.clone();
        let finances_dbconn_rq_2 = finances_dbconn.clone();
        let stock_dbconn_clone = stock_dbconn.clone();

        let tx_grpc = tx_rq_notify.clone();
        let stub = ServerController {
            finances_dbconn: finances_dbconn,
            stock_dbconn: stock_dbconn,
            // _account_client: account_risk_client,
            aka_client: akacenterconn,
            tx: tx_grpc,
        };

        let stub = Arc::new(stub);

        let stub_for_dispatch = stub.clone();
        let stub_clone = stub.clone();
        let svr_handler = ServerHandler {
            stub,
            task_dispacther: tx,
            set_close: Some(tx_close),
        };

        // info!("start to init tokio task......");
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    may_task = rx.recv() => {
                        if let Some(task) = may_task{
                            task(stub_for_dispatch.clone()).await;
                        }
                    },
                    _ = &mut rx_close => {
                        info!("Server scheduler is notified to close");
                        rx.close();
                        break;
                    }
                }
            }

            //drain unhandled task
            while let Some(task) = rx.recv().await {
                task(stub_for_dispatch.clone()).await;
            }

            warn!("Server scheduler has exited");
        });

        //收到结算通知开始恢复融券额度
        let set_tx = tx_rq_notify.clone();
        tokio::spawn(async move {
            loop {
                if let Some(message) = rx_notification.recv().await {
                    info!("notification received:{:?}", &message);
                    if let Some(message_body) = message.msg_body.to_owned() {
                        match message.msg_type() {
                            NotificationType::Settlement => {
                                info!("收到结算消息，开始进行融券额度结算");
                                if let Some(data) = &message_body.msg_settlement {
                                    if data.settle_status == 2 {
                                        let set_tx = set_tx.clone();
                                        if let Err(e) = stub_clone.rq_settle(set_tx).await {
                                            error!("{}", e);
                                        }
                                        //推送融券额度变化通知
                                    }
                                }
                            }
                            _ => {
                                error!("unhandled message type:{}", message.msg_type);
                            }
                        }
                    } else {
                        error!("message body is empty");
                    }
                }
            }
        });

        //定时任务 去扫描融券合约
        let rq_tx = tx_rq_notify.clone();
        tokio::spawn(async move {
            loop {
                persist_interval.tick().await;
                let aka_copy = aka_copy.clone();
                info!("开始扫描,判断生效日期,将合约生效!,{:?}", utility::timeutil::current_timestamp());
                let rq_tx = rq_tx.clone();
                let _ = modify_rq::check_valid_rq(&finances_dbconn_rq.dbconn, rq_tx, aka_copy).await;
            }
        });

        //做了一个延迟 去扫描失效的合约
        let re_call_tx = tx_rq_notify.clone();
        tokio::spawn(async move {
            loop {
                persist_interval_re_call.tick().await;
                let aka_copy2 = aka_copy2.clone();
                info!("开始扫描失效的合约,{:?}", utility::timeutil::current_timestamp());
                let re_call_tx = re_call_tx.clone();
                let _ = modify_rq::find_re_call(&finances_dbconn_rq_2.dbconn, &stock_dbconn_clone.dbconn, &mut account_copy, re_call_tx, aka_copy2).await;
            }
        });

        //消息推送
        tokio::spawn(async move {
            loop {
                if let Some(re) = rx_rq_notify.recv().await {
                    if !re.is_empty() {
                        let mut msg_body = MessageBody { ..Default::default() };
                        msg_body.msg_rq = Some(RqNotifyChange { rq_change: re });
                        if let Err(e) = notification_client_clone
                            .read()
                            .await
                            .try_publish(
                                &format!("notification.rq_notify"),
                                &NotificationMessage {
                                    msg_type: NotificationType::RqNotify.into(),
                                    msg_body: Some(msg_body),
                                },
                            )
                            .await
                        {
                            error!("推送失败,:{:?}", e);
                            LogClient::get().expect("get log client error").push_error(&format!("推送融券状态变更消息失败")).await;
                        }
                    }
                }
            }
        });

        // info!("server initiated successfully......");
        svr_handler
    }

    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

//这里实现grpc的接口
#[tonic::async_trait]
impl SatCenter for ServerHandler {
    async fn modify_rq(&self, request: Request<ModifyRqReq>) -> Result<Response<ModifyRqResp>, Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        let req = request.into_inner();
        if let Err(e) = self.stub.modify_rq(&req).await {
            error!("修改融券失败: {}", e);
            return Err(Status::unknown(format!("{}", e)));
        }
        let r = Response::new(ModifyRqResp {
            err_code: 0,
            err_msg: errors::get_error_code(ErrorCode::CodeOk).1,
        });
        Ok(r)
    }
}
