mod client;
mod commonutil;
mod config;
mod controller;
mod dataservice;
// mod protofiles;
mod server;
mod service;
use crate::config::settings::Settings;
use anyhow::Result;
use common::logclient::*;
use server::ServerHandler;
use tracing::*;
// use utility::loggings;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // let cfg = "config/exchanger.yaml";
    // loggings::log_init(cfg);
    let prefix = "phoenix_exchanger";
    let dir = "./log";

    let settings = Settings::new().expect("init configuration error:");
    // let level = "INFO";
    let level = &settings.system.loglevel.to_ascii_uppercase();
    let _guard = common::init_tracing(prefix, dir, level);
    info!("初始化配置信息:{:?}", &settings);

    // 1. 日志中心客户端初始化
    init_logclient(&settings.servers.logcenterserver, &format!("{}_{prefix}", &settings.notification.vhost)).await;

    // 1. 日志中心客户端初始化
    // init_logclient(&settings.servers.logcenterserver, "phoenix_exchanger").await;

    let server = prepare(&settings).await.expect("prepare server error......");

    info!(
        "Server started...... name: {} version: {} description: {}",
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    );
    log_debug(&format!(
        "Server started...... name: {} version: {} description: {}",
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    ))
    .await;

    server_run(server, &settings).await
}

async fn prepare(settings: &Settings) -> anyhow::Result<ServerHandler> {
    // let grpc_stub = create_controller(settings).await;

    let grpc = ServerHandler::new(&settings).await;

    Ok(grpc)
}

async fn server_run(mut svr: ServerHandler, _settings: &Settings) -> Result<(), Box<dyn std::error::Error>> {
    //let addr = "0.0.0.0:60000".parse().unwrap();
    //receive ctrl-c exit signal
    let (tx, rx) = tokio::sync::oneshot::channel::<()>();

    // Spawn the shutdown handler in a separate task
    let controller = svr.get_controller().clone();
    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.ok();
        info!("Ctrl-c received, shutting down");

        // Gracefully shutdown controller and all clients
        info!("Shutting down controller and clients...");
        if let Err(e) = controller.shutdown().await {
            error!("Error shutting down controller: {:?}", e);
        }

        info!("All shutdown operations completed");
        tx.send(()).ok();
    });

    // Wait for shutdown signal
    info!("Server running, waiting for shutdown signal to stop...");
    rx.await.ok();

    info!("Starting final cleanup...");

    // Shutdown server handler clients first
    if let Err(e) = svr.shutdown().await {
        error!("Error shutting down server handler: {:?}", e);
    }

    // Then call on_leave to wait for task dispatcher to close
    let on_leave = svr.on_leave();
    on_leave.leave().await;

    info!("Shutdown completed successfully");
    Ok(())
}
