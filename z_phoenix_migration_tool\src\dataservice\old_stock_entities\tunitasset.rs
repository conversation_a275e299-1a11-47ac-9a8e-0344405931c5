//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "tunitasset")]
pub struct Model {
    #[sea_orm(column_type = "Decimal(Some((8, 0)))")]
    pub l_date: Decimal,
    #[sea_orm(primary_key, auto_increment = false, column_type = "Decimal(Some((15, 0)))")]
    pub l_unit_id: Decimal,
    #[sea_orm(column_type = "Decimal(Some((8, 0)))")]
    pub l_account_id: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_begin_cash: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_current_cash: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_into_cash_remain: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_into_cash: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_withdraw_cash: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_withdraw_frozen: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_frozen_capital: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_realcash_frozen: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_prebuy_capital: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_presale_capital: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_buy_capital: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_sale_capital: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_asset_value: Decimal,
    pub vc_currency_no: String,
    #[sea_orm(column_type = "Decimal(Some((12, 2)))")]
    pub en_total_yj: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_asset_total_value: Decimal,
    #[sea_orm(column_type = "Decimal(Some((12, 2)))")]
    pub en_total_fee_qt: Decimal,
    #[sea_orm(column_type = "Decimal(Some((12, 2)))")]
    pub en_total_real_yj: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_temp_frozen_capital: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_into_cash_total: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_withdraw_cash_total: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_yesterday_jc: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_credit_cash: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_last_credit_cash: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_credit_multiple: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_real_cash: Decimal,
    pub l_interest_type: i32,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_credit_cash_today: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_cash_in_transit: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_enable_cash: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_total_profit: Decimal,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
