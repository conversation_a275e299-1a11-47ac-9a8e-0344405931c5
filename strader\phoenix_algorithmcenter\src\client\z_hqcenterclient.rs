use anyhow::{anyhow, Result};

use common::logclient::LogClient;
use protoes::hqcenter::svr_post_subscribe_hq_msg_client::SvrPostSubscribeHqMsgClient;
use protoes::hqcenter::{PreDealNumReq, TickHqReq};
use protoes::hqmsg::YsHqInfo;
use tonic::transport::Channel;
use tracing::*;

#[derive(Clone)]
pub struct HqCenterClient {
    pub client: SvrPostSubscribeHqMsgClient<Channel>,
    pub url: String,
}
impl HqCenterClient {
    #[tracing::instrument(name = "连接HqCenter", skip_all)]
    pub async fn new(url: &String) -> Self {
        loop {
            match SvrPostSubscribeHqMsgClient::connect(url.clone()).await {
                Ok(hqclient) => return Self { client: hqclient, url: url.to_owned() },
                Err(err) => {
                    error!("connect to HqCenterClient failed: {:?}", err);
                    if let Ok(log_client) = LogClient::get() {
                        log_client.push_error(&format!("HqCenterClient connect faild :{}", err.to_string())).await;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                    continue;
                }
            }
        }
    }

    pub async fn get_pre_deal_amount(&self, stock_code: &String, exchange_id: i32, time: &String) -> Result<i32> {
        let mut client = self.client.clone();
        match client
            .get_pre_deal_amount(PreDealNumReq {
                stock_code: stock_code.to_owned(),
                exchange_id,
                time: time.to_string(),
            })
            .await
        {
            Ok(val) => Ok(val.into_inner().prev_period_amount),
            Err(status) => {
                error!("hqcenter server status: {:?}", status);
                info!("try connect hqcenter server !");
                let ret = SvrPostSubscribeHqMsgClient::connect(self.url.to_owned()).await;
                if ret.as_ref().is_err() {
                    return Err(anyhow!("try connectb hqcenter server  err: {:?}", ret.as_ref().err().unwrap().to_string()));
                }
                let mut client = ret.unwrap();
                if let Ok(val) = client
                    .get_pre_deal_amount(PreDealNumReq {
                        stock_code: stock_code.to_owned(),
                        exchange_id,
                        time: time.to_string(),
                    })
                    .await
                {
                    return Ok(val.into_inner().prev_period_amount);
                }
                return Err(anyhow!("hqcenter server status: {:?}", status));
            }
        }
    }

    pub async fn post_tick_hq(&self, stock_code: &String) -> Result<YsHqInfo> {
        let mut client = self.client.clone();
        match client
            .post_tick_hq(TickHqReq {
                strcontractno: stock_code.to_string(),
                iticktype: 1,
                ticktime: 0,
                realtime: 1,
            })
            .await
        {
            Ok(val) => Ok(val.into_inner().tickhqinfo.first().ok_or(anyhow!("找不到数据"))?.clone()),
            Err(status) => {
                error!("hqcenter server status: {:?}", status);
                info!("try connect hqcenter server !");
                let ret = SvrPostSubscribeHqMsgClient::connect(self.url.to_owned()).await;
                if ret.as_ref().is_err() {
                    return Err(anyhow!("try connectb hqcenter server  err: {:?}", ret.as_ref().err().unwrap().to_string()));
                }
                let mut client = ret.unwrap();
                if let Ok(val) = client
                    .post_tick_hq(TickHqReq {
                        strcontractno: stock_code.to_string(),
                        iticktype: 1,
                        ticktime: 0,
                        realtime: 1,
                    })
                    .await
                {
                    return Ok(val.into_inner().tickhqinfo.first().ok_or(anyhow!("找不到数据"))?.clone());
                }
                return Err(anyhow!("hqcenter server status: {:?}", status));
            }
        }
    }
}
