// use config::{Config, ConfigError, File};
// // use notify::{DebouncedEvent, RecommendedWatcher, RecursiveMode, Watcher};
// use serde::Deserialize;
// #[derive(Debug, <PERSON>lone, Deserialize)]
// pub struct Application {
//     pub apphost: String,
//     pub appport: i32,
// }

// #[derive(Debug, Clone, Deserialize)]
// pub struct Database {
//     pub customer_uri: String,
//     pub finances_uri: String,
//     pub uri: String,
// }

// #[derive(Debug, Clone, Deserialize)]
// pub struct RedisUri {
//     pub prefix: String,
//     pub uri: String,
//     pub on_off: bool,
// }

// #[derive(Debug, Clone, Deserialize)]
// pub struct Mq {
//     pub amqpaddr: String,
// }

// #[derive(Debug, Clone, Deserialize)]
// pub struct Notification {
//     pub vhost: String,
//     pub exchanger: String,
//     pub queue_name: String,
//     pub router_key: String,
// }

// #[derive(Debug, Clone, Deserialize)]
// pub struct System {
//     // pub cachelong: u64,
//     // pub cacheshort: u64,
//     pub default_margin_rate: f64,
//     pub log_server: String,
// }

// #[derive(Debug, Clone, Deserialize)]
// pub struct Settings {
//     pub application: Application,
//     pub database: Database,
//     pub system: System,
//     pub notification: Notification,
//     pub redis: RedisUri,
//     pub mq: Mq,
//     // pub orders: Orders, /*订单的MQ*/
// }

// // lazy_static! {
// //     pub static ref SETTINGS: Settings = Settings::new().unwrap();
// // }

// impl Settings {
//     pub fn new() -> Result<Self, ConfigError> {
//         // let config_file = "config/riskcenter";
//         let s = Config::builder()
//             // Start off by merging in the "default" configuration file
//             .add_source(File::with_name("config/akacenter.toml"))
//             .add_source(File::with_name("config/common.toml"))
//             // Add in the current environment file
//             // Default to 'development' env
//             // Note that this file is _optional_
//             //.add_source(File::with_name(&format!("examples/hierarchical-env/config/{}", run_mode)).required(false))
//             // Add in a local configuration file
//             // This file shouldn't be checked in to git
//             //.add_source(File::with_name("examples/hierarchical-env/config/local").required(false))
//             // Add in settings from the environment (with a prefix of APP)
//             // Eg.. `APP_DEBUG=1 ./target/app` would set the `debug` key
//             //.add_source(Environment::with_prefix("app"))
//             // You may also programmatically change settings
//             //.set_override("database.url", "postgres://")?
//             .build()
//             .expect("build configuration error");

//         // You can deserialize (and thus freeze) the entire configuration as
//         s.try_deserialize()
//     }

// }

use common::pconfig::{Database, Mq, Notification, RedisUri, Servers};
use config::{Config, ConfigError, File};
use serde::Deserialize;

#[derive(Debug, Clone, Deserialize)]
pub struct Application {
    pub apphost: String,
    pub appport: i32,
}

#[derive(Debug, Clone, Deserialize)]
pub struct System {
    pub default_margin_rate: f64,
    pub loglevel: String,
}

#[derive(Debug, Clone, Deserialize)]
pub struct Settings {
    pub application: Application,
    pub system: System,
    //公共配置
    pub database: Database,
    pub notification: Notification,
    pub redis: RedisUri,
    pub mq: Mq,
    pub servers: Servers,
}

impl Settings {
    pub fn new() -> Result<Self, ConfigError> {
        // let config_file = "config/riskcenter";
        let s = Config::builder()
            .add_source(File::with_name("config/akacenter.toml"))
            .add_source(File::with_name("config/common.toml"))
            .build()
            .expect("build configuration error");

        s.try_deserialize()
    }
}
