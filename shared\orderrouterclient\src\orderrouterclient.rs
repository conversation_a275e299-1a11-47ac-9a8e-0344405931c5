// use crate::server::service::push_log;
use anyhow::{Result, anyhow};
use common::logclient::log_error;
use heartbeatclient::HeartbeatClient;
use protoes::phoenixordermsg::{ExecMsg, ExecType, MsgContent, MsgType, RegisterReq, RouterMsg};
use protoes::phoenixorderrouter::order_router_service_client::OrderRouterServiceClient;
use std::sync::{
    Arc,
    atomic::{AtomicBool, Ordering},
};
use std::time::Duration;
use tokio::sync::RwLock;
use tokio::sync::{broadcast, mpsc};
use tonic::transport::Channel;
use tracing::*;
use tracing::{error, info};

#[derive(Clone)]
pub struct OrderRouterClient {
    client: Arc<RwLock<Option<OrderRouterServiceClient<Channel>>>>,
    tx_order: Option<broadcast::Sender<RouterMsg>>, //收订单消息 -> 报盘
    tx_confirm: Option<mpsc::Sender<ExecMsg>>,
    tx_filled: Option<mpsc::Sender<ExecMsg>>,
    tx_canceled: Option<mpsc::Sender<ExecMsg>>,
    tx_rejected: Option<mpsc::Sender<ExecMsg>>,
    //以下是exchanger需要用到的
    register_sent: Option<Arc<AtomicBool>>,                         // 注册标记
    tx_order_from_router: Option<async_channel::Sender<RouterMsg>>, // 接收消息
    rx_repay_to_router: Option<async_channel::Receiver<RouterMsg>>, // 回复消息(转换为消费端)
    channel_ids: Option<Vec<i64>>,
    //save uri
    // uri: String,
    heartbeat: HeartbeatClient,
    shutdown_tx: broadcast::Sender<()>,
}

impl OrderRouterClient {
    pub async fn init(
        endpoint: &str,
        client_id: &str,
        service_name: &str,
        tx_order: Option<broadcast::Sender<RouterMsg>>,
        tx_confirm: Option<mpsc::Sender<ExecMsg>>,
        tx_filled: Option<mpsc::Sender<ExecMsg>>,
        tx_canceled: Option<mpsc::Sender<ExecMsg>>,
        tx_rejected: Option<mpsc::Sender<ExecMsg>>,
        // register_sent: Option<Arc<AtomicBool>>,                         // 注册标记
        tx_order_from_router: Option<async_channel::Sender<RouterMsg>>, // 接收消息
        rx_repay_to_router: Option<async_channel::Receiver<RouterMsg>>, // 回复消息(转换为消费端)
        channel_ids: Option<Vec<i64>>,
    ) -> Self {
        // 等待 HeartbeatClient 初始化（包含重試邏輯）
        // let heartbeat = HeartbeatClient::new(endpoint, client_id.to_string(), service_name.to_string()).await;
        let heartbeat = HeartbeatClient::new(endpoint, client_id.to_string(), service_name.to_string(), true).await;
        let client = Arc::new(RwLock::new(None));
        if let Ok(channel) = tonic::transport::Channel::from_shared(endpoint.to_string()) {
            if let Ok(channel) = channel.connect().await {
                let proto_client = OrderRouterServiceClient::new(channel);
                let mut wr = client.write().await;
                *wr = Some(proto_client);
            } else {
                log_error(&format!("connect to OrderRouterService failed: {:?}", endpoint)).await;
                error!("connect to OrderRouterService failed: {:?}", endpoint);
            }
        }

        let client_clone = client.clone();

        // Create shutdown channel for the background task
        let (shutdown_tx, _shutdown_rx) = broadcast::channel(1);

        // Clone values to move into the async task
        let endpoint_owned = endpoint.to_string();
        let service_name_owned = service_name.to_string();
        let heartbeat_clone = heartbeat.clone();
        let shutdown_tx_clone = shutdown_tx.clone();

        // 啟動後台任務管理業務客戶端連接
        tokio::spawn(async move {
            let mut shutdown_rx1 = shutdown_tx_clone.subscribe();
            loop {
                tokio::select! {
                    _ = shutdown_rx1.recv() => {
                        info!(service = %service_name_owned, "OrderRouterClient background task received shutdown signal. Exiting.");
                        break;
                    },
                    _ = async {
                        let mut shutdown_rx2 = shutdown_tx_clone.subscribe();
                        info!(service = %service_name_owned, endpoint = %endpoint_owned, "Attempting to connect Service client");
                        match tonic::transport::Channel::from_shared(endpoint_owned.clone()) {
                            Ok(channel) => match channel.connect().await {
                                Ok(channel) => {
                                    {
                                        if client_clone.read().await.is_none() {
                                            let proto_client = OrderRouterServiceClient::new(channel);
                                            let mut wr = client_clone.write().await;
                                            *wr = Some(proto_client);
                                            // *client_clone.write().await.unwrap() = Some(proto_client);
                                            info!(service = %service_name_owned, "Service client connected,");
                                        }
                                    } // 等待 HeartbeatClient 報告不健康，然後重試
                                    loop {
                                        tokio::select! {
                                            _ = shutdown_rx2.recv() => {
                                                info!(service = %service_name_owned, "OrderRouterClient health check received shutdown signal. Exiting.");
                                                return; // Exit the entire async block
                                            },
                                            _ = tokio::time::sleep(Duration::from_secs(1)) => {
                                                if !heartbeat_clone.is_healthy() {
                                                    break; // Exit health check loop
                                                }
                                            }
                                        }
                                    }
                                    {
                                        let mut wr = client_clone.write().await;
                                        *wr = None::<OrderRouterServiceClient<tonic::transport::Channel>>;
                                        error!(service = %service_name_owned, "Service client disconnected due to unhealthy state");
                                        log_error(&format!("Service client disconnected due to unhealthy state")).await;
                                    }
                                }
                                Err(e) => {
                                    error!(service = %service_name_owned, "Service client connection failed: {}", e);
                                    log_error(&format!("Service client connection failed: {}", e)).await;
                                }
                            },
                            Err(e) => {
                                error!(service = %service_name_owned, "Invalid endpoint: {}", e);
                                log_error(&format!("Invalid endpoint: {}", e)).await;
                            }
                        }
                        warn!(service = %service_name_owned, "Retrying Service1 client connection in 2 seconds");
                        tokio::time::sleep(Duration::from_secs(2)).await;
                    } => {}
                }
            }
            info!(service = %service_name_owned, "OrderRouterClient Connection management task terminated");
        });

        let mut register_sent = None;
        if channel_ids.is_some() {
            register_sent = Some(Arc::new(AtomicBool::new(false)));
        }
        Self {
            client: client,
            tx_order,
            tx_confirm,
            tx_filled,
            tx_canceled,
            tx_rejected,
            register_sent,
            tx_order_from_router,
            rx_repay_to_router,
            channel_ids,
            heartbeat,
            shutdown_tx,
        }
    }

    pub async fn shutdown(&self) {
        info!("Initiating graceful shutdown for OrderRouterClient");

        // Stop the heartbeat client first (like AccountRiskClient)
        info!("Stopping heartbeat client...");
        self.heartbeat.stop();

        // Send shutdown signal to background task
        if let Err(e) = self.shutdown_tx.send(()) {
            warn!("Failed to send shutdown signal: {}", e);
        } else {
            info!("Shutdown signal sent to background tasks");
        }

        // Give background tasks a moment to receive shutdown signal and release locks
        tokio::time::sleep(Duration::from_millis(100)).await;

        // Try to clear the client connection with timeout
        // This is necessary because order_routing() and order_transfer() methods
        // hold write locks for extended periods during streaming operations
        info!("OrderROuterClient, Clearing client connection...");
        let clear_result = tokio::time::timeout(Duration::from_millis(500), async {
            let mut write_guard = self.client.write().await;
            *write_guard = None;
        })
        .await;

        match clear_result {
            Ok(_) => info!("Client connection cleared successfully"),
            Err(_) => {
                warn!("Timeout while clearing client connection - this is expected if streaming operations are active");
                info!("Client will be cleaned up automatically when dropped");
            }
        }

        info!("OrderRouterClient shutdown completed");
    }

    /// Get a shutdown sender for external coordination
    /// This allows other parts of the system to trigger shutdown
    pub fn get_shutdown_sender(&self) -> broadcast::Sender<()> {
        self.shutdown_tx.clone()
    }

    pub async fn order_routing(&self) -> Result<()> {
        if self.client.read().await.is_none() {
            error!("订单路由未连接");
            return Err(anyhow!("订单路由未连接"));
        }

        if self.tx_order.is_none() || self.tx_canceled.is_none() || self.tx_confirm.is_none() || self.tx_filled.is_none() || self.tx_rejected.is_none() {
            error!("tx_order or tx_canceled or tx_confirm or tx_filled or tx_rejected is None");
            return Err(anyhow!("tx_order or tx_canceled or tx_confirm or tx_filled or tx_rejected is None"));
        }

        let mut rx = self.tx_order.as_ref().unwrap().subscribe();
        //报单消息
        let outbound = async_stream::stream! {
            loop {
                if let Ok(val) = rx.recv().await {
                    info!("推送到报盘: {:?}", &val);
                    yield val;
                }
            }
        };
        let mut wr = self.client.write().await;

        let response = match wr.as_mut().unwrap().order_routing(outbound).await {
            Ok(val) => val,
            Err(status) => {
                // self.client = None;
                *wr = None;
                error!("{:?}", status);
                log_error(&format!("OrderRouter center err code: {:?} message: {:?}", status.code(), status.message()).as_str()).await;
                return Err(anyhow!(format!("OrderRouter err")));
            }
        };

        let mut inbound = response.into_inner();
        let mut shutdown_rx = self.shutdown_tx.subscribe();

        loop {
            tokio::select! {
                _ = shutdown_rx.recv() => {
                    info!("OrderRouterClient order_routing received shutdown signal, exiting stream loop");
                    break;
                },
                inbound_result = inbound.message() => {
                    match inbound_result {
                        Ok(inbound_data) => {
                            if let Some(value) = inbound_data {
                                //回报消息
                                info!("回执: {:?}", value);
                                self.repay(&value).await;
                            } else {
                                info!("inbound data empty");
                            }
                        },
                        Err(e) => {
                            error!("Error receiving inbound message: {:?}", e);
                            break;
                        }
                    }
                }
            }
        }
        Ok(())
    }

    pub async fn repay(&self, router_msg: &RouterMsg) {
        match router_msg.msg_type() {
            MsgType::Register => {}
            MsgType::Order => {}
            MsgType::Exec => {
                if let Some(msg_content) = router_msg.msg_content.clone() {
                    if let Some(exec_msg) = msg_content.exec_msg {
                        match exec_msg.exec_type() {
                            ExecType::ExecUndef => {}
                            ExecType::Confirm => {
                                info!("订单{}确认回报: {:?}", exec_msg.order_id, exec_msg);
                                if let Err(err) = self.tx_confirm.as_ref().unwrap().send(exec_msg).await {
                                    error!("确认消息发送失败: {:?}", err);
                                    log_error(&format!("确认消息发送失败 error:{}", err.to_string())).await;
                                }
                            }
                            ExecType::Filled => {
                                info!("订单{}成交回报: {:?}", exec_msg.order_id, exec_msg);
                                if let Err(err) = self.tx_filled.as_ref().unwrap().send(exec_msg).await {
                                    error!("成交消息发送失败: {:?}", err);
                                    log_error(&format!("成交消息发送失败 error:{}", err.to_string())).await;
                                }
                            }
                            ExecType::Canceled => {
                                info!("订单{}撤单回报: {:?}", exec_msg.order_id, exec_msg);
                                if let Err(err) = self.tx_canceled.as_ref().unwrap().send(exec_msg).await {
                                    error!("撤单消息发送失败: {:?}", err);
                                    log_error(&format!("撤单消息发送失败 error:{}", err.to_string())).await;
                                }
                            }
                            ExecType::Rejected => {
                                info!("订单{}废单回报: {:?}", exec_msg.order_id, exec_msg);
                                if let Err(err) = self.tx_rejected.as_ref().unwrap().send(exec_msg).await {
                                    error!("废单消息发送失败: {:?}", err);
                                    log_error(&format!("废单消息发送失败 error:{}", err.to_string())).await;
                                }
                            }
                        }
                    }
                }
            }
            MsgType::Response => {}
        }
    }

    pub async fn order_transfer(&self, tx_ready_to_router: async_channel::Sender<RouterMsg>) -> Result<()> {
        if self.client.read().await.is_none() {
            error!("订单路由未连接");
            return Err(anyhow!("订单路由未连接"));
        }

        if self.register_sent.is_none() || self.tx_order_from_router.is_none() || self.rx_repay_to_router.is_none() {
            error!("register_sent is None");
            return Err(anyhow!("register_sent is None"));
        }

        let register_sent = Arc::clone(self.register_sent.as_ref().unwrap());
        // 连接成功后，如果还没发注册消息，则发一次
        if !register_sent.load(Ordering::SeqCst) {
            let register_msg = self.register_router().await;
            if let Err(e) = tx_ready_to_router.send(register_msg).await {
                error!("发送注册消息失败: {:?}", e);
            } else {
                register_sent.store(true, Ordering::SeqCst);
            }
        }
        let tx_order_from_router = self.tx_order_from_router.as_ref().unwrap().clone();
        let rx_repay_to_router = self.rx_repay_to_router.as_ref().unwrap().clone();

        //发送消息
        let outbound = async_stream::stream! {
            loop {
                if let Ok(val) = rx_repay_to_router.recv().await {
                    info!("推送回执到路由: {:?}", &val);
                    yield val;
                }
            }
        };

        let mut client = self.client.write().await;
        let sub_ret = client.as_mut().unwrap().order_transfer(outbound).await;
        if sub_ret.as_ref().is_err() {
            *client = None;
            let status = sub_ret.as_ref().err().unwrap();
            error!("订单路由中心连接失败: {:?}", status);
            // error!("{:?}", status);
            log_error(&format!("OrderRouter center err code: {:?} message: {:?}", status.code(), status.message()).as_str()).await;
            return Err(anyhow!(format!("OrderRouter err")));
        }
        let response = sub_ret.expect("resp error");
        let mut inbound = response.into_inner();

        //接收消息
        let mut shutdown_rx = self.shutdown_tx.subscribe();

        loop {
            tokio::select! {
                _ = shutdown_rx.recv() => {
                    info!("OrderRouterClient order_transfer received shutdown signal, exiting stream loop");
                    break;
                },
                inbound_result = inbound.message() => {
                    match inbound_result {
                        Ok(inbound_data) => {
                            if let Some(value) = inbound_data {
                                if value.msg_type == (MsgType::Order as i32) {
                                    info!("订单：{:#?}", value);
                                    if let Err(err) = tx_order_from_router.send(value).await {
                                        error!("订单发送失败: {:?}", err);
                                    }
                                }
                            } else {
                                info!("inbound data empty");
                            }
                        },
                        Err(e) => {
                            error!("Error receiving inbound message in order_transfer: {:?}", e);
                            break;
                        }
                    }
                }
            }
        }

        //连接断开跳出循环结束
        self.register_sent.as_ref().unwrap().store(false, Ordering::SeqCst); // 断开时重置
        Ok(())
    }

    pub async fn register_router(&self) -> RouterMsg {
        if self.channel_ids.is_none() {
            error!("channel_ids is None");
            return RouterMsg {
                msg_type: MsgType::Register as i32,
                msg_content: None,
                msg_id: 0,
                msg_time: 0,
            };
        }
        let channel_ids = self.channel_ids.as_ref().unwrap();
        let register = RouterMsg {
            msg_type: MsgType::Register as i32,
            msg_content: Some(MsgContent {
                register_req: Some(RegisterReq { channel_id: channel_ids.clone() }),
                order_msg: None,
                exec_msg: None,
                resp: None,
            }),
            msg_id: utility::timeutil::current_timestamp(),
            msg_time: utility::timeutil::current_timestamp(),
        };
        info!("注册路由中心: {:?}", register);
        register
    }
}
