//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "sys_dictionary")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub dictionary_no: i32,
    pub dictionary_key: i32,
    pub content: String,
    pub allow_edit: i8,
    pub sort_no: i32,
    pub remark: String,
    pub en_content: String,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
