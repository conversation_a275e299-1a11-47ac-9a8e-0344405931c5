//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: i64,
    pub mail: String,
    pub mobile: String,
    pub real_name: String,
    pub male: i8,
    pub create_date: i64,
    pub department: String,
    pub position: String,
    pub mobile_type: String,
    pub leader_name: String,
    pub create_name: String,
    pub id_card: String,
    pub card_type: i8,
    pub user_surname: String,
    pub user_name: String,
    pub grada_user_id: i64,
    pub en_real_name: String,
    pub status: i8,
    pub upload_state: i8,
    pub last_upload_date: i64,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
