//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_user_assets_his")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub did: i64,
    pub sys_date: i32,
    pub unit_id: i64,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub total_asset: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub total_position_value: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub gem_position_value: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub real_margin: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub real_cash: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 6)))")]
    pub risk_rate: Decimal,
    pub trade_state: i32,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub warning_line: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub available_cash: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub net_income: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub hold_yk: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub today_yk: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub total_yk: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub draw_frozen: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub trade_frozen_capital: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub gem_trade_frozen_capital: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub level_num: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub lastday_cash: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub today_deposit: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub today_withdrawal: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub frozencash: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub gem_margin_frozen_capital: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub close_line: Decimal,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
