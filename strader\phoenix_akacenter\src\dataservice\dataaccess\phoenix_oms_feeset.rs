use crate::dataservice::{
    dbsetup::DbConnection,
    stock_entities::{
        phoenix_oms_feeset,
        prelude::{PhoenixOmsFeeset, PhoenixOmsFeesetEntity},
    },
};
use anyhow::{anyhow, Result};
use sea_orm::{Condition, DbErr, EntityTrait, QueryFilter, QueryOrder};

impl phoenix_oms_feeset::Model {
    pub async fn _find_all_by_condition(db: &DbConnection, condition: Condition) -> Result<Vec<PhoenixOmsFeeset>> {
        let ret_data: Result<Vec<PhoenixOmsFeeset>, DbErr> = PhoenixOmsFeesetEntity::find()
            .filter(condition)
            .order_by_desc(phoenix_oms_feeset::Column::FeeType)
            .order_by_desc(phoenix_oms_feeset::Column::UnitId)
            .order_by_desc(phoenix_oms_feeset::Column::OrderDirection)
            .order_by_desc(phoenix_oms_feeset::Column::ExchangeId)
            .order_by_desc(phoenix_oms_feeset::Column::ChannelId)
            .order_by_desc(phoenix_oms_feeset::Column::StockType)
            .all(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }

    pub async fn _find_by_condition2(db: &DbConnection, condition: Condition) -> Result<Vec<PhoenixOmsFeeset>> {
        let ret_data: Result<Vec<PhoenixOmsFeeset>, DbErr> = PhoenixOmsFeesetEntity::find()
            .filter(condition)
            .order_by_desc(phoenix_oms_feeset::Column::UnitId)
            .order_by_desc(phoenix_oms_feeset::Column::OrderDirection)
            .order_by_desc(phoenix_oms_feeset::Column::ExchangeId)
            .order_by_desc(phoenix_oms_feeset::Column::ChannelId)
            .order_by_desc(phoenix_oms_feeset::Column::StockType)
            .all(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }

    #[allow(unused)]
    pub async fn find_by_condition(db: &DbConnection, condition: Condition) -> Result<Vec<PhoenixOmsFeeset>> {
        let ret_data: Result<Vec<PhoenixOmsFeeset>, DbErr> = PhoenixOmsFeesetEntity::find().filter(condition).all(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }

    #[allow(unused)]
    pub async fn find_by_id(db: &DbConnection, id: i64) -> Result<Option<PhoenixOmsFeeset>> {
        let ret_data: Result<Option<PhoenixOmsFeeset>, DbErr> = PhoenixOmsFeesetEntity::find_by_id(id).one(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }
}
