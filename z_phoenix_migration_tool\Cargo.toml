[package]
name = "phoenix_migration_tool"
version = "0.2.0"
edition = "2021"
description = "build time: 2025-04-22"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
akaclient = { workspace = true }
utility = { workspace = true }
common = { workspace = true }
messagecenter = { workspace = true }

config = { workspace = true }

serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
futures = { workspace = true }
lapin = { workspace = true }
tonic = { workspace = true }
prost = { workspace = true }
notify = { workspace = true }
rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }

anyhow = { workspace = true }
thiserror = { workspace = true }

log = { workspace = true }
sea-orm = { workspace = true }

[build-dependencies]
# tonic-build = { workspace = true, features = ["prost"] }
tonic-build.workspace = true
