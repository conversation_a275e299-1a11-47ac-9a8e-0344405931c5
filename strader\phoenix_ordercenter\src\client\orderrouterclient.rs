use crate::server::service::push_log;
use anyhow::Result;
use protoes::phoenixordermsg::{ExecMsg, ExecType, MsgType, RouterMsg};
use protoes::phoenixorderrouter::order_router_service_client::OrderRouterServiceClient;
use tokio::sync::{broadcast, mpsc};
use tonic::transport::Channel;
use tracing::{error, info};
#[derive(Clone)]
pub struct OrderRouterClient {
    pub client: Option<OrderRouterServiceClient<Channel>>,
    tx_order: broadcast::Sender<RouterMsg>, //收订单消息 -> 报盘
    tx_confirm: mpsc::Sender<ExecMsg>,
    tx_filled: mpsc::Sender<ExecMsg>,
    tx_canceled: mpsc::Sender<ExecMsg>,
    tx_rejected: mpsc::Sender<ExecMsg>,
    uri: String,
}

impl OrderRouterClient {
    pub async fn new(
        uri: &String,
        tx_order: broadcast::Sender<RouterMsg>,
        tx_confirm: mpsc::Sender<ExecMsg>,
        tx_filled: mpsc::Sender<ExecMsg>,
        tx_canceled: mpsc::Sender<ExecMsg>,
        tx_rejected: mpsc::Sender<ExecMsg>,
    ) -> Self {
        // let client = OrderRouterServiceClient::connect(uri.to_owned()).await.unwrap_or_else(|err| {
        //     error!("路由中心连接失败OrderRouterServiceClient: {}", err);
        //     // process::exit(1)
        //     panic!("路由中心连接失败OrderRouterServiceClient: {}", err);
        // });
        let mut order_router_client = OrderRouterClient {
            client: None,
            tx_order,
            tx_confirm,
            tx_filled,
            tx_canceled,
            tx_rejected,
            uri: uri.to_owned(),
        };
        let ret = OrderRouterServiceClient::connect(uri.to_owned()).await;
        if ret.as_ref().is_err() {
            push_log(format!("connect to OrderRouter failed: {:?}", uri).as_str()).await;
        } else {
            info!("订单路由连接成功....");
            order_router_client.client = Some(ret.expect("connect to OrderRouter failed"));
        }
        order_router_client
    }

    pub async fn init_client(&mut self) -> Result<OrderRouterServiceClient<Channel>> {
        if self.client.is_some() {
            return Ok(self.client.clone().unwrap());
        } else {
            let ret = OrderRouterServiceClient::connect(self.uri.to_owned()).await;
            if ret.as_ref().is_err() {
                push_log(format!("connect to OrderRouter failed: {:?}", self.uri).as_str()).await;
                return Err(anyhow!(format!("connect to OrderRouter failed")));
            }
            let client = ret.expect("connect to OrderRouter failed");
            info!("订单路由连接成功....");
            self.client = Some(client);
            return Ok(self.client.clone().unwrap());
        }
    }

    pub async fn order_routing(&mut self) -> Result<()> {
        let ret = self.init_client().await;
        if ret.as_ref().is_err() {
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let mut client = ret.unwrap();

        let mut rx = self.tx_order.subscribe();
        //报单消息
        let outbound = async_stream::stream! {
            loop {
                if let Ok(val) = rx.recv().await {
                    info!("推送到报盘: {:?}", &val);
                    yield val;
                }
            }
        };

        let response = match client.order_routing(outbound).await {
            Ok(val) => val,
            Err(status) => {
                self.client = None;
                error!("{:?}", status);
                push_log(format!("OrderRouter center err code: {:?} message: {:?}", status.code(), status.message()).as_str()).await;
                return Err(anyhow!(format!("OrderRouter err")));
            }
        };

        let mut inbound = response.into_inner();
        while let Ok(inbound_data) = inbound.message().await {
            if inbound_data.is_some() {
                //回报消息
                let value = inbound_data.unwrap();
                info!("回执: {:?}", value);
                self.repay(&value).await;
            } else {
                info!("inbound data empty");
            }
        }
        Ok(())
    }

    pub async fn repay(&self, router_msg: &RouterMsg) {
        match router_msg.msg_type() {
            MsgType::Register => {}
            MsgType::Order => {}
            MsgType::Exec => {
                if let Some(msg_content) = router_msg.msg_content.clone() {
                    if let Some(exec_msg) = msg_content.exec_msg {
                        match exec_msg.exec_type() {
                            ExecType::ExecUndef => {}
                            ExecType::Confirm => {
                                info!("订单{}确认回报: {:?}", exec_msg.order_id, exec_msg);
                                if let Err(err) = self.tx_confirm.send(exec_msg).await {
                                    error!("确认消息发送失败: {:?}", err);
                                    push_log(&format!("确认消息发送失败 error:{}", err.to_string())).await;
                                }
                            }
                            ExecType::Filled => {
                                info!("订单{}成交回报: {:?}", exec_msg.order_id, exec_msg);
                                if let Err(err) = self.tx_filled.send(exec_msg).await {
                                    error!("成交消息发送失败: {:?}", err);
                                    push_log(&format!("成交消息发送失败 error:{}", err.to_string())).await;
                                }
                            }
                            ExecType::Canceled => {
                                info!("订单{}撤单回报: {:?}", exec_msg.order_id, exec_msg);
                                if let Err(err) = self.tx_canceled.send(exec_msg).await {
                                    error!("撤单消息发送失败: {:?}", err);
                                    push_log(&format!("撤单消息发送失败 error:{}", err.to_string())).await;
                                }
                            }
                            ExecType::Rejected => {
                                info!("订单{}废单回报: {:?}", exec_msg.order_id, exec_msg);
                                if let Err(err) = self.tx_rejected.send(exec_msg).await {
                                    error!("废单消息发送失败: {:?}", err);
                                    push_log(&format!("废单消息发送失败 error:{}", err.to_string())).await;
                                }
                            }
                        }
                    }
                }
            }
            MsgType::Response => {}
        }
    }

    // pub async fn order_routing(&mut self) -> Result<()> {
    //     let mut rx = self.tx_order.subscribe();
    //     //报单消息
    //     let outbound = async_stream::stream! {
    //         loop {
    //             if let Ok(val) = rx.recv().await {
    //                 info!("推送到报盘: {:?}", &val);
    //                 yield val;
    //             }
    //         }
    //     };

    //     let sub_ret = self.client.order_routing(outbound).await;
    //     if sub_ret.as_ref().is_err() {
    //         error!("router error:{:?}", sub_ret.as_ref().err());
    //         //订单到这里失败了怎么办 ?
    //         return Err(anyhow!("order router error"));
    //     }
    //     let response = sub_ret.unwrap();

    //     let mut inbound = response.into_inner();
    //     while let Ok(inbound_data) = inbound.message().await {
    //         if inbound_data.is_some() {
    //             //回报消息
    //             let value = inbound_data.unwrap();
    //             info!("回执: {:#?}", value);
    //             self.repay(&value).await;
    //         } else {
    //             info!("inbound data empty");
    //         }
    //     }
    //     Ok(())
    // }

    // pub async fn retry_order_routing(&mut self) -> Result<()> {
    //     let client = OrderRouterServiceClient::connect(self.uri.to_owned()).await;
    //     if client.as_ref().is_err() {
    //         error!("{:?}", client);
    //         return Err(anyhow!("{:?}", client));
    //     }
    //     self.client = client.unwrap();
    //     let get_ret = self.order_routing().await;
    //     if get_ret.as_ref().is_err() {
    //         error!("{:?}", get_ret.as_ref().err().unwrap());
    //         return Err(anyhow!("{:?}", get_ret.as_ref().err().unwrap()));
    //     }
    //     Ok(())
    // }
}
