//错误码

pub const _DATA_ERROR: &'static str = "数据错误";
pub const _PARAM_USER_EXISTS: &'static str = "用户已存在";

//缓存超时时间常量
pub const EXPARE_TIME_8_HOUR: i64 = 8 * 60 * 60;
pub const _EXPARE_TIME_1_HOUR: i64 = 1 * 60 * 60;
pub const EXPARE_TIME_1_DAY: i64 = 24 * 60 * 60;

// pub const FEE_TYPE: [&'static str; 13] = ["1", "2", "3", "4", "5", "6", "7", "9", "a", "b", "c", "d", "e"];

pub const FEE_TYPE1: [&'static str; 7] = ["2", "3", "5", "6", "7", "b", "4"];
pub const FEE_YJ: &'static str = "4";
//rediskey
pub const MARKET_CLOSE_TIME: &'static str = "market_close_time"; //休市时间
pub const SPECIAL_ACCOUNT: &'static str = "special_account"; //全部交易对手方账号和特殊账号信息

// pub const FEE_SETTING_KEY: &'static str = "fee_setting_"; //查询费用key列表
// pub const FEE_SETTING_KEY2: &'static str = "_fee_setting"; //查询费用key列表

//rediskey前缀

pub const _USER_ASSETS_REDIS_KEY: &'static str = "assets_user_"; //用户资产信息
pub const _LOCK_USER_REDIS_KEY: &'static str = "lock_assets_user_"; //用户资产信息锁
pub const QUERY_COMMODITY_CHANNLE_CONFIG_KEY: &'static str = "QueryCommodityChannleConfig_"; //股票通道最大持仓量等信息
pub const CHANNEL_KEY: &'static str = "channel_"; //通道基础信息
pub const USER_KEY: &'static str = "user_"; //账户信息
pub const MARKET_KEY: &'static str = "market_"; //市场信息
pub const HOLIDAY_MARKET_KEY: &'static str = "holiday_market_"; //账户信息
pub const BZJ_KEY: &'static str = "user_margin_"; //用户品种保证金
pub const CHANNEL_MARGIN_KEY: &'static str = "channel_margin_"; //用户品种保证金

//持仓key 形式  user_1_stock_1
pub const _USER_POSITION_KEY: &'static str = "user_{}_stock_{}"; //用户持仓
pub const _LOCK_USER_POSITION_KEY: &'static str = "lock_user_{}_stock_{}"; //用户持仓锁
pub const _USER_POSITION_STOCKID_SET: &'static str = "user_stockids_"; //存储用户stockid集合

pub const _SYSTEM_DATE_KEY: &'static str = "system_current_date_"; //系统当前日期
pub const COMMODITY_KEY: &'static str = "commodity_"; //品种key
pub const _RATE_CNY_HKD_STOCK_BUY: &'static str = "RATE_CNY_HKD_STOCK_BUY"; //人民币兑港币买汇率
pub const _RATE_CNY_HKD_STOCK_SELL: &'static str = "RATE_CNY_HKD_STOCK_SELL"; //人民币兑港币卖汇率
                                                                              //币种
pub const CURRENCY_CNY: &'static str = "CNY";
pub const CURRENCY_HKD: &'static str = "HKD";
pub const CURRENCY_USD: &'static str = "USD";

pub const _CURRENCY_NUM: i32 = 3;

#[repr(i32)]
pub enum TradingDayType {
    NonTradingDay = 0, //非交易日
    TradingDay,        //交易日
    HalfTradingDay,    //半日市
}

impl TryFrom<i32> for TradingDayType {
    type Error = ();

    fn try_from(value: i32) -> Result<Self, Self::Error> {
        match value {
            v if v == TradingDayType::NonTradingDay as i32 => Ok(TradingDayType::NonTradingDay),
            v if v == TradingDayType::TradingDay as i32 => Ok(TradingDayType::TradingDay),
            v if v == TradingDayType::HalfTradingDay as i32 => Ok(TradingDayType::HalfTradingDay),
            _ => Err(()),
        }
    }
}

#[repr(i32)]
pub enum QueryType {
    Common = 0, //普通日期
    TradingDay, //交易日
    SettleDay,  //交收日
}

impl TryFrom<i32> for QueryType {
    type Error = ();

    fn try_from(value: i32) -> Result<Self, Self::Error> {
        match value {
            v if v == QueryType::Common as i32 => Ok(QueryType::Common),
            v if v == QueryType::TradingDay as i32 => Ok(QueryType::TradingDay),
            v if v == QueryType::SettleDay as i32 => Ok(QueryType::SettleDay),
            _ => Err(()),
        }
    }
}
