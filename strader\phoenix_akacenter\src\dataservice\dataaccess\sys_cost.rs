use crate::dataservice::{
    dbsetup::DbConnection,
    entities::{
        prelude::{SysCost, SysCostEntity},
        sys_cost,
    },
};
use anyhow::{anyhow, Result};
use sea_orm::{DbErr, EntityTrait};

impl sys_cost::Model {
    pub async fn find_all(db: &DbConnection) -> Result<Vec<SysCost>> {
        let ret_data: Result<Vec<SysCost>, DbErr> = SysCostEntity::find().all(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }
}
