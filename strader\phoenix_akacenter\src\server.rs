pub use super::controller::*;

use crate::config::settings::Settings;
// use futures;
use tonic::{self, Request, Response, Status};

use messagecenter::notificationclient::NotificationClient;
use protoes::phoenixnotification::NotificationMessage;
use tracing::*;
// use crate::protofiles::phoenixriskcenter::phoenix_riskcenter_server::PhoenixRiskcenter;
use protoes::phoenixakacenter::{
    phoenix_aka_center_server::PhoenixAkaCenter, AccountInfoReq, AccountInfoResp, ChannelHoldLimitReq, ChannelHoldLimitResp, ChannelInfoReq, ChannelInfoResp, ExchangeRateReq, ExchangeRateResp, FeeSettingReq,
    FeeSettingResp, MarketCloseInfoReq, MarketCloseInfoResp, MarketCodeReq, MarketCodeResp, MarketInfoReq, MarketInfoResp, SecuritiesBorrowLevelReq, SecuritiesBorrowLevelResp, SecuritiesBorrowLimitReq,
    SecuritiesBorrowLimitResp, SpecialAccountInfoReq, SpecialAccountInfoResp, StockChannelReq, StockChannelResp, StockInfoReq, StockInfoResp, StockMarginReq, StockMarginResp, StockQuotaReq, StockQuotaResp,
    StockSuspensionReq, StockSuspensionResp, StockTradeTimeReq, StockTradeTimeResp, TradeDateReq, TradeDateResp, UserStockMarginReq, UserStockMarginResp,
};

use std::pin::Pin;
use std::sync::Arc;
// use tracing::*;

use crate::dataservice::dbsetup;
use crate::service::fee_setting::FeeSettingCache;
use common::redisclient::redispool::{RedisClient, RedisConfig};
use tokio::sync::{mpsc, oneshot, RwLock};

type StubType = Arc<AkaServerController>;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

pub struct ServerHandler {
    stub: StubType,
    task_dispacther: mpsc::Sender<ControllerAction>,
    // order_dispacther: mpsc::Sender<PhoenixRiskCheckInfo>,
    set_close: Option<oneshot::Sender<()>>,
    // mqclient: QuotationClient,
}

// fn map_dispatch_err<T: 'static>(_: mpsc::error::SendError<T>) -> tonic::Status {
//     tonic::Status::unknown("Server temporary unavaliable")
// }
// type ControllerRet<OT> = Result<OT, tonic::Status>;
// type ServerRet<OT> = Result<Response<OT>, tonic::Status>;
// fn map_dispatch_ret<OT: 'static>(recv_ret: Result<ControllerRet<OT>, oneshot::error::RecvError>) -> ServerRet<OT> {
//     match recv_ret {
//         Ok(ret) => {
//             info!("收到结果，开始分发结果返回客户端");
//             ret.map(Response::new)
//         }
//         Err(_) => Err(Status::unknown("Dispatch ret unreach")),
//     }
// }
pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);

impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

impl ServerHandler {
    pub async fn new(settings: &Settings) -> Self {
        let mut persist_interval = tokio::time::interval(std::time::Duration::from_secs(5 * 60 * 60 as u64));

        let (tx_notification, mut rx_notification) = tokio::sync::mpsc::channel::<NotificationMessage>(128);

        let queue_name = format!("phoenix_akacenter_notification_{}", utility::timeutil::current_timestamp());
        let notification_client = Arc::new(RwLock::new(
            NotificationClient::new(
                &settings.notification.notification_exchanger.as_str(),
                &queue_name,
                settings.notification.akacenter_routing_key.clone(),
                &format!("{}{}", &settings.mq.amqpaddr, &settings.notification.vhost),
                tx_notification,
            )
            .await,
        ));

        //监听时间
        messagecenter::init::init_notification_client(notification_client.clone()).await;
        info!("start to listen notification message");
        messagecenter::init::init_notification_listen(notification_client).await;

        let customer_dbconn = dbsetup::DbConnection::new(&settings.database.customer_uri).await;
        let finances_dbconn = dbsetup::DbConnection::new(&settings.database.finances_uri).await;
        let stock_dbconn = dbsetup::DbConnection::new(&settings.database.stock_uri).await;
        
        let mut rdscfg = RedisConfig::default();
        rdscfg.urls = settings.redis.uri.to_owned();
        rdscfg.prefix = settings.redis.prefix.to_owned();
        rdscfg.max_size = 10;
        rdscfg.min_idle = 10;
        info!("redis config info:{:?}", &rdscfg);
        let redis_client = RedisClient::new(&rdscfg).expect("redis connect err");

        let fee_setting_cache = FeeSettingCache::init(finances_dbconn.clone()).await;

        let stub = AkaServerController {
            settings: settings.clone(), // rbcon: rb.to_owned(),
            redis: redis_client,
            fee_setting_cache,
            customer_db: customer_dbconn,
            finances_db: finances_dbconn,
            stock_db: stock_dbconn,
            redis_on: settings.redis.on_off,
        };

        // stub.init_fee_setting_redis().await; //初始化redis费用设置

        let stub = Arc::new(stub);

        let (tx, mut rx) = mpsc::channel(16);
        let (tx_close, mut rx_close) = oneshot::channel();

        let svr_handler = ServerHandler {
            task_dispacther: tx,
            set_close: Some(tx_close),
            stub: stub.clone(),
        };

        let stub_for_dispatch = stub.clone();
        tokio::spawn(async move {
            persist_interval.tick().await; //skip first tick
            loop {
                tokio::select! {
                    _ = persist_interval.tick() => {
                        // info!("Start a time interval task (persist, computing):{:?}",std::thread::current());
                    }
                    notification = rx_notification.recv() => {
                        //收到消息通知
                        if let Some(message) = &notification{
                            //receive message
                            info!("receive message from message center: {:?}", &message);
                            if message.msg_body.is_some() {
                                if let Some(msg_feesetting) = &message.msg_body.as_ref().unwrap().msg_feesetting {
                                    info!("receive message from message center: {:?}", &msg_feesetting.id);
                                    stub_for_dispatch.fee_setting_cache.update_cache().await;
                                }
                            }

                        }
                    }
                }
            }
        });

        let stub_for_dispatch = stub.clone();
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    may_task = rx.recv() => {
                        if let Some(task) = may_task{
                            task(stub_for_dispatch.clone()).await;
                        }
                    }
                    _ = &mut rx_close => {
                        info!("Server scheduler is notified to close");
                        rx.close();
                        break;
                    }
                }
            }

            //drain unhandled task
            while let Some(task) = rx.recv().await {
                task(stub_for_dispatch.clone()).await;
            }

            warn!("Server scheduler has exited");
        });

        svr_handler
    }
    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

//这里实现grpc的接口
#[tonic::async_trait]
impl PhoenixAkaCenter for ServerHandler {
    async fn query_channel_hold_limit(&self, request: Request<ChannelHoldLimitReq>) -> Result<Response<ChannelHoldLimitResp>, Status> {
        let rt = self.stub.query_channel_hold_limit(request.into_inner()).await?;
        // info!("query_channel_hold_limit 返回结果:{:?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_channel_info(&self, request: Request<ChannelInfoReq>) -> Result<Response<ChannelInfoResp>, Status> {
        let rt = self.stub.query_channel_info(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_stock_channel(&self, request: Request<StockChannelReq>) -> Result<Response<StockChannelResp>, Status> {
        let rt = self.stub.query_stock_channel(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_stock_info(&self, request: Request<StockInfoReq>) -> Result<Response<StockInfoResp>, Status> {
        let rt = self.stub.query_stock_info(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_special_account(&self, request: Request<SpecialAccountInfoReq>) -> Result<Response<SpecialAccountInfoResp>, Status> {
        let rt = self.stub.query_special_account(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_market_info(&self, request: Request<MarketInfoReq>) -> Result<Response<MarketInfoResp>, Status> {
        let rt = self.stub.query_market_info(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_market_close_info(&self, request: Request<MarketCloseInfoReq>) -> Result<Response<MarketCloseInfoResp>, Status> {
        let rt = self.stub.query_market_close_info(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_exchange_rate(&self, request: Request<ExchangeRateReq>) -> Result<Response<ExchangeRateResp>, Status> {
        let rt = self.stub.query_exchange_rate(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_account_info(&self, request: Request<AccountInfoReq>) -> Result<Response<AccountInfoResp>, Status> {
        let rt = self.stub.query_account_info(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_trade_date(&self, request: Request<TradeDateReq>) -> Result<Response<TradeDateResp>, Status> {
        let rt = self.stub.query_trade_date(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_unit_stock_margin(&self, request: Request<UserStockMarginReq>) -> Result<Response<UserStockMarginResp>, Status> {
        let rt = self.stub.query_unit_stock_margin(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_fee_setting(&self, request: Request<FeeSettingReq>) -> Result<Response<FeeSettingResp>, Status> {
        let rt = self.stub.query_fee_setting(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_stock_channel_margin(&self, request: Request<StockMarginReq>) -> Result<Response<StockMarginResp>, Status> {
        let rt = self.stub.query_stock_channel_margin(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_stock_suspension_info(&self, request: Request<StockSuspensionReq>) -> Result<Response<StockSuspensionResp>, Status> {
        let rt = self.stub.query_stock_suspension_info(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_stock_trade_time(&self, request: Request<StockTradeTimeReq>) -> Result<Response<StockTradeTimeResp>, Status> {
        let rt = self.stub.query_stock_trade_time(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_securities_borrow_limit(&self, request: Request<SecuritiesBorrowLimitReq>) -> Result<Response<SecuritiesBorrowLimitResp>, Status> {
        let rt = self.stub.query_securities_borrow_limit(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_securities_borrow_level(&self, request: Request<SecuritiesBorrowLevelReq>) -> Result<Response<SecuritiesBorrowLevelResp>, Status> {
        let rt = self.stub.query_securities_borrow_level(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_stock_channel_quota(&self, request: Request<StockQuotaReq>) -> Result<Response<StockQuotaResp>, Status> {
        let rt = self.stub.query_stock_channel_quota(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }

    async fn query_market_code(&self, request: Request<MarketCodeReq>) -> Result<Response<MarketCodeResp>, Status> {
        let rt = self.stub.query_market_code(request.into_inner()).await?;
        // info!("返回结果:{:#?}", &rt);
        Ok(Response::new(rt))
    }
}
