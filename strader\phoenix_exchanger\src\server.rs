pub use super::controller::*;

use crate::client::marketclosetimecache::CloseTimeCached;

use crate::client::MarketsCached;
// use crate::client::OrderRouterClientV2;

use crate::dataservice::entities::prelude::*;
use crate::service::ordercontroller::OrderExController;
use crate::{commonutil::commonutil::CommonUtil, config::settings::Settings, service::PersistentController};
use anyhow::Result;
use async_channel::unbounded;
use common::logclient::*;
use common::uidservice;
use common::uidservice::UidgenService;
use dbconnection::DbConnection;
use futures::Stream;
use messagecenter::quotationclient::QuotationClient;
use orderrouterclient::OrderRouterClient;
use protoes::{
    hqmsg::YsHqInfo,
    phoenixexchanger::phoenix_exchanger_server::PhoenixExchanger,
    phoenixordermsg::{ExecType, MsgContent, MsgType, OrderType, RouterMsg, *},
};
use std::pin::Pin;
use std::{collections::HashMap, sync::Arc};
use tokio::sync::{broadcast, mpsc};
use tonic::{self, Request, Response, Status};
use tracing::*;
use utility::timeutil;

// type StubType = Arc<RwLock<ServerController>>;
type StubType = Arc<ServerController>;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

#[allow(dead_code)]
pub struct ServerHandler {
    stub: StubType,
    task_dispacther: Option<mpsc::Sender<ControllerAction>>,
    // order_dispacther: mpsc::Sender<PhoenixRiskCheckInfo>,
    // mqclient: QuotationClient,
    quotation_client: Option<QuotationClient>,
    order_router_client: Option<OrderRouterClient>,
    shutdown_tx: broadcast::Sender<()>,
}

pub enum PersistData {
    OrderInfo(Box<PhoenixExcOrderinfo>),
    ExcDealdetail(Box<PhoenixExcDealdetail>),
    UpdateOrder(Box<PhoenixExcOrderinfo>),
    DelOrder(Box<PhoenixExcOrderinfo>),
}

impl ServerHandler {
    pub async fn new(settings: &Settings) -> Self {
        let mut persist_interval = tokio::time::interval(std::time::Duration::from_secs(5 * 60 * 60 as u64));
        let common = CommonUtil::new();
        let ret = common.init(&settings).await; //.expect("init time error");
        if ret.is_err() {
            error!("init time error:{:?}", ret);
        }
        let (tx_persist, rx_persist) = unbounded::<PersistData>();
        let (tx, mut rx) = mpsc::channel(16);

        // Create shutdown broadcast channel for coordinating background tasks
        let (shutdown_tx, _shutdown_rx) = broadcast::channel::<()>(16);

        let mut op = akaclient::akaclient::AkaCacheOption::default();
        op.use_cache = true;
        op.mq_uri = format!("{}{}", &settings.mq.amqpaddr, &settings.notification.vhost);
        op.routing_keys = settings.notification.exchanger_routing_key.clone();
        op.exchange = settings.notification.notification_exchanger.clone();
        let akacenterconn = akaclient::akaclient::AkaClient::init(&settings.servers.akacenterserver, &op, "exchanger", "akacenter").await;

        //路由中心 ------>
        let (tx_order_from_router, rx_order_from_router) = unbounded::<RouterMsg>();
        // ------> 路由中心
        let (tx_repay_to_router, rx_repay_to_router) = unbounded::<RouterMsg>();

        let orderrouterclient = OrderRouterClient::init(
            &settings.servers.orderrouterserver,
            "exchanger",
            "orderrouter",
            None,
            None,
            None,
            None,
            None,
            Some(tx_order_from_router.clone()),
            Some(rx_repay_to_router.clone()),
            Some(vec![settings.system.channel_id as i64, settings.system.channel_doudi_id as i64]),
        )
        .await;

        // Clone the orderrouterclient before it's moved into the async task
        let orderrouterclient_for_handler = orderrouterclient.clone();

        let mut retry_interval = tokio::time::interval(std::time::Duration::from_secs(3));
        let tx_repay = tx_repay_to_router.clone();
        let mut shutdown_rx_retry = shutdown_tx.subscribe();

        tokio::spawn(async move {
            retry_interval.tick().await;
            loop {
                tokio::select! {
                    _ = retry_interval.tick() => {
                        if let Err(err) = orderrouterclient.order_transfer(tx_repay.clone()).await {
                            error!("{:?}", err);
                        }
                    }
                    _ = shutdown_rx_retry.recv() => {
                        info!("OrderRouter retry task received shutdown signal");
                        break;
                    }
                }
            }
            info!("OrderRouter retry task has exited");
        });

        //行情连接......
        let (tx_quotation, mut rx_quotation) = tokio::sync::broadcast::channel::<YsHqInfo>(1024);
        let queue_name = format!("phoenix_exchanger_quotation_{}", utility::timeutil::current_timestamp());
        let routing_key: HashMap<String, i32> = HashMap::new();
        let quotation_client = QuotationClient::new(
            &&settings.quotation.stocklive_exchanger.as_str(),
            &queue_name,
            routing_key,
            &format!("{}{}", &settings.mq.amqpaddr, &settings.quotation.vhost),
            tx_quotation,
        )
        .await;
        let quotation_client_copy = quotation_client.clone();
        let quotation_client_for_handler = quotation_client.clone();
        // 监听行情 - with shutdown coordination using shared library function
        let shutdown_rx_quotation_listen = shutdown_tx.subscribe();
        messagecenter::init::init_quotation_listen_with_shutdown(quotation_client, shutdown_rx_quotation_listen).await;
        let (tx_log, rx_log) = unbounded::<String>();
        let db_conn = DbConnection::new(&settings.database.stock_uri).await;
        let mut datetime_controller = CloseTimeCached::new();
        if let Err(_) = datetime_controller.init_market_closetime(&akacenterconn).await {
            error!("init close time error......");
        }

        let mut market_controller = MarketsCached::new();
        if let Err(_) = market_controller.init_markets(&akacenterconn).await {
            error!("init market error......");
        }

        let order_ex_controller = OrderExController::new(tx_persist.clone(), settings.system.machine_id, settings.system.node_id, tx_log.clone());
        let stub = ServerController {
            order_persist: tx_persist.clone(),
            common_util: common,
            _deal_time: datetime_controller.clone(),
            market_cache: market_controller.clone(),
            basic_client: akacenterconn.clone(),
            order_controller: order_ex_controller.clone(),
            router_sender: tx_repay_to_router.clone(),
        };

        let shared_db_conn = Arc::new(db_conn);
        let order_db = PersistentController::new(shared_db_conn.clone());
        let temp_order_db = PersistentController::new(shared_db_conn.clone());

        let stub = Arc::new(stub);
        let stub_for_dispatch = stub.clone();
        let stub_for_dispatch_hq = stub.clone();

        let mut stub_quotation = quotation_client_copy;
        let r_tx = tx_repay_to_router.clone();

        let svr_handler = ServerHandler {
            task_dispacther: Some(tx),
            stub,
            quotation_client: Some(quotation_client_for_handler),
            order_router_client: Some(orderrouterclient_for_handler),
            shutdown_tx: shutdown_tx.clone(),
        };

        // Clone shutdown receivers for background tasks
        let mut shutdown_rx_main = shutdown_tx.subscribe();

        // Spawn main task dispatcher and store handle
        let _main_task_handle = tokio::spawn(async move {
            persist_interval.tick().await; //skip first tick
            loop {
                tokio::select! {
                    may_task = rx.recv() => {
                        if let Some(task) = may_task{
                            task(stub_for_dispatch.clone()).await;
                        } else {
                            // Channel closed, break the loop
                            break;
                        }
                    }
                    order_msg = rx_order_from_router.recv()=>{
                        info!("收到订单消息......:{:?}",order_msg);
                        if let Ok(order_msg) = order_msg {
                            //把订单信息存入缓存，同时发送消息，保存到数据库(可以通过channel的方式)
                            info!("order message is:{:?}",&order_msg);
                            if let Some(msg) = order_msg.msg_content {
                                if let Some(order_msg) = msg.order_msg {
                                    let mut flag = false;
                                    let mut flag_id = 0;
                                    let mut flag_cancel_id = 0;
                                    if order_msg.order_type ==OrderType::Place as i32 { //下单
                                        let order = stub_for_dispatch.new_order(&order_msg).await;
                                        if let Ok(_val) = order {
                                            //let _ = tx_persist.send(PersistData::OrderInfo(Box::new(val))).await;
                                            let new_keys = stub_for_dispatch.order_controller.get_stock_positions_quotation_key(&market_controller).await; //取消订阅
                                            let _ = stub_quotation.update_bindings(&new_keys).await;
                                            //返回确认消息
                                            flag = true;
                                            flag_id =  ExecType::Confirm as i32;
                                        } else {
                                            flag = true;
                                            flag_id =  ExecType::Rejected as i32;
                                        }
                                    } else if order_msg.order_type ==OrderType::Cancel as i32 { //撤单
                                        info!("收到订单撤单消息......:{:?}",&order_msg);
                                        let orders = temp_order_db.find_by_order_id_and_date_and_status
                                        (order_msg.order_id, timeutil::current_date(), common::constant::ExOrderStatus::UnExec as i64).await;
                                            let c_id = stub_for_dispatch.cancel_order(&order_msg,orders).await;
                                            flag_cancel_id = c_id.unwrap();
                                            let new_keys = stub_for_dispatch.order_controller.get_stock_positions_quotation_key(&market_controller).await; //取消订阅
                                            let _ = stub_quotation.update_bindings(&new_keys).await;
                                                //返回确认消息
                                                flag = true;
                                                    flag_id =  ExecType::Canceled as i32;
                                    } else {
                                        info!("未定义的订单类型:{}",order_msg.order_type)
                                    }
                                    if flag {
                                        let mut id = UidgenService::new(8, 16);
                                        let mut router_msg = RouterMsg { ..Default::default() };
                                        router_msg.msg_type = MsgType::Exec as i32;
                                        router_msg.msg_content = Some(MsgContent {
                                        register_req: None,
                                            order_msg: None,
                                            exec_msg: Some(ExecMsg{
                                                    exec_type :flag_id,
                                                    order_id : order_msg.order_id,
                                                    channel_id:order_msg.channel_id,
                                                    channel_type :order_msg.channel_type,
                                                    brk_order_id : id.get_uid().to_string(),
                                                    order_direction:order_msg.order_direction,
                                                    ..Default::default()
                                                        }),
                                            resp: None,
                                        });
                                        if flag_id == ExecType::Canceled as i32 {
                                            router_msg.msg_content = Some(MsgContent {
                                            register_req: None,
                                                order_msg: None,
                                                exec_msg: Some(ExecMsg{
                                                        exec_type :flag_id,
                                                        order_id : order_msg.order_id,
                                                        channel_id:order_msg.channel_id,
                                                        channel_type :order_msg.channel_type,
                                                        brk_order_id : id.get_uid().to_string(),
                                                        order_direction:order_msg.order_direction,
                                                        cancel_id:flag_cancel_id,
                                                        ..Default::default()
                                                            }),
                                                resp: None,
                                            });
                                        }
                                        let mut snow =  uidservice::UidgenService::new(1, 1);
                                        let uid = snow.get_uid();
                                        router_msg.msg_id = uid;
                                        router_msg.msg_time = timeutil::current_timestamp();
                                        info!("回报 message is:{:?}",&router_msg);
                                        let _ = r_tx.send(router_msg).await;
                                    }
                                }
                            }
                        }
                    }
                    _ = shutdown_rx_main.recv() => {
                        info!("Main task dispatcher received shutdown signal");
                        break;
                    }
                }
            }
            info!("Main task dispatcher has exited");
        });

        let mut shutdown_rx_persist = shutdown_tx.subscribe();

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    persist_msg = rx_persist.recv()=>{
                        // info!("收到持久化信息...");
                        if let Ok(persist) = persist_msg {
                            match persist {
                                PersistData::OrderInfo(data) => {
                                    info!("收到订单持久化信息:{:?}...",data);
                                    let _= order_db.insert_ex_order(&data).await;
                                },
                                PersistData::ExcDealdetail(data) => {
                                    info!("收到订单执行结果持久化信息:{:?}...",data);
                                    let _= order_db.insert_ex_deal(&data).await;
                                },
                                PersistData::UpdateOrder(data) => {
                                    info!("收到订单执行结果更新信息:{:?}...",data);
                                    let mut order = data;
                                    let _= order_db.update_order(&mut order).await;
                                },
                                PersistData::DelOrder(data) => {
                                    info!("收到订单执行结果删除信息:{:?}...",data);
                                    let mut order = data;
                                    let _= order_db.del_order(&mut order).await;
                                },
                            }
                        }
                    }
                    _ = shutdown_rx_persist.recv() => {
                        info!("Persistence task received shutdown signal");
                        break;
                    }
                }
            }
            info!("Persistence task has exited");
        });

        let mut shutdown_rx_log = shutdown_tx.subscribe();

        tokio::spawn(async move {
            loop {
                tokio::select! {
                        log_msg = rx_log.recv()=>{
                        if let Ok(val) = log_msg{
                            log_debug(&val).await;
                        }
                    }
                    _ = shutdown_rx_log.recv() => {
                        info!("Logging task received shutdown signal");
                        break;
                    }
                }
            }
            info!("Logging task has exited");
        });

        let mut shutdown_rx_quotation = shutdown_tx.subscribe();

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    quotation_msg = rx_quotation.recv() => {
                        //收到行情，判断处理成交，如何保证成交处理按顺序执行？
                        //如果通过mpsc管道来处理，是否可以不用加锁？
                        if let Ok(quotation) = &quotation_msg{
                            //receive quotation
                            info!("receive quotation from quotation center: {:?}", &quotation);
                            //如果收到行情，处理成交
                            let _ret = stub_for_dispatch_hq.deal_order(&quotation).await;
                        }
                    }
                    _ = shutdown_rx_quotation.recv() => {
                        info!("Quotation task received shutdown signal");
                        break;
                    }
                }
            }
            info!("Quotation task has exited");
        });

        svr_handler
    }

    /// Get a reference to the controller for shutdown purposes
    pub fn get_controller(&self) -> &Arc<ServerController> {
        &self.stub
    }

    /// Gracefully shutdown all clients in the ServerHandler
    pub async fn shutdown(&mut self) -> Result<()> {
        info!("Initiating graceful shutdown of ServerHandler");

        // Send shutdown signal to all background tasks
        info!("Sending shutdown signal to all background tasks...");
        if let Err(e) = self.shutdown_tx.send(()) {
            warn!("Failed to send shutdown signal to background tasks: {:?}", e);
        }

        // QuotationClient doesn't have a shutdown method, connection will be closed automatically
        if self.quotation_client.is_some() {
            info!("QuotationClient will be cleaned up automatically when dropped");
        }

        // Shutdown OrderRouterClient
        if let Some(ref order_router_client) = self.order_router_client {
            info!("Shutting down OrderRouterClient...");
            order_router_client.shutdown().await;
            info!("OrderRouterClient shutdown completed");
        }

        // Give background tasks a moment to complete their shutdown
        tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;

        // Drop the task dispatcher to allow clean shutdown
        if let Some(task_dispatcher) = self.task_dispacther.take() {
            drop(task_dispatcher);
            info!("Task dispatcher dropped to allow clean shutdown");
        }

        // Force cleanup of any remaining resources
        info!("Forcing cleanup of remaining resources...");

        // Give a final moment for all cleanup to complete
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        info!("ServerHandler shutdown completed");
        Ok(())
    }
}

//这里实现grpc的接口
#[tonic::async_trait]
impl PhoenixExchanger for ServerHandler {
    type PhoenixExchangerOrderStream = Pin<Box<dyn Stream<Item = Result<RouterMsg, Status>> + Send + 'static>>;

    async fn phoenix_exchanger_order(&self, request: Request<tonic::Streaming<RouterMsg>>) -> Result<Response<Self::PhoenixExchangerOrderStream>, Status> {
        info!("phoenix_exchanger_order:{:?}", &request);

        todo!();
    }
}
