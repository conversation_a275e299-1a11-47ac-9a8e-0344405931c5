//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_closure_record")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub user_id: i64,
    pub account_no: String,
    pub real_name: String,
    pub card_type: i8,
    pub id_number: String,
    pub closure_reasons: i32,
    pub topic_answers: String,
    pub sar_answers: String,
    pub filename: String,
    pub noresponse_days: i32,
    pub approval_status: i8,
    pub application_time: i64,
    pub review_time: i64,
    pub remark: String,
    pub management_name: String,
    pub management_position: String,
    pub create_user: String,
    pub review_user: String,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
