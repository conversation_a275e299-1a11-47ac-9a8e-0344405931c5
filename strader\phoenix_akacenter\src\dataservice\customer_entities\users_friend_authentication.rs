//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_friend_authentication")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub friend_name: String,
    pub friend_mobile: String,
    pub create_time: i64,
    pub create_name: String,
    pub question_1: String,
    pub answer_1: String,
    pub question_2: String,
    pub answer_2: String,
    pub question_3: String,
    pub answer_3: String,
    pub mobile: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
