#!/bin/bash

# Validate input parameter
if [ -z "$1" ]; then
    echo "Error: No action specified. Usage: $0 {start|stop|restart}"
    exit 1
fi

ACTION="$1"

# Service lists for stopping (in reverse dependency order)
STOP_ORDER=(
    "phoenix_settlecenter"
    "phoenix_algorithmcenter"
    "phoenix_matchserver"
    "phoenix_ordercenter"
    "phoenix_exchanger"
    "phoenix_orderrouter"
    "phoenix_riskcenter"
    "phoenix_blackscholes"
    "phoenix_accountriskcenter"
    "phoenix_satcenter"
    "phoenix_assetscenter"
    "phoenix_akacenter"
)

# Service lists for starting/restarting (in dependency order)
START_ORDER=(
    "phoenix_akacenter"
    "phoenix_assetscenter"
    "phoenix_satcenter"
    "phoenix_accountriskcenter"
    "phoenix_riskcenter"
    "phoenix_orderrouter"
    "phoenix_exchanger"
    "phoenix_ordercenter"
    "phoenix_matchserver"
    "phoenix_algorithmcenter"
    "phoenix_settlecenter"
    "phoenix_blackscholes"
)

# Function to stop services
stop_services() {
    echo "Stopping services..."
    for service in "${STOP_ORDER[@]}"; do
        if systemctl is-active --quiet "$service"; then
            echo "Stopping $service..."
            sudo systemctl stop "$service" || {
                echo "Error: Failed to stop $service"
                # exit 1
            }
        else
            echo "$service is already stopped"
        fi
    done
    sleep 2
}

# Function to start/restart services
manage_services() {
    local action="$1"
    echo "Performing $action on services..."
    for service in "${START_ORDER[@]}"; do
        echo "$action $service..."
        sudo systemctl "$action" "$service" || {
            echo "Error: Failed to $action $service"
            # exit 1
        }
         # Add sleep for specific services as per original script
        case "$service" in
            "phoenix_akacenter"|"phoenix_assetscenter"|"phoenix_accountriskcenter"|"phoenix_orderrouter"|"phoenix_ordercenter")
                sleep 1.5
                ;;
        esac
    done
}

# Main logic
case "$ACTION" in
    stop)
        stop_services
        echo "All services stopped."
        ;;
    start|restart)
        stop_services
        manage_services "$ACTION"
        echo "All services ${ACTION}ed."
        ;;
    *)
        echo "Error: Invalid action '$ACTION'. Usage: $0 {start|stop|restart}"
        exit 1
        ;;
esac

exit 0