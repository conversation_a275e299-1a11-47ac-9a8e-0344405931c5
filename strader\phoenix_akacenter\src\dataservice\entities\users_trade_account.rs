//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_trade_account")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    #[sea_orm(unique)]
    pub account_no: String,
    pub pwd: String,
    pub user_id: i64,
    pub agent_id: i64,
    pub user_organ_code: String,
    pub sale_user_id: i64,
    pub create_time: i64,
    pub status: i8,
    pub group_type: String,
    pub r#type: i8,
    pub dm_id: i64,
    pub lever: i32,
    pub account_type: Option<i8>,
    pub use_type: Option<u8>,
    pub email: Option<String>,
    pub agent_account_no: Option<String>,
    #[sea_orm(column_type = "Decimal(Some((15, 2)))", nullable)]
    pub money: Option<Decimal>,
    pub crmgroup_id: i64,
    pub account_cate: i8,
    pub follow_no: i32,
    pub currency: String,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, Enum<PERSON><PERSON>, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
