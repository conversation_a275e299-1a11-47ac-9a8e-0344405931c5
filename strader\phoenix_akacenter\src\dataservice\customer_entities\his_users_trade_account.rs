//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "his_users_trade_account")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    #[sea_orm(unique)]
    pub account_no: String,
    pub pwd: String,
    pub user_id: i64,
    pub agent_id: i64,
    pub user_organ_code: String,
    pub sale_user_id: i64,
    pub create_time: i64,
    pub status: i8,
    pub group_type: String,
    pub business_type: i8,
    pub dm_id: i64,
    pub lever: i32,
    pub account_type: Option<i8>,
    pub use_type: Option<u8>,
    pub account_cate: i8,
    pub follow_no: i32,
    pub currency: String,
    pub online: i8,
    pub last_login_time: i64,
    pub open_source: i8,
    pub bi_no: String,
    pub infomation_complate: i8,
    pub into_ctrl: i8,
    pub withdraw_ctrl: i8,
    pub transfer_ctrl: i8,
    #[sea_orm(unique)]
    pub organ_code: Option<String>,
    #[sea_orm(primary_key, auto_increment = false)]
    pub c_date: i64,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
