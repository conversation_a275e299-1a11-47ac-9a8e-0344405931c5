syntax = "proto3";

package managerunit;

option go_package = "/pb";

service ManagerUnit {
  rpc ModifyUnitState(ModifyUnitReq) returns (ModifyUnitResp) {
  } // 触发风控通知管理端修改用户状态，同时发送邮件给运营
  rpc ModifyRq(ModifyRqReq) returns (ModifyRqResp) {} // 融券召回操作
  rpc ModifyAlgorithm(ModifyAlgorithmReq) returns (ModifyAlgorithmResp) {
  } // 算法单更新
  rpc QueryAlgorithm(AlgorithmReq) returns (AlgorithmResp) {} // 算法单查询
  rpc QueryOptionQuotation(OptionQuotationReq) returns (OptionQuotationResp) {
  } // 期权自动报价基础数据查询
}

message ModifyUnitReq {
  int64 user_id = 1; // 用户id
  int32 status = 2;  // 1:冻结，2：解冻
  int32 push_msg =
      3; // 冻结时是否发送邮件通知运营，0：不发送，1：触发预警，2：触发平仓预警
  double rate = 4; // 当前风险率
}

message ModifyUnitResp {
  string err_msg = 1;
  sint32 err_code = 2;
}

message ModifyRqReqList {
  int64 stock_id = 1; // 品种id
  int64 user_id = 2;  // 用户id
  int32 use_num = 3;  // 已用 >-1更新; =-1召回
}

message ModifyRqReq { repeated ModifyRqReqList rqlist = 1; }

message ModifyRqResp {
  string err_msg = 1;
  sint32 err_code = 2;
}

message ModifyAlgorithmReq {
  int64 uid = 1;               // 算法单id
  int32 ordernum = 2;          // 委托数量
  int32 dealnum = 3;           // 成交数量
  int64 last_execute_time = 4; // 上一次执行时间
  int32 status = 5;            // 算法单状态
}

message ModifyAlgorithmResp {
  string err_msg = 1;
  sint32 err_code = 2;
}

message AlgorithmReq {}

message AlgorithmResp {
  string err_msg = 1;
  sint32 err_code = 2;
  repeated QueryAlgorithmResp data = 3; // 算法未执行或者执行中的记录
}

message QueryAlgorithmResp {
  int64 algorithm_id = 1;   // 算法单id
  int64 exchange_id = 2;    // 市场id
  string stock_code = 3;    // 品种代码
  int64 user_id = 4;        // 用户id
  int32 algorithm_type = 5; // 算法类型
  int64 stock_id = 6;       // 品种id
  int64 start_date = 7;     // 开始时间
  int64 end_date = 8;       // 结束时间
  int32 price_type = 9;     //'价格类型，1：限价，2：市价',
  double price_num = 10;  //'价格/市价 1：对手一档，2：对手二挡'
  int32 order_num = 11;   // 订单数量
  int32 order_type = 12;  //'1:买，2：卖',
  int32 entrust_num = 13; // 已委托数量
  int32 deal_num = 14;    // 已成交数量
  int64 create_date = 15; // 创建时间
  string trigger_condition = 16; // trigger类型时，条件的触发条件，>= 等
  double trigger_price = 17;    // trigger类型时，触发价格
  int32 interval_time = 18;     // 间隔时间
  int64 last_execute_time = 19; // 上一次执行时间
  int32 market_type = 20; // 市场类型，1：港股，2：美股，3：沪深
  int32 hands_num = 21;   // 每手股数
  string market_code = 22;  // 市场代码
  int32 maximum_order = 23; // 最大下单量
  int64 unit_id = 24;       // 用户id
}

message OptionQuotationReq {}

message OptionQuotationResp {
  string err_msg = 1;
  sint32 err_code = 2;
  repeated OptionQuotationDataResp data = 3;
}

message OptionQuotationDataResp {
  string stock_code = 1;
  int32 market_id = 2;
  double max_limit = 3;
  double used_limit = 4;
  repeated OptionQuotationMonthResp month_limit = 5;
}

message OptionQuotationMonthResp {
  string structs = 1; // 结构 100% ，90%等
  string months = 2;  // 期限 1M,2M等
  string cates = 3;   // T+1,T+5,两种
  string rates = 4;   //  10.01%
}