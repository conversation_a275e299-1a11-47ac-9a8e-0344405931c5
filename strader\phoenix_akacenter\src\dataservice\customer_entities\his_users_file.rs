//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "his_users_file")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub file_url: Option<String>,
    pub file_suffix: String,
    pub file_name: String,
    pub file_type: i8,
    pub create_time: i64,
    pub create_name: String,
    pub upload_type: i8,
    pub upload_source: i8,
    pub user_id: i64,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
