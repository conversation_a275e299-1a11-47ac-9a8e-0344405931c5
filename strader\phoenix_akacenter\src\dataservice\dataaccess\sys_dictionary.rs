use crate::dataservice::{
    entities::{
        prelude::{SysDictionary, SysDictionaryEntity},
        sys_dictionary,
    },
};
use dbconnection::DbConnection;
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, DbErr, EntityTrait, QueryFilter};

impl sys_dictionary::Model {
    pub async fn find_all(db: &DbConnection, dicno: i32) -> Result<Vec<SysDictionary>> {
        let ret_data: Result<Vec<SysDictionary>, DbErr> = SysDictionaryEntity::find().filter(sys_dictionary::Column::DictionaryNo.eq(dicno)).all(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }
}
