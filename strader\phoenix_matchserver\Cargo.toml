[package]
name = "phoenix_matchserver"
version = "0.2.0"
edition = "2021"
description = "撮合服务 - build time: 2025-07-07"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
utility = { workspace = true }
common = { workspace = true }
messagecenter = { workspace = true }
akaclient = { workspace = true }
config = { workspace = true }
protoes = { workspace = true }

accountriskcenterclient = { workspace = true }

serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
futures = { workspace = true }
lapin = { workspace = true }
tonic = { workspace = true }
prost = { workspace = true }
notify = { workspace = true }
rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }
async-channel = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
tokio-stream = { workspace = true }
# async-stream = { workspace = true }
dashmap = { workspace = true }
# 时间
chrono = { workspace = true }
time = { workspace = true }

# log = { workspace = true }
tracing = { workspace = true }

# [build-dependencies]
# # tonic-build = { workspace = true, features = ["prost"] }
# tonic-build.workspace = true

#[[bin]]
#name="test1"
#path="src/test1.rs"
#[[bin]]
#name="test2"
#path="src/test2.rs"
