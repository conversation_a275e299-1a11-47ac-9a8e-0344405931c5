//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "tstockposition")]
pub struct Model {
    #[sea_orm(column_type = "Decimal(Some((8, 0)))")]
    pub l_date: Decimal,
    #[sea_orm(primary_key)]
    pub l_position_no: i64,
    #[sea_orm(column_type = "Decimal(Some((8, 0)))")]
    pub l_account_id: Decimal,
    #[sea_orm(column_type = "Decimal(Some((8, 0)))")]
    pub l_unit_id: Decimal,
    pub vc_stock_code: Option<String>,
    pub l_exchange_id: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub c_position_flag: String,
    pub l_begin_amount: i32,
    pub l_current_amount: i32,
    pub l_frozen_amount: i32,
    pub l_buy_amount: i32,
    pub l_sale_amount: i32,
    pub l_prebuy_amount: i32,
    pub l_presale_amount: i32,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_buy_capital: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub en_sale_capital: Decimal,
    #[sea_orm(column_type = "Decimal(Some((12, 2)))")]
    pub en_buy_fee: Decimal,
    #[sea_orm(column_type = "Decimal(Some((12, 2)))")]
    pub en_sale_fee: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))", nullable)]
    pub en_current_cost: Option<Decimal>,
    #[sea_orm(column_type = "Decimal(Some((16, 8)))", nullable)]
    pub en_avg_price: Option<Decimal>,
    #[sea_orm(column_type = "Decimal(Some((20, 8)))", nullable)]
    pub en_total_value: Option<Decimal>,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))", nullable)]
    pub en_trade_capital: Option<Decimal>,
    #[sea_orm(column_type = "Decimal(Some((12, 2)))", nullable)]
    pub en_real_buy_fee: Option<Decimal>,
    #[sea_orm(column_type = "Decimal(Some((12, 2)))", nullable)]
    pub en_real_sale_fee: Option<Decimal>,
    pub l_temp_frozen_amount: Option<i32>,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))", nullable)]
    pub en_today_profit: Option<Decimal>,
    pub l_stock_id: Option<i32>,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))", nullable)]
    pub en_last_price: Option<Decimal>,
    pub l_buy_in_transit: Option<i32>,
    pub l_sale_in_transit: Option<i32>,
    pub l_channel_id: Option<i32>,
    pub l_stock_type: i32,
    #[sea_orm(column_type = "Decimal(Some((20, 4)))")]
    pub en_total_value_hkd: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 4)))")]
    pub en_begin_value_hkd: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 4)))")]
    pub en_today_total_value_hkd: Decimal,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
