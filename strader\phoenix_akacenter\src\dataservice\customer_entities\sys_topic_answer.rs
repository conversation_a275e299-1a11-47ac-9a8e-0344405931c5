//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "sys_topic_answer")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub answer_desc: String,
    pub answer_sort: i8,
    pub topic_id: i64,
    pub relation_text_state: i8,
    pub answer_score: i8,
    pub create_time: i32,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
