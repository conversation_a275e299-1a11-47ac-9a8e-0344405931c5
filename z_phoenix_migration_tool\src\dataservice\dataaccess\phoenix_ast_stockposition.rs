use crate::dataservice::dataaccess::tstockposition::TStockPositionResult;
// use crate::dataservice::old_stock_entities::prelude::Tstockposition;
use crate::dataservice::stock_entities::{phoenix_ast_stockposition, prelude::*};
use anyhow::{anyhow, Result};
use rust_decimal::prelude::{ToPrimitive, Zero};
use rust_decimal::Decimal;
use sea_orm::{ActiveModelTrait, ColumnTrait, ConnectionTrait, DbErr, EntityTrait, QueryFilter, QuerySelect};
use serde_json::json;

impl phoenix_ast_stockposition::Model {
    pub async fn find_id_by_unit_id<'a, 'async_trait, C>(db: &'a C, unit_id: i64, stock_id: i64) -> Result<Option<i64>>
    where
        C: ConnectionTrait,
        'a: 'async_trait,
        C: 'async_trait,
    {
        let ret_data: Result<Option<i64>, DbErr> = PhoenixAstStockpositionEntity::find()
            .filter(phoenix_ast_stockposition::Column::UnitId.eq(unit_id))
            .filter(phoenix_ast_stockposition::Column::StockId.eq(stock_id))
            .select_only()
            .column(phoenix_ast_stockposition::Column::Id)
            .into_tuple()
            .one(db)
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(format!("PhoenixAstStockposition: {:?}", ret_data.err())));
        }

        Ok(ret_data.unwrap())
    }

    pub async fn save_many<'a, 'async_trait, C>(info: &mut Vec<PhoenixAstStockposition>, db: &'a C) -> Result<i64>
    where
        C: ConnectionTrait,
        'a: 'async_trait,
        C: 'async_trait,
    {
        if info.len() <= 0 {
            return Err(anyhow!("empty data"));
        }

        let mut insert_values: Vec<phoenix_ast_stockposition::ActiveModel> = Vec::new();
        let mut update_values: Vec<phoenix_ast_stockposition::ActiveModel> = Vec::new();
        for val in info.iter_mut() {
            let opt = PhoenixAstStockposition::find_id_by_unit_id(db, val.unit_id, val.stock_id).await?;
            match opt {
                None => {}
                Some(id) => {
                    log::info!("phoenix_ast_stockposition 更新id: {}", id);
                    val.id = id;
                }
            }

            let ret = phoenix_ast_stockposition::ActiveModel::from_json(json!(val));
            if ret.as_ref().is_err() {
                return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
            }
            if val.id <= 0 {
                insert_values.push(ret.unwrap());
            } else {
                update_values.push(ret.unwrap());
            }
        }

        let mut last_insert_id = 0;

        // do insert
        if insert_values.len() > 0 {
            let ret = PhoenixAstStockpositionEntity::insert_many(insert_values).exec(db).await;
            if ret.as_ref().is_err() {
                return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
            }

            let ids = ret.unwrap();
            last_insert_id = ids.last_insert_id;
        }

        //do update
        if update_values.len() > 0 {
            for val in update_values {
                let ret = val.update(db).await;
                if ret.as_ref().is_err() {
                    return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
                }
            }
        }

        Ok(last_insert_id)
    }

    pub async fn insert_many<'a, 'async_trait, C>(info: &Vec<PhoenixAstStockposition>, db: &'a C) -> Result<i64>
    where
        C: ConnectionTrait,
        'a: 'async_trait,
        C: 'async_trait,
    {
        if info.len() <= 0 {
            return Err(anyhow!("empty data"));
        }

        let mut insert_values: Vec<phoenix_ast_stockposition::ActiveModel> = Vec::new();
        for val in info {
            let opt = PhoenixAstStockposition::find_id_by_unit_id(db, val.unit_id, val.stock_id).await?;
            match opt {
                None => {}
                Some(_) => return Ok(0),
            }

            let ret = phoenix_ast_stockposition::ActiveModel::from_json(json!(val));
            if ret.as_ref().is_err() {
                return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
            }

            insert_values.push(ret.unwrap());
        }

        let mut last_insert_id = 0;

        // do insert
        if insert_values.len() > 0 {
            let ret = PhoenixAstStockpositionEntity::insert_many(insert_values).exec(db).await;
            if ret.as_ref().is_err() {
                return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
            }

            let ids = ret.unwrap();
            last_insert_id = ids.last_insert_id;
        }

        Ok(last_insert_id)
    }

    pub async fn covert_form_tstockposition(src: &TStockPositionResult, credit_multiple: Decimal) -> Result<Self> {
        Ok(PhoenixAstStockposition {
            id: 0,
            sys_date: src.l_date.to_i32().unwrap_or_default(),
            unit_id: src.l_unit_id.to_i64().unwrap_or_default(),
            stock_code: src.vc_stock_code.as_ref().unwrap_or(&"".to_string()).to_string(),
            stock_id: src.l_stock_id.unwrap_or_default() as i64,
            exchange_id: src.l_exchange_id,
            position_flag: src.c_position_flag.parse().unwrap_or_default(),
            begin_amount: src.l_begin_amount.to_i32().unwrap_or_default(),
            current_amount: src.l_current_amount.to_i32().unwrap_or_default(),
            frozen_amount: src.l_frozen_amount.to_i32().unwrap_or_default(),
            temp_frozen_amount: src.l_temp_frozen_amount.unwrap_or_default().to_i32().unwrap_or_default(),
            buy_amount: src.l_buy_amount.to_i32().unwrap_or_default(),
            sale_amount: src.l_sale_amount.to_i32().unwrap_or_default(),
            prebuy_amount: src.l_prebuy_amount.to_i32().unwrap_or_default(),
            presale_amount: src.l_presale_amount.to_i32().unwrap_or_default(),
            buy_fee: src.en_buy_fee,
            sale_fee: src.en_sale_fee,
            real_buy_fee: src.en_real_buy_fee.unwrap_or_default(),
            real_sale_fee: src.en_real_sale_fee.unwrap_or_default(),
            current_cost: src.en_current_cost.unwrap_or_default(),
            avg_price: match src.l_current_amount.eq(&Decimal::zero()) {
                true => Decimal::zero(),
                false => src.en_total_value.unwrap_or_default() / src.l_current_amount,
            },
            total_value: src.en_total_value.unwrap_or_default(),
            total_value_hkd: src.en_total_value_hkd,
            begin_value_hkd: src.en_begin_value_hkd,
            today_total_value_hkd: src.en_today_total_value_hkd,
            last_price: src.en_last_price.unwrap_or_default(),
            buy_in_transit: src.l_buy_in_transit.unwrap_or_default().to_i32().unwrap_or_default(),
            sale_in_transit: src.l_sale_in_transit.unwrap_or_default().to_i32().unwrap_or_default(),
            qf_amount: 0,
            stock_type: src.l_stock_type,
            margin_rate: Decimal::from(1) / (Decimal::from(1) + credit_multiple),
            position_no: src.l_position_no,
            create_time: 0,
            last_modify_time: 0,
            use_quota: 0,
        })
    }
}
