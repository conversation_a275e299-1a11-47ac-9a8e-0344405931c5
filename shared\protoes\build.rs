use std::fs;

fn build_hq_grpc(outdir: &str) {
    tonic_build::configure()
        .out_dir(outdir)
        .type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize)]")
        .compile_protos(
            &[
                // "protofiles/hqprotofiles/fiuweb.proto",
                // "protofiles/hqprotofiles/hk.proto",
                "protofiles/hqprotofiles/hqmsg.proto",
                // "protofiles/hqprotofiles/hs.proto",
                "protofiles/hqprotofiles/marketdata.proto",
                "protofiles/hqprotofiles/phoenixklinecenter.proto",
                "protofiles/hqprotofiles/phoenixtickcenter.proto",
                "protofiles/hqprotofiles/stockaid.proto",
                "protofiles/hqprotofiles/subscribehqmsg.proto",
                "protofiles/hqprotofiles/usserver.proto",
            ],
            &["protofiles/hqprotofiles"],
        )
        .expect("build failed");
}

fn build_grpc(outdir: &str) {
    tonic_build::configure()
        .out_dir(outdir)
        .type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize)]")
        .compile_protos(
            &[
                "protofiles/notification.proto",
                "protofiles/accountriskcenter.proto",
                "protofiles/akacenter.proto",
                "protofiles/algorithmcenter.proto",
                "protofiles/assetscenter.proto",
                // "protofiles/basicdataservice.proto",
                "protofiles/blackscholes.proto",
                "protofiles/exchanger.proto",
                // "protofiles/HqMsg.proto",
                "protofiles/logcenter.proto",
                "protofiles/managerunit.proto",
                "protofiles/matchserver.proto",
                "protofiles/messagerouter.proto",
                "protofiles/migrationtool.proto",
                // "protofiles/notification.proto",
                "protofiles/omstradeapi.proto",
                "protofiles/ordercenter.proto",
                "protofiles/ordermsg.proto",
                "protofiles/orderrouter.proto",
                // "protofiles/omstradeapi.proto",
                "protofiles/riskcenter.proto",
                "protofiles/satcenter.proto",
                "protofiles/settlecenter.proto",
                // "protofiles/StockAid.proto",
                // "protofiles/SubscribeHqMsg.proto",
                // "protofiles/usserver.proto",
            ],
            &["protofiles"],
        )
        .expect("build failed");
}

fn main() {
    let outdir = "src/hqprotos";
    fs::create_dir_all(outdir).unwrap();
    build_hq_grpc(outdir);

    let outdir = "src/protos";
    fs::create_dir_all(outdir).unwrap();
    build_grpc(outdir);
}
