use crate::dataservice::{
    dbsetup::DbConnection,
    entities::{
        prelude::{UsersAgentTrading, UsersAgentTradingEntity},
        users_agent_trading,
    },
};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, DbErr, EntityTrait, QueryFilter};
// use tracing::*;

impl UsersAgentTrading {
    pub async fn find_by_user_id(db: &DbConnection, user_id: i64) -> Result<Option<UsersAgentTrading>> {
        let ret_data: Result<Option<UsersAgentTrading>, DbErr> = UsersAgentTradingEntity::find().filter(users_agent_trading::Column::UserId.eq(user_id)).one(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        let data = ret_data.unwrap();
        Ok(data)
        // match UsersAgentTradingEntity::find().filter(users_agent_trading::Column::UserId.eq(user_id)).one(db.get_connection()).await {
        //     Ok(data) => {
        //         if data.is_none() {
        //             error!("查询数据为空,user_id: {}", user_id);
        //             return Err(anyhow!("查询数据为空,user_id: {}", user_id));
        //         }
        //         Ok(data.unwrap())
        //     }
        //     Err(err) => {
        //         error!("查询数据失败,user_id: {},err: {}", user_id, err);
        //         return Err(anyhow!(err));
        //     }
        // }
    }

    pub async fn _find_account_all(db: &DbConnection) -> Result<Vec<UsersAgentTrading>> {
        let ret_data: Result<Vec<UsersAgentTrading>, DbErr> = UsersAgentTradingEntity::find().all(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }
}
