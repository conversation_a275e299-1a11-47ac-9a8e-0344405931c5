use std::sync::Arc;
use std::vec;

use crate::controller::PersistData;
use protoes::assetscenter::{PhoenixassetscenterRequestInfo, Phoenixassetspostioninfo, PositionMarginRate, PositionPriceChangeItemReq};

use super::assetsmodel::PositionsData;
use crate::dataservice::entities::prelude::*;
use crate::service::assetsmodel::AssetsData;
use crate::{app::constdata, dataservice::dbsetup::DbConnection};
use akaclient::akaclient::AkaClient;
use anyhow::{anyhow, Result};
use common::constant;
use common::logclient::{log_error, LogClient, LogLevel};
use common::redisclient::redispool::RedisClient;
use common::uidservice::UidgenService;
use protoes::phoenixnotification::NotificationPosition;
use rust_decimal::Decimal;
use tokio::sync::RwLock;
use tracing::*;
use utility::timeutil;

pub struct UnitPositionService {}

//主要业务逻辑：
//1）初始化时，从数据库读取资产并保存到redis
//2) 查询时直接从redis读取（无锁）
//3) 资产调整时，从redis读取数据（加锁），更新后写回redis，并解锁
impl UnitPositionService {
    pub fn new() -> Self {
        UnitPositionService {}
    }

    pub async fn push_log(&self, log: String) {
        error!("{}", log);
        let logclient = LogClient::get();
        if let Ok(c) = logclient {
            _ = c.push(LogLevel::Error, log.as_str()).await;
        }
    }

    pub async fn init(&self, unit_id: i64, stock_id: i64, db: &DbConnection) -> Result<Vec<PhoenixAstStockposition>> {
        let ret = PhoenixAstStockposition::query_many(unit_id, stock_id, db).await;
        if let Err(err) = ret {
            self.push_log(format!("查询用户持仓失败:{:?}", err)).await;
            return Err(err);
        }
        let retdata = ret.unwrap();
        Ok(retdata)
    }

    pub async fn query_position(&self, unit_id: i64, stock_id: i64, db: &DbConnection) -> Result<Option<PhoenixAstStockposition>> {
        let ret = self.init(unit_id, stock_id, db).await;
        if let Err(err) = ret {
            return Err(err);
        }
        let retdata = ret.unwrap();
        for item in retdata {
            if item.stock_id == stock_id {
                return Ok(Some(item));
            }
        }
        Ok(None)
    }

    pub async fn postions_change(
        &self,
        req: &PositionsData,
        db: &DbConnection,
        init_date: i32,
        current_rate_buy: f64,
        current_rate_sell: f64,
        uidgen: &Arc<RwLock<UidgenService>>,
        akaclient: &AkaClient,
        special_lion_unitid: i64,
    ) -> Result<(NotificationPosition, Vec<PersistData>, Option<AssetsData>)> {
        info!("postions_change,service入口,{:?}", req);

        let mut item = req.positions.to_owned();
        let position_result = self.query_position(req.unit_id, item.stock_id, db).await;
        if let Err(err) = position_result {
            self.push_log(format!("查询持仓失败,{},{},{:?}", req.unit_id, item.stock_id, err)).await;
            return Err(anyhow!("持仓查询失败{}", req.unit_id));
        }

        let position_op = position_result.unwrap();
        info!("postions_change缓存数据1,{},{:?}", req.message_id, position_op);

        // let mut position_no: i64 = 0;
        // {
        //     position_no = uidgen.write().await.get_uid();
        // }
        let position_no = uidgen.write().await.get_uid();

        let system_date = init_date;
        let mut update_position = PhoenixAstStockposition {
            sys_date: system_date,
            unit_id: req.unit_id,
            user_id: req.user_id,
            stock_id: item.stock_id,
            position_flag: item.position_flag,
            position_no: position_no,
            create_time: timeutil::current_date() as i64,
            ..Default::default()
        };

        if update_position.user_id == 0 {
            let ret = PhoenixAstUnitasset::find_by_userid(req.unit_id, req.user_id, req.currency.clone(), db).await;
            if let Err(err) = ret {
                self.push_log(format!("db查询资产失败:{:?}", err)).await;
                return Err(err);
            }

            let ret = ret.unwrap();
            if ret.is_none() {
                self.push_log(format!("db查询资产失败:{}", req.unit_id)).await;
                return Err(anyhow!("查询资产失败"));
            }
            let ret = ret.unwrap();
            update_position.user_id = ret.user_id
        }

        if position_op.is_some() {
            let p = position_op.unwrap();
            //判断是否是今日开仓记录,历史持仓记录当前数量为0也是一直存在的。，数据这边重算生成持仓
            if p.current_amount == 0 {
                //是否需要重新生成持仓状态,false 代表不需要，true为需要
                let mut flag = false;
                let current_date = timeutil::current_date() as i64;
                if p.create_time != current_date {
                    if p.sale_amount == 0 && p.buy_amount == 0 && p.begin_amount == 0 && p.sale_in_transit == 0 && p.buy_in_transit == 0 {
                        flag = true;
                    }
                }
                if !flag {
                    update_position = p;
                } else {
                    update_position.id = p.id;
                }
            } else {
                update_position = p;
            }
        }

        if update_position.exchange_id == 0 {
            let stock_op = akaclient.query_stock_info(item.stock_id).await;
            if let Err(_) = stock_op {
                self.push_log(format!("查询持仓品种失败,{}", item.stock_id)).await;
                return Err(anyhow!("查询持仓品种失败,{}", item.stock_id));
            }
            let stock = stock_op.unwrap();
            if stock.is_none() {
                self.push_log(format!("持仓品种不存在,{}", item.stock_id)).await;
                return Err(anyhow!("持仓品种不存在,{}", item.stock_id));
            }

            let stock = stock.unwrap();
            update_position.stock_code = stock.stock_code;
            update_position.exchange_id = stock.market_id as i32;
            update_position.stock_type = stock.stock_type;
        }

        if req.positions.margin_rate > 0.0 {
            update_position.margin_rate = Decimal::from_f64_retain(req.positions.margin_rate).unwrap_or_default();
        }

        //默认多方向
        if update_position.position_flag == 0 {
            update_position.position_flag = 1;
        }
        let mut current_amount = 0;
        let mut buy_amount = 0;
        let mut sale_amount = 0;
        let mut frozen_amount = 0;
        let mut buy_fee: f64 = 0.0;
        let mut sale_fee: f64 = 0.0;
        //总成本
        let mut rate = 1.0;
        let mut temp_frozen_amount = 0;
        let mut prebuy_amount = 0;
        let mut presale_amount = 0;
        let mut buy_in_transit = 0;
        let mut sale_in_transit = 0;
        let mut avg_price: f64 = 0.0;
        let mut avg_price_hkd: f64 = 0.0;
        let mut last_price: f64 = 0.0;
        let mut currency = String::from("HKD");
        //是否有用户实现盈亏
        let mut assetsflag = false;
        if update_position.exchange_id == constant::MarketCode::XSHG as i32 || update_position.exchange_id == constant::MarketCode::XSHE as i32 {
            currency = "CNY".to_string();
        }

        let mut t_value = update_position.total_value.to_string().parse::<f64>().unwrap_or_default();
        let mut t_value_hkd = update_position.total_value_hkd.to_string().parse::<f64>().unwrap_or_default();
        let mut current_cost = update_position.current_cost.to_string().parse::<f64>().unwrap_or_default();
        if update_position.current_amount != 0 {
            avg_price = t_value / (update_position.current_amount as f64);
            avg_price_hkd = t_value_hkd / (update_position.current_amount as f64);
            last_price = item.deal_price;
        }

        if item.deal_amount < 0 {
            if item.op_type == constant::AssetChangeDirection::ReducePosition as i32 {
                item.op_type = constant::AssetChangeDirection::AddPosition as i32;
                item.deal_amount = 0 - item.deal_amount;
            } else if item.op_type == constant::AssetChangeDirection::AddPosition as i32 {
                item.op_type = constant::AssetChangeDirection::ReducePosition as i32;
                item.deal_amount = 0 - item.deal_amount;
            }
        }

        //汇率处理
        if item.op_type == constant::AssetChangeDirection::AddPosition as i32 {
            if update_position.exchange_id as i64 != constant::EXCHANGE_HK {
                rate = current_rate_buy
            }
        } else if item.op_type == constant::AssetChangeDirection::ReducePosition as i32 {
            if update_position.exchange_id as i64 != constant::EXCHANGE_HK {
                rate = current_rate_sell
            }
        }

        //计算成交记录实现盈亏，按持仓币种算
        //计算用户资金实现盈亏，按港币成本算
        //1、若持仓为负，且买多仓时，持仓数量+成交数量>=0 ,实现盈亏部分数量为持仓数量，否则为0-成交量数量
        //2、若持仓为正，且卖空仓时，持仓数量-成交数量<=0时，实现盈亏部分数量为持仓数量，否则为成交量数量
        //其他情况不需要计算实现盈亏
        let mut amount = 0;
        if item.op_type == constant::AssetChangeDirection::AddPosition as i32 && update_position.current_amount < 0 {
            if update_position.current_amount + item.deal_amount >= 0 {
                amount = update_position.current_amount;
            } else {
                amount = 0 - item.deal_amount;
            }
            assetsflag = true;
        } else if item.op_type == constant::AssetChangeDirection::ReducePosition as i32 && update_position.current_amount > 0 {
            if update_position.current_amount - item.deal_amount <= 0 {
                amount = update_position.current_amount;
            } else {
                amount = item.deal_amount;
            }
            assetsflag = true;
        }
        let mut dealyk = 0.0;
        let mut assetsyk = 0.0;
        if assetsflag {
            dealyk = (item.deal_price - avg_price) * amount as f64;
            assetsyk = (item.deal_price * rate - avg_price_hkd) * amount as f64;
            info!(
                "dealyk{},deal_price:{},avg_price:{},rate:{},avg_price_hkd:{},amount:{}",
                dealyk, item.deal_price, avg_price, rate, avg_price_hkd, amount
            );
        }

        //计算用户的持仓成本和港币成本
        if item.op_type == constant::AssetChangeDirection::AddPosition as i32 {
            //无实现盈亏时，成本一直累计
            if !assetsflag {
                t_value = t_value + (item.deal_amount as f64) * item.deal_price;
                t_value_hkd = t_value_hkd + (item.deal_amount as f64) * item.deal_price * rate;
            } else {
                //当由负持仓变成正持仓时，成本取正持仓那部分
                if update_position.current_amount + item.deal_amount >= 0 {
                    t_value = (update_position.current_amount + item.deal_amount) as f64 * item.deal_price;
                    t_value_hkd = (update_position.current_amount + item.deal_amount) as f64 * item.deal_price * rate;
                } else {
                    //负持仓买入时
                    t_value = t_value + (item.deal_amount as f64) * avg_price;
                    t_value_hkd = t_value_hkd + (item.deal_amount as f64) * avg_price_hkd;
                }
            }
            current_cost = current_cost + (item.deal_amount as f64) * item.deal_price + item.fee_value;
        } else if item.op_type == constant::AssetChangeDirection::ReducePosition as i32 {
            //无实现盈亏时，成本一直减
            if !assetsflag {
                t_value = t_value - (item.deal_amount as f64) * item.deal_price;
                t_value_hkd = t_value_hkd - (item.deal_amount as f64) * item.deal_price * rate;
            } else {
                //若由正持仓变成负持仓时
                if update_position.current_amount - item.deal_amount <= 0 {
                    t_value = (update_position.current_amount - item.deal_amount) as f64 * item.deal_price;
                    t_value_hkd = (update_position.current_amount - item.deal_amount) as f64 * item.deal_price * rate;
                } else {
                    //正持仓卖时
                    t_value = t_value - (item.deal_amount as f64) * avg_price;
                    t_value_hkd = t_value_hkd - (item.deal_amount as f64) * avg_price_hkd;
                }
            }
            current_cost = current_cost - (item.deal_amount as f64) * item.deal_price + item.fee_value;
        }

        //计算其他值
        if item.op_type == constant::AssetChangeDirection::AddPosition as i32 {
            current_amount = item.deal_amount;
            buy_amount = item.deal_amount;
            buy_fee = item.fee_value;
            if update_position.exchange_id as i64 == constant::EXCHANGE_HK {
                buy_in_transit = item.deal_amount;
            }
            if update_position.exchange_id == constant::MarketCode::XSHG as i32 || update_position.exchange_id == constant::MarketCode::XSHE as i32 {
                if special_lion_unitid != req.unit_id {
                    frozen_amount = item.deal_amount;
                }
            }
            //预买减少
            prebuy_amount = 0 - item.deal_amount;
            last_price = item.deal_price;
        } else if item.op_type == constant::AssetChangeDirection::ReducePosition as i32 {
            current_amount = 0 - item.deal_amount;
            sale_amount = item.deal_amount;
            sale_fee = item.fee_value;
            if update_position.exchange_id as i64 == constant::EXCHANGE_HK {
                sale_in_transit = item.deal_amount;
            }
            presale_amount = 0 - item.deal_amount;
            temp_frozen_amount = 0 - item.deal_amount;
            last_price = item.deal_price;
        } else {
            prebuy_amount = item.prebuy_amount;
            presale_amount = item.presale_amount;
            if presale_amount != 0 {
                temp_frozen_amount = presale_amount;
            }
        }

        //计算已用额度，若是卖，且期初-今卖小于0的，则累加已用额度
        // if item.op_type == constant::AssetChangeDirection::ReducePosition as i32 {
        //   //若不够卖，占用的是融券额度
        //   let d = update_position.begin_amount - update_position.sale_amount - update_position.presale_amount;
        //   if d <= 0 {
        //     use_quota = item.deal_amount;
        //   } else {
        //     if d - item.deal_amount < 0 {
        //       use_quota = item.deal_amount - d;
        //     }
        //   }
        // }

        update_position.current_amount += current_amount;
        update_position.buy_amount += buy_amount;
        update_position.sale_amount += sale_amount;
        update_position.frozen_amount += frozen_amount;
        update_position.buy_fee += Decimal::from_f64_retain(buy_fee).unwrap_or_default();
        update_position.sale_fee += Decimal::from_f64_retain(sale_fee).unwrap_or_default();
        update_position.current_cost = Decimal::from_f64_retain(current_cost).unwrap_or_default();
        update_position.total_value = Decimal::from_f64_retain(t_value).unwrap_or_default();
        //最新成交价格作为最新价
        if last_price > 0.0 {
            update_position.last_price = Decimal::from_f64_retain(last_price).unwrap_or_default();
        }
        update_position.total_value_hkd = Decimal::from_f64_retain(t_value_hkd).unwrap_or_default();
        update_position.temp_frozen_amount += temp_frozen_amount;
        update_position.prebuy_amount += prebuy_amount;
        update_position.presale_amount += presale_amount;
        update_position.buy_in_transit += buy_in_transit;
        update_position.sale_in_transit += sale_in_transit;
        update_position.last_modify_time = timeutil::current_timestamp();

        let p = update_position.clone();
        //若是新建持仓
        if update_position.id == 0 {
            info!("PhoenixAstStockposition插入记录{:?}", update_position);
            let ret = PhoenixAstStockposition::insert(&update_position, db).await;
            if let Err(err) = ret {
                self.push_log(format!("持仓插入失败:{:?},err:{:?}", update_position, err)).await;
                return Err(anyhow!("持仓插入异常"));
            }
            update_position.id = ret.unwrap()
        } else {
            _ = self.update_positions_dbdata(&vec![p], db).await;
        }

        info!("postions_change更新缓存数据,{},{:?}", req.message_id, update_position);
        // //更新redis
        // let ret = self.update_position_redis(req.unit_id, item.stock_id, &update_position, redis).await;

        // if let Err(err) = ret {
        //   self.push_log(format!("update_position_redis,redis更新失败：{:?},{:?}", req, err)).await;
        //   return Err(anyhow!("持仓缓存更新异常"));
        // }

        //查询资产缓存,更新请求过快时，可能查询出来得还是上上次的redi缓存数据。导致更新被覆盖，一做时间延迟，二做日志查询
        // let position_result2 = self.query_position(req.unit_id, item.stock_id, redis, db, init_date).await;
        // info!("postions_change缓存数据2,{},{:?}", req.message_id, position_result2);

        //更新数据库
        let mut persist_data_vec: Vec<PersistData> = Vec::new();

        //插入资金流水
        if item.op_type == constant::AssetChangeDirection::AddPosition as i32 || item.op_type == constant::AssetChangeDirection::ReducePosition as i32 {
            let rate_deciaml = Decimal::from_f64_retain(rate);
            let flow = PhoenixOmsAssetflow {
                sys_date: system_date.to_owned(),
                unit_id: update_position.unit_id,
                user_id: update_position.user_id,
                busin_flag: req.business_flag,
                occur_capital: Decimal::new(item.deal_amount as i64, 0),
                post_capital: Decimal::new(current_amount as i64, 0),
                datetime: timeutil::current_timestamp(),
                remarks: item.deal_no.clone().to_string(),
                op_type: item.op_type,
                currency_no: currency.clone(),
                currency_rate: rate_deciaml.unwrap(),
                ..Default::default()
            };

            let operator_id = req.operator_id as i32;
            //创建资金操作表记录
            let option_detail = PhoenixAstOperationDetail {
                sys_date: system_date.to_owned(),
                unit_id: update_position.unit_id,
                user_id: update_position.user_id,
                op_businflag: req.business_flag.to_string(),
                remark: item.deal_no.clone().to_string(),
                currency_no: currency,
                create_time: timeutil::current_timestamp(),
                operator: operator_id,
                op_type: item.op_type,
                occur_capital: Decimal::new(item.deal_amount as i64, 0),
                ..Default::default()
            };
            let data = (flow, option_detail);

            persist_data_vec.push(PersistData::PhoenixOmsAssetflow(Box::new(data)));
        }

        let mut ret_assetsyk = None;
        //插入交易盈亏更新记录
        if assetsflag {
            if dealyk != 0.0 {
                let yk = Decimal::from_f64_retain(dealyk).unwrap_or_default();
                persist_data_vec.push(PersistData::PhoenixOrdStockdeal(Box::new(PhoenixOrdStockdeal {
                    deal_no: item.deal_no,
                    refer_profit: yk,
                    ..Default::default()
                })));
            }

            //资产更新记录
            let mut optype = constant::AssetChangeDirection::AddCapital as i32;
            if assetsyk < 0.0 {
                optype = constant::AssetChangeDirection::ReduceCapital as i32;
            }
            if assetsyk < 0.0 {
                assetsyk = 0.0 - assetsyk;
            }

            let assetsyk = AssetsData {
                message_id: req.message_id,
                operator_id: req.operator_id,
                business_flag: req.business_flag,
                unit_id: req.unit_id,
                create_time: req.create_time,
                memo: item.deal_no.clone().to_string(),
                assets: PhoenixassetscenterRequestInfo {
                    change_amount: assetsyk,
                    op_type: optype,
                    flag: 0,
                    memo: "买卖成交实现盈亏".to_string(),
                },
                ..Default::default()
            };
            ret_assetsyk = Some(assetsyk);
        }

        let ret = self.phoenix_query_positions_formatdata(&update_position).await;
        if let Err(err) = ret {
            log_error(&format!("phoenix_query_positions_formatdata:{:?}", &err)).await;
            return Err(anyhow!("参数异常"));
        }

        let r_data = (ret.unwrap(), persist_data_vec, ret_assetsyk);
        Ok(r_data)
    }

    pub async fn phoenix_query_positions_formatdata(&self, item: &PhoenixAstStockposition) -> Result<NotificationPosition> {
        let margin_rate = item.margin_rate.to_string().parse::<f64>();
        let total_value = item.total_value.to_string().parse::<f64>();
        let total_value_hkd = item.total_value_hkd.to_string().parse::<f64>();
        let last_price = item.last_price.to_string().parse::<f64>();
        if margin_rate.is_err() || total_value.is_err() || total_value_hkd.is_err() || last_price.is_err() {
            return Err(anyhow!(constdata::DATA_ERROR));
        }
        let mut use_credit = 0;
        if item.sale_amount + item.presale_amount > item.begin_amount {
            use_credit = item.sale_amount + item.presale_amount - item.begin_amount;
        }

        let info = NotificationPosition {
            position_flag: item.position_flag,
            unit_id: item.unit_id,
            position_no: item.position_no,
            stock_code: item.to_owned().stock_code,
            stock_id: item.stock_id,
            exchange_id: item.exchange_id as i64,
            begin_amount: item.begin_amount,
            current_amount: item.current_amount,
            frozen_amount: item.frozen_amount,
            temp_frozen_amount: item.temp_frozen_amount,
            buy_amount: item.buy_amount,
            sale_amount: item.sale_amount,
            prebuy_amount: item.prebuy_amount,
            presale_amount: item.presale_amount,
            buy_in_transit: item.buy_in_transit,
            sale_in_transit: item.sale_in_transit,
            channel_id: 0,
            stock_type: item.stock_type,
            margin_rate: margin_rate.unwrap(),
            total_value: total_value.unwrap(),
            total_value_hkd: total_value_hkd.unwrap(),
            qfii_amount: item.qf_amount,
            timestamp: timeutil::current_timestamp(),
            last_price: last_price.unwrap(),
            use_credit: use_credit,
            user_id: item.user_id,
        };
        Ok(info)
    }

    //查询所有持仓
    pub async fn phoenix_query_positions(&self, db: &DbConnection) -> Result<Vec<Phoenixassetspostioninfo>> {
        let mut ret: Vec<Phoenixassetspostioninfo> = Vec::new();

        let vec_posion_ret = PhoenixAstStockposition::query_many(0, 0, db).await;
        if let Err(err) = vec_posion_ret {
            self.push_log(format!("查询用户持仓失败:{:?}", err)).await;
            return Err(err);
        }
        let vec_posion = vec_posion_ret.unwrap();
        for item in vec_posion {
            let margin_rate = item.margin_rate.to_string().parse::<f64>();
            let total_value = item.total_value.to_string().parse::<f64>();
            let total_value_hkd = item.total_value_hkd.to_string().parse::<f64>();
            let last_price = item.last_price.to_string().parse::<f64>();
            if margin_rate.is_err() || total_value.is_err() || total_value_hkd.is_err() || last_price.is_err() {
                return Err(anyhow!(constdata::DATA_ERROR));
            }

            let mut use_credit = 0;
            if item.sale_amount + item.presale_amount > item.begin_amount {
                use_credit = item.sale_amount + item.presale_amount - item.begin_amount;
            }
            let info = Phoenixassetspostioninfo {
                position_flag: item.position_flag,
                unit_id: item.unit_id,
                position_no: item.position_no,
                stock_code: item.stock_code,
                stock_id: item.stock_id,
                exchange_id: item.exchange_id as i64,
                begin_amount: item.begin_amount,
                current_amount: item.current_amount,
                frozen_amount: item.frozen_amount,
                temp_frozen_amount: item.temp_frozen_amount,
                buy_amount: item.buy_amount,
                sale_amount: item.sale_amount,
                prebuy_amount: item.prebuy_amount,
                presale_amount: item.presale_amount,
                buy_in_transit: item.buy_in_transit,
                sale_in_transit: item.sale_in_transit,
                channel_id: 0,
                stock_type: item.stock_type,
                margin_rate: margin_rate.unwrap(),
                total_value: total_value.unwrap(),
                total_value_hkd: total_value_hkd.unwrap(),
                qfii_amount: item.qf_amount,
                last_price: last_price.unwrap(),
                use_credit: use_credit,
            };
            ret.push(info);
        }
        return Result::Ok(ret);
    }

    //更新持仓数据到db
    pub async fn update_positions_dbdata(&self, data: &Vec<PhoenixAstStockposition>, db: &DbConnection) -> Result<()> {
        for item in data.iter() {
            let ret = PhoenixAstStockposition::save(item, db).await;
            if let Err(err) = ret {
                self.push_log(format!("持仓更新到数据库失败,data:{:?},err:{:?}", item, err)).await;
                return Err(err);
            }
        }
        Ok(())
    }

    pub async fn save_ast_change_param(&self, data: &AssetsData, db: &DbConnection) -> Result<()> {
        let str = serde_json::json!(data).to_string();
        let dbdata = PhoenixAstChangeParam {
            data: str,
            create_time: data.create_time,
            memo: data.memo.clone(),
            status: 0,
            try_count: 0,
            op_type: 1,
            message_id: data.message_id,
            unit_id: data.unit_id,
            err_type: data.err_type,
            id: 0,
        };
        let ret = PhoenixAstChangeParam::insert(&dbdata, db).await;
        if let Err(err) = ret {
            self.push_log(format!("save_ast_change_param失败,data:{:?},err:{:?}", data, err)).await;
            return Err(err);
        }
        Ok(())
    }

    pub async fn save_ast_position_change_param(&self, data: &PositionsData, db: &DbConnection) -> Result<()> {
        let str = serde_json::json!(data).to_string();
        let dbdata = PhoenixAstChangeParam {
            data: str,
            create_time: data.create_time,
            memo: data.memo.clone(),
            status: 0,
            try_count: 0,
            op_type: 2,
            message_id: data.message_id,
            unit_id: data.unit_id,
            err_type: data.err_type,
            id: 0,
        };
        let ret = PhoenixAstChangeParam::insert(&dbdata, db).await;
        if let Err(err) = ret {
            self.push_log(format!("save_ast_change_param失败,data:{:?},err:{:?}", data, err)).await;
            return Err(err);
        }
        Ok(())
    }

    //更新持仓流水数据
    pub async fn save_positions_flow_data(&self, data: (PhoenixOmsAssetflow, PhoenixAstOperationDetail), db: &DbConnection) -> Result<()> {
        let insert_ret = PhoenixOmsAssetflow::insert(&data.0, db).await;
        if let Err(err) = insert_ret {
            return Err(err);
        }
        let mut option_detail = data.1;
        option_detail.ext_id = insert_ret.unwrap();
        let insert_detal = PhoenixAstOperationDetail::insert(&option_detail, db).await;
        if let Err(err) = insert_detal {
            return Err(err);
        }
        Ok(())
    }

    pub async fn update_positions_marginrate(&self, req: &PositionMarginRate, _redis: &RedisClient, db: &DbConnection, _init_date: i32) -> Result<()> {
        //更新数据库保证金比例
        let _ = PhoenixAstStockposition::update_posiion_margin_rate(req.margin_rate, req.stock_id, req.user_id, db).await;
        Ok(())
    }

    //更新数据库最新价
    pub async fn update_positions_price(&self, req: &PositionPriceChangeItemReq, _redis: &RedisClient, db: &DbConnection, _init_date: i32) -> Result<()> {
        //更新数据库价格
        let _ = PhoenixAstStockposition::update_position_price(req.last_price, req.stock_id, db).await;
        Ok(())
    }

    //刷新用户持仓缓存
    pub async fn refresh_unit_position(&self, unit_id: i64, _redis: &RedisClient, db: &DbConnection) -> Result<Vec<NotificationPosition>> {
        let ret = PhoenixAstStockposition::query_many(unit_id, 0, db).await;
        if let Err(err) = ret {
            return Err(err);
        }

        let mut v = Vec::new();
        let ret_vec = ret.unwrap();

        for item in ret_vec {
            let ret = self.phoenix_query_positions_formatdata(&item).await;
            if let Err(err) = ret {
                return Err(err);
            }
            v.push(ret.unwrap());
        }

        Ok(v)
    }
}
