use crate::dataservice::{
    dbsetup::DbConnection,
    entities::prelude::{SysTradeChannel, SysTradeChannelEntity},
};
use anyhow::{anyhow, Result};
use sea_orm::{DbErr, EntityTrait};

impl SysTradeChannel {
    pub async fn find_all(db: &DbConnection) -> Result<Vec<SysTradeChannel>> {
        let ret_data: Result<Vec<SysTradeChannel>, DbErr> = SysTradeChannelEntity::find().all(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }

    pub async fn find_by_id(db: &DbConnection, channel_id: i64) -> Result<SysTradeChannel> {
        let ret_data: Result<Option<SysTradeChannel>, DbErr> = SysTradeChannelEntity::find_by_id(channel_id).one(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        let data = ret_data.unwrap();
        return match data {
            Some(v) => Ok(v),
            None => Err(anyhow!("SysTradeChannel数据不存在")),
        };
    }
}
