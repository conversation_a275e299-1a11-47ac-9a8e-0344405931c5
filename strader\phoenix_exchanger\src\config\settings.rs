// use config::{Config, ConfigError, File};
// // use notify::{DebouncedEvent, RecommendedWatcher, RecursiveMode, Watcher};
// use serde::Deserialize;
// use std::collections::HashMap;
// // use std::sync::mpsc::channel;
// // use std::time::Duration;
// // use std::env;
// // #[derive(Debug, Clone, Deserialize)]
// // pub struct Application {
// //     pub apphost: String,
// //     pub appport: i32,
// // }

// #[derive(Debug, Clone, Deserialize)]
// pub struct Database {
//     pub uri: String,
// }

// // #[derive(Debug, Clone, Deserialize)]
// // pub struct RedisUri {
// //     pub prefix: String,
// //     pub uri: String,
// // }
// #[derive(Debug, Default, <PERSON>lone, Deserialize)]
// pub struct Mq {
//     pub amqpaddr: String,
// }

// #[derive(Debug, Clone, Deserialize)]
// pub struct Notification {
//     pub vhost: String,
//     pub exchanger: String,
// }

// #[derive(Debug, Clone, Deserialize)]
// pub struct Quotation {
//     pub vhost: String,
//     pub exchanger: String,
// }

// #[derive(Debug, Clone, Deserialize)]
// pub struct System {
//     pub channel_id: u64,
//     pub akacenterserver: String,
//     pub node_id: u64,
//     pub machine_id: u64,
//     pub logserver: String,
// }
// #[derive(Debug, Clone, Deserialize)]
// pub struct GrpcClient {
//     //   pub riskcenterclient: String,
//     pub orderrouterclient: String,
//     //  pub assetscenterclient: String,
// }

// #[derive(Debug, Clone, Deserialize)]
// pub struct Settings {
//     //  pub application: Application,
//     pub database: Database,
//     pub system: System,
//     pub notification: Notification,
//     pub quotation: Quotation,
//     pub grpcclient: GrpcClient,
//     pub quotaiontime: HashMap<String, HashMap<String, Vec<String>>>,
//     pub mq: Mq,
// }

// impl Settings {
//     pub fn new() -> Result<Self, ConfigError> {
//         // let config_file = "config/riskcenter";
//         let s = Config::builder()
//             // Start off by merging in the "default" configuration file
//             .add_source(File::with_name("config/exchanger.toml"))
//             .add_source(File::with_name("config/common.toml"))
//             // Add in the current environment file
//             // Default to 'development' env
//             // Note that this file is _optional_
//             //.add_source(File::with_name(&format!("examples/hierarchical-env/config/{}", run_mode)).required(false))
//             // Add in a local configuration file
//             // This file shouldn't be checked in to git
//             //.add_source(File::with_name("examples/hierarchical-env/config/local").required(false))
//             // Add in settings from the environment (with a prefix of APP)
//             // Eg.. `APP_DEBUG=1 ./target/app` would set the `debug` key
//             //.add_source(Environment::with_prefix("app"))
//             // You may also programmatically change settings
//             //.set_override("database.url", "postgres://")?
//             .build()
//             .expect("build configuration error");

//         // You can deserialize (and thus freeze) the entire configuration as
//         s.try_deserialize()
//     }

//     // pub fn watch(&mut self) {
//     //     // Create a channel to receive the events.
//     //     let (tx, rx) = channel();
//     //     // Automatically select the best implementation for your platform.
//     //     // You can also access each implementation directly e.g. INotifyWatcher.
//     //     let mut watcher: RecommendedWatcher = Watcher::new(tx, Duration::from_secs(3)).expect("create configuration watch error");

//     //     // Add a path to be watched. All files and directories at that path and
//     //     // below will be monitored for changes.
//     //     watcher
//     //         .watch("config/riskcenter", RecursiveMode::NonRecursive)
//     //         .expect("watch error");

//     //     // This is a simple loop, but you may want to use more complex logic here,
//     //     // for example to handle I/O.
//     //     loop {
//     //         match rx.recv() {
//     //             Ok(DebouncedEvent::Write(_)) => {
//     //                 println!(" * configuration written; refreshing configuration ...");
//     //                 self.refresh().unwrap();
//     //                 // show();
//     //             }

//     //             Err(e) => println!("watch error: {:?}", e),

//     //             _ => {
//     //                 // Ignore event
//     //             }
//     //         }
//     //     }
//     // }
// }

use common::pconfig::{Database, Mq, Notification, Quotation, Servers};
use config::{Config, ConfigError, File};
use serde::Deserialize;
use std::collections::HashMap;

#[derive(Debug, Clone, Deserialize)]
pub struct System {
    pub channel_id: u64,
    pub channel_doudi_id: u64,
    pub node_id: u64,
    pub machine_id: u64,
    pub loglevel: String,
}

#[derive(Debug, Clone, Deserialize)]
pub struct Settings {
    pub system: System,
    //公共配置
    pub database: Database,
    pub notification: Notification,
    pub quotation: Quotation,
    pub quotaiontime: HashMap<String, HashMap<String, Vec<String>>>,
    pub mq: Mq,
    pub servers: Servers,
}

impl Settings {
    pub fn new() -> Result<Self, ConfigError> {
        let s = Config::builder()
            .add_source(File::with_name("config/exchanger.toml"))
            .add_source(File::with_name("config/common.toml"))
            .build()
            .expect("build configuration error");

        s.try_deserialize()
    }
}
