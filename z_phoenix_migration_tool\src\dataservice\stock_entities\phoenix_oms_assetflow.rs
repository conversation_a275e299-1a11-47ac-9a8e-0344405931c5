//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_oms_assetflow")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub sys_date: i32,
    pub unit_id: i64,
    pub busin_flag: i32,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub occur_capital: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub post_capital: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub currency_rate: Decimal,
    pub stock_code: String,
    pub stock_id: i64,
    pub remarks: String,
    pub currency_no: String,
    pub deal_no: String,
    pub datetime: i64,
    pub op_type: i32,
}

#[derive(Copy, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
