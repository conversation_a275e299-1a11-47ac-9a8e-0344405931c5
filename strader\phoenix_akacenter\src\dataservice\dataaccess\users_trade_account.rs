use crate::dataservice::{
    customer_entities::{
        prelude::{UsersTradeAccount, UsersTradeAccountEntity},
        users_trade_account,
    },
    dbsetup::DbConnection,
};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, DbErr, EntityTrait, QueryFilter};

impl users_trade_account::Model {
    pub async fn find_account_all(db: &DbConnection) -> Result<Vec<UsersTradeAccount>> {
        let ret_data: Result<Vec<UsersTradeAccount>, DbErr> = UsersTradeAccountEntity::find().filter(users_trade_account::Column::AccountCate.eq(2)).all(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }

    pub async fn find_account_by_user_id(db: &DbConnection, user_id: i64) -> Result<UsersTradeAccount> {
        let ret_data: Result<Option<UsersTradeAccount>, DbErr> = UsersTradeAccountEntity::find()
            .filter(users_trade_account::Column::UserId.eq(user_id))
            .filter(users_trade_account::Column::AccountCate.eq(2))
            .one(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        let data = ret_data.unwrap();
        return match data {
            Some(v) => Ok(v),
            None => Err(anyhow!("UsersTradeAccount数据不存在")),
        };
    }
}
