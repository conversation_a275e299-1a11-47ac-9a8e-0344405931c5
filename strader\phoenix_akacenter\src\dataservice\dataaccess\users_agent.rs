use crate::dataservice::{
    entities::{
        prelude::{UsersAgent, UsersAgentEntity},
        users_agent,
    },
};
use dbconnection::DbConnection;
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, DbErr, EntityTrait, QueryFilter};
// use tracing::*;

impl UsersAgent {
    pub async fn _find_by_agent_id(db: &DbConnection, agent_id: i64) -> Result<Option<UsersAgent>> {
        let ret_data: Result<Option<UsersAgent>, DbErr> = UsersAgentEntity::find().filter(users_agent::Column::AgentId.eq(agent_id)).one(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        let data = ret_data.unwrap();
        Ok(data)

        // match UsersAgentEntity::find().filter(users_agent::Column::AgentId.eq(agent_id)).one(db.get_connection()).await {
        //     Ok(data) => {
        //         if data.is_none() {
        //             error!("查询数据为空,agent_id: {}", agent_id);
        //             return Err(anyhow!("查询数据为空,agent_id: {}", agent_id));
        //         }
        //         Ok(data.unwrap())
        //     }
        //     Err(err) => {
        //         error!("查询数据失败,agent_id: {},err: {}", agent_id, err);
        //         return Err(anyhow!(err));
        //     }
        // }
    }
}
