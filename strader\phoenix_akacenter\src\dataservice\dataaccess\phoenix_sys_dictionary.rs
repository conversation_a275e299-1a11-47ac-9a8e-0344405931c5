use crate::dataservice::{
    dbsetup::DbConnection,
    stock_entities::{
        phoenix_sys_dictionary,
        prelude::{PhoenixSysDictionary, PhoenixSysDictionaryEntity},
    },
};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, DbErr, EntityTrait, QueryFilter};

impl phoenix_sys_dictionary::Model {
    #[allow(unused)]
    pub async fn find_all(db: &DbConnection) -> Result<Vec<PhoenixSysDictionary>> {
        let ret_data: Result<Vec<PhoenixSysDictionary>, DbErr> = PhoenixSysDictionaryEntity::find()
            .filter(phoenix_sys_dictionary::Column::LemmaItem.is_not_in(["!", "0", "8"]))
            .all(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }
}
