pub use super::controller::*;

use crate::config::settings::Settings;

use tonic::{self, Request, Response, Status};
use tracing::*;

use crate::client::OrderCenterClient;
use accountriskcenterclient::AccountRiskClient;
use akaclient::akaclient::{AkaCacheOption, AkaClient};
use messagecenter::notificationclient::NotificationClient;
use protoes::phoenixmatchserver::{match_service_server::MatchService, ManualMatchReq, MatchResp};
use protoes::phoenixnotification::{NotificationMessage, NotificationPosition, NotificationType};

use std::collections::HashMap;
use std::pin::Pin;
use std::sync::Arc;
use tokio::sync::{mpsc, oneshot, RwLock};

type StubType = Arc<ServerController>;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

pub struct ServerHandler {
    stub: StubType,
    task_dispacther: mpsc::Sender<ControllerAction>,
    set_close: Option<oneshot::Sender<()>>,
}
#[derive(Debug, Clone)]
pub struct StockTemp {
    pub stock_info: NotificationPosition,
    pub timed: i64,
}

pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);

impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

impl ServerHandler {
    pub async fn new(settings: &Settings) -> Self {
        let mut persist_interval_exec = tokio::time::interval(std::time::Duration::from_secs(1));

        let (tx_notification, mut rx_notification) = tokio::sync::mpsc::channel::<NotificationMessage>(1024);
        //连接消息中心服务
        let queue_name = format!("phoenix_matchserver_notification_{}", utility::timeutil::current_timestamp());

        let notification_client = Arc::new(RwLock::new(
            NotificationClient::new(
                &settings.notification.notification_exchanger,
                &queue_name,
                settings.notification.matchserver_routing_key.to_string(),
                &format!("{}{}", &settings.mq.amqpaddr, &settings.notification.vhost),
                tx_notification,
            )
            .await,
        ));
        messagecenter::init::init_notification_client(notification_client.clone()).await;
        messagecenter::init::init_notification_listen(notification_client).await;

        let op = AkaCacheOption::default();
        let akacenterconn = AkaClient::init(settings.servers.akacenterserver.to_string(), &op).await;
        let account_risk_client = AccountRiskClient::init(settings.servers.accountriskserver.clone()).await;
        let order_client = OrderCenterClient::new(&settings.servers.ordercenterserver).await;
        let account = akacenterconn.query_special_account(protoes::phoenixakacenter::SpecialAccountType::Counterparty as i32).await;
        // if account.as_ref().is_err() {
        //     error!("查找交易对手方信息错误:{:?}", account.as_ref().err().unwrap().to_string());
        //     panic!();
        // }
        let mut internal_channel_id = 16 as i64;
        if let Ok(channels) = akacenterconn.query_all_channel_info().await {
            if let Some(internal_channel) = channels
                .iter()
                .find(|&p| p.channel_attr == common::constant::InternalChannelAttr::Count as i32 && p.channel_type == common::constant::ChannelType::INTERNAL as i32)
            {
                internal_channel_id = internal_channel.channel_id as i64;
            }
        }
        info!(internal_channel_id, "柜台账号通道");
        let account_list = account.as_ref().unwrap().to_owned();
        info!("acount list: {:?}", account_list);
        let stub = ServerController {
            settings: Arc::new(RwLock::new(settings.clone())), // rbcon: rb.to_owned(),
            // uidsvc,
            account_risk_client: account_risk_client,
            akacenterconn,
            order_client,
            account_list: account_list,
            in_channel_id: internal_channel_id,
            position: Arc::new(RwLock::new(HashMap::new())),
        };

        let stub = Arc::new(stub);

        let (tx, mut rx) = mpsc::channel(16);
        let (tx_close, mut rx_close) = oneshot::channel();

        let stub_clone = stub.clone();
        let stub_clone_exec = stub.clone();
        let stub_for_dispatch = stub.clone();

        let svr_handler = ServerHandler {
            task_dispacther: tx,
            set_close: Some(tx_close),
            stub,
        };

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    may_task = rx.recv() => {
                        if let Some(task) = may_task{
                            task(stub_for_dispatch.clone()).await;
                        }
                    }

                    _ = &mut rx_close => {
                        info!("Server scheduler is notified to close");
                        rx.close();
                        break;
                    }
                    notification = rx_notification.recv() => {
                      //包括：订单信息，资金调整消息
                      // 1) 更新用户账户的资产数据和持仓数据
                      // 2) 更新分账户的资产数据和持仓数据
                        if let Some(message) = notification {
                            if let Some(message_body) = message.msg_body.to_owned() {
                                match message.msg_type() {
                                    NotificationType::PositionChanged => {
                                        if let Some(msg_position) = &message_body.msg_position {
                                            info!("账户的持仓变化:{:?}",&msg_position);
                                            let _ = stub_clone.add(&msg_position).await;
                                        }
                                    },
                                    _ =>(),
                                }
                            }else{
                                error!("message body is empty");
                            }
                        } else {
                            error!("empty notification message...");
                        }
                    }
                }
            }

            //drain unhandled task
            while let Some(task) = rx.recv().await {
                task(stub_for_dispatch.clone()).await;
            }

            warn!("Server scheduler has exited");
        });

        tokio::spawn(async move {
            // persist_interval_exec.tick().await; //skip first tick
            loop {
                persist_interval_exec.tick().await;
                let _ = stub_clone_exec.auto_match().await;
            }
        });
        svr_handler
    }

    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

//这里实现grpc的接口
#[tonic::async_trait]
impl MatchService for ServerHandler {
    async fn manual_match(&self, request: Request<ManualMatchReq>) -> Result<Response<MatchResp>, Status> {
        info!("manual_match Client from {:?}", request.remote_addr());
        let req = request.into_inner();
        match self.stub.manual_match(&req).await {
            Ok(val) => Ok(Response::new(val)),
            Err(err) => {
                error!("place order err: {:?}", err);
                Ok(Response::new(MatchResp::default()))
            }
        }
    }
}
