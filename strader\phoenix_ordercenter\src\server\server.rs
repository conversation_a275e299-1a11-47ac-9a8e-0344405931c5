use accountriskcenterclient::AccountRiskClient;
// use accountriskcenterclient::AccountRiskClient;
use anyhow::Result;
// use dashmap::DashMap;
// use tokio::runtime;
use akaclient::akaclient::{AkaCacheOption, AkaClient};
use common::{
    // logclient::*,
    redisclient::redispool::{RedisClient, RedisConfig},
    uidservice::UidgenService,
};
use messagecenter::notificationclient::NotificationClient;
use protoes::phoenixnotification::NotificationMessage;
use std::sync::Arc;
use std::{pin::Pin, time::Duration};
use tokio::sync::{broadcast, mpsc, oneshot, RwLock};
use tonic::Response;
use tracing::{error, info};

use crate::config::settings::Settings;
use crate::odcommon::common::OrderDetail;
use crate::server::service::tool;
// use crate::server::service::CacheKey;
use super::controller::{OrderCenterController, PersistData};
use crate::client::{AssetsCenterClient, DbClient, OrderRouterClient};
use protoes::phoenixordercenter::{order_center_service_server::OrderCenterService, CancelReq, OrderReq, OrderResp, ReplenishOrderReq, ReplenishOrderResp};
use protoes::phoenixordermsg::{ExecMsg, RouterMsg};
use riskcenterclient::RiskCenterClient;
use tracing::*;

type StubType = Arc<OrderCenterController>;
type ControllerAction = Box<dyn (FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>>) + Send>;

pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);
impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}
pub struct OrderServerHandler {
    stub: StubType,
    pub settings: Settings,
    task_dispacther: mpsc::Sender<ControllerAction>,
    set_close: Option<oneshot::Sender<()>>,
}

impl OrderServerHandler {
    pub async fn new(settings: &Settings) -> Result<Self> {
        let (tx, mut rx) = mpsc::channel(32); //多生产 单消费
        let (tx_close, mut rx_close) = oneshot::channel(); //单生产 单消费

        //报单channel: 订单 -> channel send -> 报单
        let (tx_order, mut _rx_order) = broadcast::channel::<RouterMsg>(2048);

        let (tx_persist, mut rx_persist) = mpsc::channel::<PersistData>(1024);
        let (tx_opponent, mut rx_opponent) = mpsc::channel::<OrderDetail>(128);

        //回报channel：回报 -> channel send -> 处理
        // let (tx_repay, mut rx_repay) = broadcast::channel::<RouterMsg>(8192);
        let (tx_confirm, mut rx_confirm) = mpsc::channel::<ExecMsg>(2048);
        let (tx_filled, mut rx_filled) = mpsc::channel::<ExecMsg>(2048);
        let (tx_canceled, mut rx_canceled) = mpsc::channel::<ExecMsg>(512);
        let (tx_rejected, mut rx_rejected) = mpsc::channel::<ExecMsg>(512);

        //订阅mq: 目前本项目中无用
        let (tx_mq, mut _rx_mq) = mpsc::channel::<NotificationMessage>(16);

        let mut rds_cfg = RedisConfig::default();
        rds_cfg.urls = settings.redis.uri.to_owned();
        rds_cfg.prefix = settings.redis.prefix.to_owned();
        rds_cfg.max_size = 16;
        rds_cfg.min_idle = 8;
        let redis_client = RedisClient::new(&rds_cfg).expect("redis connect err");

        let db_client = DbClient::new(&settings.database.stock_uri).await;
        let uidgen = UidgenService::new(settings.application.machineid, settings.application.nodeid);

        //用于发送key: notification.order.status.*,notification.order.exec.*,notification.order.info.*
        let queue_name = format!("phoenix_ordercenter_notification_{}", utility::timeutil::current_timestamp());
        let mut mq_client = NotificationClient::new(
            &settings.notification.notification_exchanger,
            &queue_name,
            settings.notification.ordercenter_routing_key.to_owned(),
            &format!("{}{}", &settings.mq.amqpaddr, &settings.notification.vhost),
            tx_mq,
        )
        .await;
        let _ = mq_client.try_connect().await.expect("mq client connection error");

        // let riskcenter_client = RiskCenterClient::new(&settings.servers.riskcenterserver).await;
        let riskcenter_client = RiskCenterClient::init(settings.servers.riskcenterserver.clone()).await;
        let assetscenter_client = AssetsCenterClient::new(&settings.servers.assetscenterserver).await;
        // let account_risk_client = AccountRiskClient::new(&settings.servers.accountriskserver).await;
        let account_risk_client = AccountRiskClient::init(settings.servers.accountriskserver.clone()).await;

        let mut opt = AkaCacheOption::default();
        opt.use_cache = true;
        opt.mq_uri = format!("{}{}", &settings.mq.amqpaddr, &settings.notification.vhost);
        opt.exchange = settings.notification.notification_exchanger.to_owned();
        opt.routing_keys = settings.notification.ordercenter_routing_key.to_owned();
        let aka_client = AkaClient::init(settings.servers.akacenterserver.clone(), &opt).await;

        let stub = OrderCenterController {
            settings: Arc::new(settings.to_owned()),
            tx_persist,
            tx_order: tx_order.clone(),
            tx_filled: tx_filled.clone(),
            tx_cancel: tx_canceled.clone(),
            tx_opponent,
            // tx_place_order,
            db_client: Arc::new(db_client),
            aka_client: Arc::new(aka_client),
            riskcenter_client, //: Arc::new(RwLock::new(riskcenter_client)),
            assetscenter_client: Arc::new(RwLock::new(assetscenter_client)),
            // account_risk_client: Arc::new(RwLock::new(account_risk_client)),
            account_risk_client,
            redis_client: Arc::new(redis_client),
            mq_client: Arc::new(mq_client),
            uidgen: Arc::new(RwLock::new(uidgen)),
        };

        let stub = Arc::new(stub);
        let stub_clone = stub.clone();
        let stub_for_dispatch = stub.clone();

        // let stub_place_order = stub.clone();
        let stub_clone_persist = stub.clone();
        let stub_confirm = stub.clone();
        let stub_filled = stub.clone();
        let stub_canceled = stub.clone();
        let stub_rejected = stub.clone();

        let ret = OrderServerHandler {
            stub,
            settings: settings.to_owned(),
            task_dispacther: tx,
            set_close: Some(tx_close),
        };

        // 以下用来测试订阅mq消息
        // let mut interval = tokio::time::interval(Duration::from_secs(3));
        // tokio::spawn(async move {
        // 	loop {
        // 		tokio::select! {
        // 			_ = interval.tick() => {
        // 				let _ = stub_copy.mq_client.try_consume().await;
        // 			}
        // 		}
        // 	}
        // });

        let mut order_router_client = OrderRouterClient::new(
            &settings.servers.orderrouterserver,
            tx_order.clone(),
            tx_confirm.clone(),
            tx_filled.clone(),
            tx_canceled.clone(),
            tx_rejected.clone(),
        )
        .await;
        let mut retry_interval = tokio::time::interval(Duration::from_secs(3));
        tokio::spawn(async move {
            retry_interval.tick().await;
            loop {
                tokio::select! {
                    _ = retry_interval.tick() => {
                        if let Err(err) = order_router_client.order_routing().await {
                            error!("{:?}", err);
                            // let _ = order_router_client.retry_order_routing().await;
                        }
                    }
                }
            }
        });

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    confirm_task = rx_confirm.recv() => {//确认
                        if let Some(confirm) = confirm_task {
                            let _ = tool::order_lock(confirm.order_id, &stub_confirm.redis_client).await;
                            if let Err(err) = stub_confirm.confirm_order_receipt(&confirm).await {
                                error!("confirm error: {:#?}", err);
                            }
                            let _ = tool::order_un_lock(confirm.order_id, &stub_confirm.redis_client).await;
                        }
                    }
                }
            }
        });

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    filled_task = rx_filled.recv() => {//成交
                        if let Some(filled) = filled_task {
                            // let stub_filled = stub_filled.clone();
                            // tokio::spawn(async move {
                            let _ = tool::order_lock(filled.order_id, &stub_filled.redis_client).await;
                            if let Err(err) = stub_filled.filled_order_receipt(&filled).await {
                                error!("filled error: {:#?}", err);
                            }
                            let _ = tool::order_un_lock(filled.order_id, &stub_filled.redis_client).await;
                            // });
                        }
                    }
                }
            }
        });

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    canceled_task = rx_canceled.recv() => {//撤单/废单
                        if let Some(canceled) = canceled_task {
                            let _ = tool::order_lock(canceled.order_id, &stub_canceled.redis_client).await;
                            if let Err(err) = stub_canceled.cancel_order_receipt(&canceled).await {
                                error!("canceled error: {:#?}", err);
                            }
                            let _ = tool::order_un_lock(canceled.order_id, &stub_canceled.redis_client).await;
                        }
                    }
                }
            }
        });

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    rejected_task = rx_rejected.recv() => {//Rejected
                        if let Some(rejected) = rejected_task {
                            let _ = tool::order_lock(rejected.order_id, &stub_rejected.redis_client).await;
                            if let Err(err) = stub_rejected.rejected_order_receipt(&rejected).await {
                                error!("rejected error: {:#?}", err);
                            }
                            let _ = tool::order_un_lock(rejected.order_id, &stub_rejected.redis_client).await;
                        }
                    }
                }
            }
        });

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    persist_task = rx_persist.recv() => {//持久化channel
                        if let Some(persist_data) = persist_task {
                            // info!("Starting persist task");
                            if let Err(err) = stub_clone_persist.persist_data(&persist_data).await {
                                error!("persist data error: {:#?}", err);
                            }
                        }
                    }
                }
            }
        });

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    may_task = rx.recv() => {
                        // info!("task from client received...");
                        let task = may_task.expect("Server scheduler has unexpected exit");
                        task(stub_for_dispatch.clone()).await;
                    }
                    // _ = persist_interval.tick() => {//定时任务
                    // }
                    // mq_task = rx_mq.recv() => {//测试mq消息接收
                    // 	if let Some(msg) = mq_task {
                    // 		info!("==================={:#?}", msg);
                    // 	}
                    // }
                    opponent_task = rx_opponent.recv() => {
                        if let Some(mut data) = opponent_task {
                            info!("{:?}", data);
                            if let Err(err) = stub_clone.generate_opponent_order(&mut data).await {
                                error!("generate_opponent_order error: {:#?}", err);
                            }
                        }
                    }
                    _ = &mut rx_close => {
                        info!("Server scheduler is notified to close");
                        rx.close();
                        break;
                    }
                }
            }

            while let Some(task) = rx.recv().await {
                task(stub_for_dispatch.clone()).await;
                info!("drain unhandled task received");
            }

            warn!("Server scheduler has exited");
        });

        Ok(ret)
        // Err(anyhow!("启动失败"))
    }

    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

#[tonic::async_trait]
impl OrderCenterService for OrderServerHandler {
    async fn place_order(&self, request: tonic::Request<OrderReq>) -> Result<tonic::Response<OrderResp>, tonic::Status> {
        info!("place_order Client from {:?}", request.remote_addr());

        let req = request.into_inner();
        match self.stub.place_order(&req).await {
            Ok(val) => Ok(Response::new(val)),
            Err(err) => {
                error!("place order err: {:?}", err);
                Ok(Response::new(OrderResp::default()))
            }
        }
    }

    async fn cancel_order(&self, request: tonic::Request<CancelReq>) -> Result<tonic::Response<OrderResp>, tonic::Status> {
        info!("cancel_order Client from {:?}", request.remote_addr());

        let req = request.into_inner();
        match self.stub.cancel_order(&req).await {
            Ok(val) => Ok(Response::new(val)),
            Err(err) => {
                error!("cancel order err: {:?}", err);
                Ok(Response::new(OrderResp::default()))
            }
        }
    }

    async fn replenishment_order(&self, request: tonic::Request<ReplenishOrderReq>) -> Result<tonic::Response<ReplenishOrderResp>, tonic::Status> {
        info!("replenishment_order Client from {:?}", request.remote_addr());
        // request.set_timeout(Duration::from_secs(30));
        let req = request.into_inner();
        let mut resp = ReplenishOrderResp::default();
        match self.stub.replenishment_order(&req).await {
            Ok(_) => {
                resp.err_code = 0;
                resp.err_msg = format!("补单成功");
                Ok(Response::new(resp))
            }
            Err(err) => {
                error!("replenishment_order order err: {:?}", err);
                resp.err_code = 1;
                resp.err_msg = format!("{}", err);
                Ok(Response::new(resp))
            }
        }
    }
}
