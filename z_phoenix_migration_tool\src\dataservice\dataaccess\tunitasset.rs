use crate::dataservice::{
    dbsetup::DbConnection,
    old_stock_entities::{prelude::*, tunitasset},
};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, EntityTrait, QueryFilter};
// use serde_json::json;

impl tunitasset::Model {
    pub async fn query_many(unit_id: i64, db: &DbConnection) -> Result<Vec<Tunitasset>> {
        let ret = match unit_id {
            0 => TunitassetEntity::find().all(db.get_connection()).await,
            _ => TunitassetEntity::find().filter(tunitasset::Column::LUnitId.eq(unit_id)).all(db.get_connection()).await,
        };
        if ret.as_ref().is_err() {
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let d = ret.unwrap();
        return Ok(d);
    }
}
