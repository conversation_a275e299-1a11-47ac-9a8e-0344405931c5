use crate::dataservice::{
    entities::{
        prelude::{UsersChinextRate, UsersChinextRateEntity},
        users_chinext_rate,
    },
};
use dbconnection::DbConnection;
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, DbErr, EntityTrait, QueryFilter};

impl UsersChinextRate {
    pub async fn find_by_user_id(db: &DbConnection, user_id: i64) -> Result<Option<UsersChinextRate>> {
        let ret_data: Result<Option<UsersChinextRate>, DbErr> = UsersChinextRateEntity::find().filter(users_chinext_rate::Column::UserId.eq(user_id)).one(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        let data = ret_data.unwrap();
        Ok(data)
    }

    pub async fn find_all(db: &DbConnection) -> Result<Vec<UsersChinextRate>> {
        let ret_data: Result<Vec<UsersChinextRate>, DbErr> = UsersChinextRateEntity::find().all(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }
}
