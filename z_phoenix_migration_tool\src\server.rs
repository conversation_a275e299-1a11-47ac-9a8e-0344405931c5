pub use super::controller::*;

use crate::config::settings::Settings;
use common::uidservice::UidgenService;

use tonic::{self, Request, Response, Status};
// use utility::constant::MessageType;
// use crate::protofiles::phoenixriskcenter::phoenix_riskcenter_server::PhoenixRiskcenter;
use crate::protofiles::phoenixmigrationtool::{phoenix_migration_tool_server::PhoenixMigrationTool, MigrationDataReq, MigrationDataResp};
// use futures::{
//     channel::mpsc::{channel, Receiver},
//     SinkExt, StreamExt,
// };
// use std::fmt::Debug;
use std::pin::Pin;
use std::sync::Arc;

use crate::dataservice::dbsetup::DbConnection;
use common::redisclient::redispool::{RedisClient, RedisConfig};
use tokio::sync::{mpsc, oneshot, RwLock};

type StubType = Arc<ServerController>;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

pub struct ServerHandler {
    stub: StubType,
    task_dispacther: mpsc::Sender<ControllerAction>,
    // order_dispacther: mpsc::Sender<PhoenixRiskCheckInfo>,
    set_close: Option<oneshot::Sender<()>>,
    // mqclient: QuotationClient,
}
pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);

impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

impl ServerHandler {
    pub async fn new(settings: &Settings) -> Self {
        // let (tx_notification, mut rx_notification) = tokio::sync::mpsc::channel::<NotificationMessage>(128);

        // let notification_client = Arc::new(RwLock::new(NotificationClient::new(
        //   settings.notification.exchanger.as_str(),
        //   settings.notification.queue_name.as_str(),
        //   settings.notification.router_key.clone(),
        //   &settings.notification.amqpaddr.as_str(),
        //   tx_notification,
        // )
        //     .await));
        //
        // messagecenter::init::init_notification_client(notification_client.clone()).await;
        // messagecenter::init::init_notification_listen(notification_client).await;

        let uidsvc = Arc::new(RwLock::new(UidgenService::new(settings.application.machineid, settings.application.nodeid)));
        let src_dbconn = Arc::new(DbConnection::new(&settings.database.src_uri.as_str()).await);
        let dst_dbconn = Arc::new(DbConnection::new(&settings.database.dst_uri.as_str()).await);
        let customer_dbconn = Arc::new(DbConnection::new(&settings.database.customer_uri.as_str()).await);
        let mut rdscfg = RedisConfig::default();
        rdscfg.urls = settings.redis.uri.to_owned();
        rdscfg.prefix = settings.redis.prefix.to_owned();
        rdscfg.max_size = 12;
        let redis_client = RedisClient::new(&rdscfg).unwrap();

        let stub = ServerController {
            settings: Arc::new(RwLock::new(settings.clone())), // rbcon: rb.to_owned(),
            src_dbconn,
            dst_dbconn,
            customer_dbconn,
            uidsvc,
            redis: Arc::new(redis_client),
        };

        let stub = Arc::new(stub);

        let (tx, mut rx) = mpsc::channel(16);
        let (tx_close, mut rx_close) = oneshot::channel();

        // let stub_clone = stub.clone();
        let stub_for_dispatch = stub.clone();

        let svr_handler = ServerHandler {
            task_dispacther: tx,
            set_close: Some(tx_close),
            stub,
        };

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    may_task = rx.recv() => {
                        if let Some(task) = may_task{
                            task(stub_for_dispatch.clone()).await;
                        }
                    }
                    // notification = rx_notification.recv() => {
                    //     //收到消息通知
                    //     if let Some(message) = &notification{
                    //         //receive message
                    //         log::info!("receive message from message center: {:?}", &message);
                    //     }
                    // }
                    _ = &mut rx_close => {
                        log::info!("Server scheduler is notified to close");
                        rx.close();
                        break;
                    }
                }
            }

            //drain unhandled task
            while let Some(task) = rx.recv().await {
                task(stub_for_dispatch.clone()).await;
            }

            log::warn!("Server scheduler has exited");
        });

        svr_handler
    }
    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

//这里实现grpc的接口
#[tonic::async_trait]
impl PhoenixMigrationTool for ServerHandler {
    async fn migration_data(&self, request: Request<MigrationDataReq>) -> Result<Response<MigrationDataResp>, Status> {
        //注意：以下代码仅作示例
        let ret = self.stub.migration_data(&request.into_inner()).await;
        match ret {
            Ok(ret) => Ok(Response::new(ret)),
            Err(e) => Err(Status::unknown(format!("{}", e))),
        }
    }
}
