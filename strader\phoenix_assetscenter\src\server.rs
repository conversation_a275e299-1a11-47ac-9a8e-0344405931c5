pub use super::controller::*;

use protoes::assetscenter::{phoenixassetscenter_server::Phoenixassetscenter, PhoenixassetscenterQueryRequest, PhoenixassetscenterRequest, PhoenixassetscenterResponse};
use protoes::assetscenter::{PositionMarginRateReq, PositionMarginRateResp, PositionPriceChangeReq, PositionPriceChangeResp, RefreshUnitDataReq, RefreshUnitDataResp};

use crate::service::assetsmodel::{AssetsData, PositionsData};
use crate::service::orddealsvc::Orddealsvc;
use crate::{
    config::settings::Settings,
    dataservice::dbsetup,
    service::{assetssvc::UnitAssetsService, stockpositionsvc::UnitPositionService},
};
use akaclient::akaclient::{AkaCacheOption, AkaClient};
use common::constant;
use common::uidservice::UidgenService;
use tokio::time::Instant;
use utility::timeutil;
// use messagecenter::assetsclient::AssetsChangeClient;
use crate::dataservice::entities::prelude::PhoenixSysSystem;
use common::redisclient::redispool::{RedisClient, RedisConfig};
use messagecenter::notificationclient::NotificationClient;
use protoes::phoenixnotification::{NotificationMessage, NotificationSettlement, NotificationType};

use std::pin::Pin;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::{mpsc, oneshot, RwLock};
use tonic::{self, Request, Response, Status};
use tracing::*;

type StubType = Arc<ServerController>;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

pub struct ServerHandler {
    stub: StubType,
    task_dispacther: mpsc::Sender<ControllerAction>,
    // order_dispacther: mpsc::Sender<PhoenixRiskCheckInfo>,
    set_close: Option<oneshot::Sender<()>>,
    // mqclient: QuotationClient,
}

pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);

impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

impl ServerHandler {
    pub async fn new(settings: &Settings) -> Self {
        let mut persist_interval = tokio::time::interval(std::time::Duration::from_secs(2 as u64));

        let starttime = Instant::now() + Duration::from_secs(timeutil::get_first_interval_time(&String::from("23:59:59")));
        let mut refresh_time = tokio::time::interval_at(starttime, std::time::Duration::from_secs(24 * 60 * 60));

        let (tx, mut rx) = mpsc::channel(16);
        let (tx_close, mut rx_close) = oneshot::channel();

        let (tx_notification, mut rx_notification) = tokio::sync::mpsc::channel::<NotificationMessage>(1024);

        let dbconn = dbsetup::DbConnection::new(&settings.database.stock_uri).await;
        let mut rdscfg = RedisConfig::default();
        rdscfg.urls = settings.redis.uri.to_owned();
        rdscfg.prefix = settings.redis.prefix.to_owned();
        rdscfg.max_size = 6;
        let redisclient: RedisClient = RedisClient::new(&rdscfg).unwrap();
        let uidsvc = UidgenService::new(settings.application.machineid, settings.application.nodeid);

        //连接消息中心服务
        let queue_name = format!("phoenix_assetscenter_notification_{}", utility::timeutil::current_timestamp());
        let notification_client = NotificationClient::new(
            &settings.notification.notification_exchanger,
            &queue_name,
            settings.notification.assetscenter_routing_key.clone(),
            &format!("{}{}", &settings.mq.amqpaddr, &settings.notification.vhost),
            tx_notification,
        )
        .await;
        messagecenter::init::init_notification_client(Arc::new(RwLock::new(notification_client.clone()))).await;
        messagecenter::init::init_notification_listen(Arc::new(RwLock::new(notification_client.clone()))).await;
        let msgclient = Arc::new(notification_client);

        // old dode
        // let (tx_message, _rx_message) = mpsc::channel::<NotificationMessage>(1024);
        // let queue_name = format!("phoenix_assetscenter_queue_data_{}", utility::timeutil::current_timestamp());
        // let msgclient = NotificationClient::new(
        //     &settings.msgcenter.assets_exchange,
        //     &queue_name,
        //     settings.msgcenter.routing_key.clone(),
        //     &format!("{}{}", &settings.mq.amqpaddr, &settings.msgcenter.vhost),
        //     tx_message,
        // )
        // .await;
        // let msgclient = Arc::new(msgclient);

        // //连接消息中心服务
        // let queue_name = "phoenix_assetscenter_queue_settle";
        // let notification_client = NotificationClient::new(
        //     &settings.msgcenter.assets_exchange,
        //     queue_name,
        //     settings.msgcenter.settlement_key.clone(),
        //     &format!("{}{}", &settings.mq.amqpaddr, &settings.msgcenter.vhost),
        //     tx_notification,
        // )
        // .await;
        // messagecenter::init::init_notification_client(Arc::new(RwLock::new(notification_client.clone()))).await;
        // messagecenter::init::init_notification_listen(Arc::new(RwLock::new(notification_client))).await;

        let opt = AkaCacheOption {
            use_cache: false,
            mq_uri: format!("{}{}", &settings.mq.amqpaddr, &settings.notification.vhost),
            exchange: settings.notification.notification_exchanger.to_owned(),
            routing_keys: settings.notification.assetscenter_routing_key.to_owned(),
        };
        let akaclient = AkaClient::init(settings.servers.akacenterserver.clone(), &opt).await;
        let sysinfo = PhoenixSysSystem::find(&dbconn).await.expect("find error").unwrap();
        let ordsvc = Orddealsvc::new();
        let (tx_assets, mut rx_assets) = mpsc::channel::<AssetsData>(1024);
        let (tx_positions, mut rx_positions) = mpsc::channel::<PositionsData>(1024);

        let stub = ServerController {
            redis: Arc::new(redisclient),
            dbconn: Arc::new(dbconn),
            assetssvc: Arc::new(UnitAssetsService::new()),
            positionsvc: Arc::new(UnitPositionService::new()),
            uidgen: Arc::new(RwLock::new(uidsvc)),
            akasvc: Arc::new(akaclient),
            config: Arc::new(RwLock::new(settings.to_owned())),
            sysinfo: Arc::new(RwLock::new(sysinfo)),
            notify: msgclient,
            tx_assets: tx_assets,
            tx_positions: tx_positions,
            settlestate: Arc::new(RwLock::new(0)),
            ordsvc: Arc::new(ordsvc),
        };
        //用户资产数据初始化
        stub.init().await;

        let stub = Arc::new(stub);
        let stub_clone = stub.clone();
        let stub_for_dispatch = stub.clone();
        let stub_clone_assets = stub.clone();
        let svr_handler = ServerHandler {
            task_dispacther: tx,
            set_close: Some(tx_close),
            stub,
        };

        //定时处理资产请求
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    assets = rx_assets.recv() => {
                        info!("receive rx_assets data:{:?}",&assets);
                        if let Some(persist_data)=assets{
                        let _ = stub_clone_assets.assets_change(persist_data).await;
                        }
                    }
                    assets = rx_positions.recv() => {
                        info!("receive rx_positions data:{:?}",&assets);
                        if let Some(persist_data)=assets{
                        let _ = stub_clone_assets.positions_change(persist_data).await;
                        }
                    }
                }
            }
        });

        tokio::spawn(async move {
            persist_interval.tick().await;
            loop {
                tokio::select! {
                    may_task = rx.recv() => {
                        if let Some(task) = may_task{
                            task(stub_for_dispatch.clone()).await;
                        }
                    }
                    _ = persist_interval.tick() => {
                        //info!("persist_interval.tick()");
                        let _ = stub_clone.handle_persist_dbdata(false).await;
                        let _ = stub_clone.update_ord_stock_deal_interval().await;
                    }

                    _=refresh_time.tick()=>{
                        info!("重新刷新sys_date");
                        let msg = NotificationSettlement{
                            settle_status:2,
                            exchange_type:0
                        };
                        stub_clone.set_setttlestate(&msg).await;
                    }

                    notification = rx_notification.recv() => {

                        info!("notification received: {:?}", notification);
                        if let Some(message) = notification {
                            if let Some(message_body) = message.msg_body.to_owned() {
                                match message.msg_type() {
                                    NotificationType::Settlement=>{
                                        if let Some(msg) = &message_body.msg_settlement{
                                            stub_clone.set_setttlestate(msg).await;
                                        }
                                    }
                                    NotificationType::DvdSettle => {
                                        if let Some(data) = &message_body.msg_dvdsettle {
                                            info!("收到权益分派消息: {:?}", data);
                                            if data.status ==2 {
                                                info!("权益分派结束，重新初始化缓存数据");
                                                if let Err(e) = stub_clone.refresh_data().await {
                                                error!("重新初始化缓存数据失败：{}", e);
                                                }
                                            }
                                        }
                                    }
                                    _=>{

                                    }
                                }
                            }
                        }
                    }
                    _ = &mut rx_close => {
                        info!("Server scheduler is notified to close");
                        rx.close();
                        break;
                    }
                }
            }
            //drain unhandled task
            while let Some(task) = rx.recv().await {
                task(stub_for_dispatch.clone()).await;
            }

            warn!("Server scheduler has exited");
        });

        svr_handler
    }
    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

//这里实现grpc的接口
#[tonic::async_trait]
impl Phoenixassetscenter for ServerHandler {
    async fn phoenix_assets_change(&self, request: Request<PhoenixassetscenterRequest>) -> Result<Response<PhoenixassetscenterResponse>, Status> {
        // let stub = self.stub.read().await;
        // Ok(Response::new(stub.phoenix_place_order(request.into_inner())?))
        let req = request.into_inner();
        let ret = self.stub.phoenix_assets_change(&req).await;

        match ret {
            Ok(v) => Ok(Response::new(v)),
            Err(e) => {
                let res = PhoenixassetscenterResponse {
                    ret_code: constant::ResultCode::ERROR as i32,
                    ret_msg: e.to_string(),
                    assetsinfo: vec![],
                };
                Ok(Response::new(res))
            }
        }
    }

    async fn phoenix_assets_query(&self, request: Request<PhoenixassetscenterQueryRequest>) -> Result<Response<PhoenixassetscenterResponse>, Status> {
        let req = request.into_inner();
        let ret = self.stub.phoenix_assets_query(&req).await;

        match ret {
            Ok(v) => Ok(Response::new(v)),
            Err(e) => {
                let res = PhoenixassetscenterResponse {
                    ret_code: constant::ResultCode::ERROR as i32,
                    ret_msg: e.to_string(),
                    assetsinfo: vec![],
                };
                Ok(Response::new(res))
            }
        }
    }

    async fn phoenix_positions_marginrate_change(&self, request: Request<PositionMarginRateReq>) -> Result<Response<PositionMarginRateResp>, Status> {
        let req = request.into_inner();
        let ret = self.stub.phoenix_positions_marginrate_change(&req).await;
        match ret {
            Ok(v) => Ok(Response::new(v)),
            Err(e) => {
                let res = PositionMarginRateResp {
                    ret_code: constant::ResultCode::ERROR as i32,
                    ret_msg: e.to_string(),
                };
                Ok(Response::new(res))
            }
        }
    }

    async fn phoenix_positions_price_change(&self, request: Request<PositionPriceChangeReq>) -> Result<Response<PositionPriceChangeResp>, Status> {
        let req = request.into_inner();
        let ret = self.stub.phoenix_positions_lastprice_change(&req).await;
        match ret {
            Ok(v) => Ok(Response::new(v)),
            Err(e) => {
                let res = PositionPriceChangeResp {
                    ret_code: constant::ResultCode::ERROR as i32,
                    ret_msg: e.to_string(),
                };
                Ok(Response::new(res))
            }
        }
    }

    async fn phoenix_refresh_unit_data(&self, request: tonic::Request<RefreshUnitDataReq>) -> Result<Response<RefreshUnitDataResp>, Status> {
        let req = request.into_inner();
        let ret = self.stub.refresh_unit_data(&req).await;
        match ret {
            Ok(v) => Ok(Response::new(v)),
            Err(e) => {
                let res = RefreshUnitDataResp {
                    ret_code: constant::ResultCode::ERROR as i32,
                    ret_msg: e.to_string(),
                };
                Ok(Response::new(res))
            }
        }
    }
}
