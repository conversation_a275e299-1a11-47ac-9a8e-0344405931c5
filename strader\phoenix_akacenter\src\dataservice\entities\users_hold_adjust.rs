//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_hold_adjust")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub business_type: i8,
    pub user_id: i64,
    pub types: i8,
    pub commodity_id: i64,
    pub status: i8,
    pub create_date: i64,
    pub check_date: i64,
    pub send_date: i32,
    pub create_name: String,
    pub check_name: String,
    pub mark: String,
    pub num: i32,
    pub enable_num: i32,
    pub tid: i64,
    pub change_cost_state: i8,
    pub channel_id: i64,
    pub average_price: String,
    pub is_zt: i8,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
