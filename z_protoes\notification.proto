syntax = "proto3";

package phoenixnotification;

// 通知类别
enum NotificationType {
  UndefNotificationType = 0;
  TimeChanged = 1;        // 交易时间调整
  OrderStatusChanged = 2; // 订单状态变化
  OrderExecMsg = 3;       // 订单执行回报消息
  AssetChanged = 4;       // 资产变化（变化后的结果数据）
  AssetAdjusted = 5; // 资产调整（调整的内容（不是结果数据））
  PositionChanged = 6;    // 持仓变化
  PositionAdjusted = 7;   // 持仓变化（调整内容）- 类型暂时保留
  Settlement = 8;         // 结算
  ChannelInfoChanged = 9; // 通道基础信息发生变化
  StockInfoChanged = 10;
  ExchangeRateChanged = 11;
  AccountInfoChanged = 12;
  UserStockMarginChanged = 13;
  FeeSettingChanged = 14;
  StockSuspensionChanged = 15;
  UserClientNofity = 16; // 通知客户端保证金比例发生变化
  UserOrderInfo = 17;    // 客户订单详情
  NoticeNotify = 18;     // 公告通知
  RqNotify = 19;         // 融券额度发生变更
  DvdSettle = 20;        // 权益分派
  PositionChannelInitNotify=21;  //分帐户持仓需要重新初始化
}

enum OrderExecType {
  UndefExecType = 0;
  NewOrder = 1;       // 新订单
  OrderFill = 2;      // 订单成交
  OrderCancelled = 3; // 撤单
}

message NotificationMessage {
  NotificationType msg_type = 1; // 1：时间类消息
  MessageBody msg_body = 2;      // 消息内容
}

message NotificationMessageData{
  string content=1;//推送内容
}


enum OperationType { // 定义枚举类型，
  INSERT = 0;
  EDIT = 1;
  DELETE = 2;
}

message MessageBody {
  NotificationDatetime msg_datetime = 1;           // 这些都是optional的
  NotificationOrderStatus msg_orderstatus = 2;     // 订单状态变化
  NotificationOrderExec msg_orderexec = 3;         // 订单执行回报消息
  NotificationAsset msg_asset = 4;                 // 资产变化
  NotificationPosition msg_position = 5;           // 持仓变化
  NotificationAssetAdjusted msg_assetadjusted = 6; // 资产调整
  NotificationSettlement msg_settlement = 7;       // 结算通知
  ChannelInfoChange msg_channelinfo = 8;    // 通道基础信息发生变化
  StockInfoChange msg_stockinfo = 9;        // 股票信息修改
  ExchangeRateChange msg_exchangerate = 10; // 汇率变化
  AccountInfoChange msg_accountinfo = 11;   // 用户信息发生变化
  UserStockMarginChange msg_userstockmargin = 12; // 用户品种保证金变化
  FeeSettingChange msg_feesetting = 13;           // 费用变化
  StockSuspensionChange msg_stocksuspension = 14; // 停牌信息
  UserClientNotifyDataChange msg_userclientnotify = 15; // 用户客户端推送
  NotificationOrderInfo msg_orderinfo = 16;             // 订单详情
  NoticeDataChange msg_notice = 17;                     //
  RqNotifyChange msg_rq = 18;               // 融券额度变化消息
  NotificationDvdSettle msg_dvdsettle = 19; // 权益分派通知
  NotificationMessageData msg_postionchannel_init=20; //分帐户持仓需要重新初始化
}

message NotificationDatetime {
  int32 datetime_type = 1; // 1:休市 ,2:交易时间
  int64 market_id = 2;     // 市场id
  int64 stock_id = 3;      // 品种id 品种为0推送的是市场的
  string msg_time = 4; // 时间，时间格式为 2020-12-01 09:30:00|2020-12-01
                       // 15:30:00,2020-12-02 09:30:00|2020-12-02 15:30:00
  int32 trade_type = 5; // 交易时间时，1：开仓，2：平仓
}

// 订单状态变化
message NotificationOrderStatus {
  int64 unit_id = 1;        // 用户id
  int64 order_id = 2;       // 订单ID
  int32 order_quantity = 3; // 发生数量
  double order_price = 4;
  string order_action = 5; // 1:新订单  2:成交  3:撤单
  int32 order_status = 6;  // 当前状态
  int64 deal_no = 7;       // 成交编号
  string memo = 8;         // 备注
  int64 user_id = 9;
}

// 订单成交撤单回报
message NotificationOrderExec {
  int64 unit_id = 1;           // 用户id
  int64 order_id = 2;          // 订单ID
  int64 stock_id = 3;          // 股票id
  int32 channel_id = 4;        // 通道id
  int32 channel_type = 5;      // 通道类别
  int32 order_quantity = 6;    // 发生数量
  double order_price = 7;      // 成交价格
  int32 order_direction = 8;   // 订单方向
  OrderExecType exec_type = 9; // 1:新订单  2:成交  3:撤单
  int64 deal_no = 10;          // 成交编号
  string memo = 11;            // 备注
  int64 msg_id = 12;           // 消息id
  int64 algorithm_id = 13;     // 算法单id(普通0)
  int64 user_id = 14;
}

// 订单详情    topic key：  order.info.unit_id
message NotificationOrderInfo {
  int64 unit_id = 1;          // 用户id
  int32 order_direction = 2;  // 委托方向
  string stock_code = 3;      // 证券代码
  double order_price = 4;     // 委托价格
  int64 order_no = 5;         // 委托编号
  int32 order_type = 6;       // 委托类别
  double deal_price = 7;      // 成交均价
  int32 order_amount = 8;     // 委托数量
  int32 deal_amount = 9;      // 成交数量
  int32 canceled_amount = 10; // 已撤数量
  string create_time = 11;    // 委托时间 2023-02-01 14:35:25
  string last_deal_time = 12; // 最后成交时间 2023-02-01 14:35:25
  int32 order_status = 13;    // 订单状态
  int64 stock_id = 14;        // 证券id
  string order_memo = 15;     // 废单原因
  int64 user_id = 16;
  int64 deal_no = 17; // 成交编号
}

message NotificationAssetPositions {
  NotificationAsset assets = 1;
  NotificationPosition positions = 2;
}

// 资产变化
message NotificationAsset {
  int64 unit_id = 1;               // 账户id
  double current_cash = 2;         // 当前本金
  double frozen_capital = 3;       // 冻结资金
  double trade_frozen_capital = 4; // 交易临时冻结
  double begin_cash = 5;           // 期初本金
  double cash_in_transit = 6;      // 在途资金
  string currency_no = 7;          // 币种
  double credit_multiple = 8;      // 信用倍数
  int64 timestamp = 9;
  double today_deposit = 10;       // 今日入金
  double today_withdraw = 11;      // 今日出金
  double total_deposit = 12;       // 总入金
  double total_withdraw = 13;      // 总出金
  double today_total_value = 14;   // 昨日本金
  double gem_frozen_capital = 15;  // 创业板挂单保证金占用
  int64 user_id = 16;              // 用户id
  double transfer_cash = 17;       // 今日净入资金
  double total_transfer_cash = 18; // 总净划入资金
}

// 持仓变化
message NotificationPosition {
  int64 position_no = 1;
  int64 unit_id = 2;             // 账户id
  string stock_code = 3;         // 证券代码
  int64 stock_id = 4;            // 证券id
  int64 exchange_id = 5;         // 市场ID
  int32 position_flag = 6;       // 1多 2空
  int32 begin_amount = 7;        // 期初数量
  int32 current_amount = 8;      // 当前数量
  int32 frozen_amount = 9;       // 冻结数量
  int32 temp_frozen_amount = 10; // 临时冻结数量
  int32 buy_amount = 11;         // 今买数量
  int32 sale_amount = 12;        // 今卖数量
  int32 prebuy_amount = 13;      // 预买数量
  int32 presale_amount = 14;     // 预卖数量
  int32 buy_in_transit = 15;     // 在途持仓数量(买)
  int32 sale_in_transit = 16;    // 在途持仓数量(卖)
  int64 channel_id = 17;         // 通道id
  int32 stock_type = 18;         // 股票类别
  double margin_rate = 19;       // 保证金比例
  double total_value = 20;       // 开仓成本;
  double total_value_hkd = 21;   // 港币开仓成本
  int32 qfii_amount = 22;        // qf持仓数量
  int64 timestamp = 23;
  double last_price = 24; // 最新价
  int32 use_credit = 25;  // 已用融券额度
  int64 user_id = 26;     // 用户id
}

message NotificationAssetAdjusted {
  int64 unit_id = 1; // 账户id
  int32 business_flag =
      2; // 资金业务大类：1：用户出入金调整 2：运营手动调整资产
         // 3：交易产生的资产变动 4：交收资产变动 5：权益分派资产变动
         // 6：系统资产调整（结息）
  int32 op_type =
      3; // 操作类型，101：本金增加，102：本金减少，103：本金冻结，104：本金解冻，105：交易金额临时冻结，106：交易金额临时解冻，901：信用倍数调整，902：创建资产用户
  double adjusted_value = 4; // 调整的金额
  string memo = 5;           // 备注
  int64 msg_id = 6;
  int64 user_id = 7; // 用户id
}

message NotificationSettlement {
  int32 settle_status = 1; // 结算状态  1: 开始  2: 完成
  int32 exchange_type = 2; // 市场类别: 0:全部 1:A股 2:港股 3:美股
}

message NotificationDvdSettle {
  int32 status = 1; // 状态  1: 开始  2: 完成
}

// 通道基础信息发生变化   exchanger:    topic key：
// channel_info_change.{channel_id}
message ChannelInfoChange {
  int64 channel_id = 1;
  string channel_name = 2;
  int32 channel_state = 3;
  int32 channel_type = 4; // 通道类型 1：外盘通道 2：内盘通道
  int64 account_id = 5;   // 分账号id
  int32 qfii_state = 6;   // 是否支持qfii，用于判断交易时间
}

// 股票信息修改  topic key: stock_info_change.{stock_id}
// ,重新获取query_channel_hold_limit和query_stock_info
message StockInfoChange {
  int64 stock_id = 1; // 证券id,
}

// 汇率发生变化  topic key: exchange_rate_change
message ExchangeRateChange {
  int32 currency = 1;      // 交易币种
  int32 base_currency = 2; // 基准币 默认为HKD
  double buy_rate = 3;     // 买入交易币
  double sell_rate = 4;    // 卖出交易币
  int64 modify_time = 5;   // 汇率维护时间
}

// 用户信息发生变更  topic key: account_info_change.{unit_id}
message AccountInfoChange {
  int64 user_id = 1;    // 用户ID
  int32 trade_mode = 2; // 接入方式 1:USER(用户直连) 2:AGENT(代理托管)
  int64 agent_account =3; // 代理账户(接入方式为2:AGENT时不能为空, 非开户代理, 原merchant)
  string level_rate = 4;   // 融资杠杆比例
  int64 expire_date = 5;   // 到期日期
  double warning_line = 6; // 预警线
  double close_line = 7;   // 平仓线
  int64 trade_state =8; // 交易账号状态 1.正常交易  2.禁止开仓 3.禁止交易 4.账号冻结
  double gem_limit = 9; // 创业板限制
}

// 用户品种保证金变化 topic key: unit_stock_margin_change
message UserStockMarginChange {
  repeated UserStockMarginData margin_data = 1; // 用户id
}

message UserStockMarginData {
  int64 user_id = 1;      // 用户id
  int64 stock_id = 2;     // 用户id
  double margin_rate = 3; // 用户品种保证金
  string maxhold = 4;     // 用户最大持仓量限制
  string minhold = 5;     // 用户最小持仓量限制
}

// 费用变更 topic key: fee_setting_change
message FeeSettingChange { repeated int64 id = 1; }

// 停牌信息  topic_key :  stock_suspension
message StockSuspensionChange {
  repeated StockSuspensionData stock_suspension = 1;
}

message StockSuspensionData {
  int64 stock_id = 1;               // 证券id
  int32 susp_date = 2;              // 停牌日期
  int32 process_date = 3;           // 处理日期
  double margin_rate_increment = 4; // 保证金增量
}

message UserClientNotifyDataChange {
  int64 user_id = 1;      // 用户id
  int64 stock_id = 2;     // 品种id
  double margin_rate = 3; // 保证金比例
}

message NoticeDataChange {
  string content = 1; // 公告内容
}

// 融券额度变化通知   topic key: notification.rq_notify
message RqNotifyChange { repeated RqChange rq_change = 1; }

message RqChange {
  int64 user_id = 1;  // 用户id
  int64 stock_id = 2; // 品种id
  int32 rq_state = 3; // 1新增 2生效 3召回 4删除 5过期
  string stock_code = 4;
  string stock_name = 5;
}


message NotifyPositionChannelInit{
  string content = 1; 
}