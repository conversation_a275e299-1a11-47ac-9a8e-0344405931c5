//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_trade_transfer")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub user_id: i64,
    pub source_account: String,
    pub target_account: String,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub money: Decimal,
    pub status: i8,
    pub create_date: i64,
    pub check_date: i64,
    pub check_name: String,
    pub check_mark: String,
    pub abnormal_flag: i8,
    pub source_state: i8,
    pub target_state: i8,
    pub source_order: String,
    pub target_order: String,
    pub handle_name: String,
    pub business_type: i8,
    pub operation_id: i64,
    pub currency: String,
    pub level_state: i8,
    pub transfer_type: i8,
    pub related_id: i64,
    pub target_level_state: i8,
    pub r#type: i8,
    pub flow_type: i8,
    pub target_user_id: i64,
    pub jtxt: String,
    pub create_name: String,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))", nullable)]
    pub tomoney: Option<Decimal>,
    pub apply_mark: String,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub apply_money: Decimal,
    pub tnterest: String,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
