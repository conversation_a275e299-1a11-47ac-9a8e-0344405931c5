//! Unit tests for SatClient with comprehensive coverage
//!
//! This test module verifies the graceful shutdown functionality,
//! heartbeat integration, and client optimization patterns.

#[cfg(test)]
mod tests {
    use super::super::satclient::SatClient;
    use std::time::Duration;
    use tokio::time::timeout;

    /// Test that SatClient can be initialized successfully
    #[tokio::test]
    async fn test_sat_client_initialization() {
        let client = SatClient::init("http://localhost:50057", "test_client_id", "test_service").await;

        // Verify client is initialized
        assert!(!client.heartbeat.service_name.is_empty());
        println!("✅ SatClient initialization test passed");
    }

    /// Test graceful shutdown functionality
    #[tokio::test]
    async fn test_graceful_shutdown() {
        let client = SatClient::init("http://localhost:50057", "test_client_id", "test_service").await;

        // Test shutdown - should complete within reasonable time
        let shutdown_result = timeout(Duration::from_secs(5), client.shutdown()).await;
        assert!(shutdown_result.is_ok(), "Shutdown should complete within 5 seconds");
        println!("✅ SatClient graceful shutdown test passed");
    }

    /// Test multiple shutdowns don't cause issues
    #[tokio::test]
    async fn test_multiple_shutdowns() {
        let client = SatClient::init("http://localhost:50057", "test_client_id", "test_service").await;

        // First shutdown
        client.shutdown().await;

        // Second shutdown should not panic or hang
        let second_shutdown = timeout(Duration::from_secs(2), client.shutdown()).await;
        assert!(second_shutdown.is_ok(), "Multiple shutdowns should be safe");
        println!("✅ SatClient multiple shutdown test passed");
    }

    /// Test client health checking behavior when service is unavailable
    #[tokio::test]
    async fn test_client_unavailable_handling() {
        let client = SatClient::init("http://localhost:50057", "test_client_id", "test_service").await;

        // Test modify_used when service unavailable - should return error gracefully
        let result = client.modify_used(123, 456, 100).await;
        assert!(result.is_err(), "Should return error when service unavailable");

        // Test recall_rq when service unavailable - should return error gracefully
        let result = client.recall_rq(789).await;
        assert!(result.is_err(), "Should return error when service unavailable");

        println!("✅ SatClient unavailable service handling test passed");
    }

    /// Test that client helper methods work correctly
    #[tokio::test]
    async fn test_helper_methods() {
        let client = SatClient::init("http://localhost:50057", "test_client_id", "test_service").await;

        // Test get_healthy_client - should return None when service unavailable
        let healthy_client = client.get_healthy_client().await;
        assert!(healthy_client.is_none(), "Should return None when service unavailable");

        // Test handle_client_error - should return proper error
        let error_result = client.handle_client_error("test_operation", Some("test_error"));
        assert!(error_result.is_err(), "Should return error");
        assert!(error_result.unwrap_err().to_string().contains("test_operation"));
        assert!(error_result.unwrap_err().to_string().contains("test_error"));

        println!("✅ SatClient helper methods test passed");
    }

    /// Test concurrent operations don't cause issues
    #[tokio::test]
    async fn test_concurrent_operations() {
        let client = SatClient::init("http://localhost:50057", "test_client_id", "test_service").await;

        // Launch multiple concurrent operations
        let handles: Vec<_> = (0..5)
            .map(|i| {
                let client_clone = client.clone();
                tokio::spawn(async move {
                    let _ = client_clone.modify_used(i, i + 100, 50).await;
                    let _ = client_clone.recall_rq(i + 200).await;
                })
            })
            .collect();

        // Wait for all operations to complete
        for handle in handles {
            let _ = handle.await;
        }

        // Cleanup
        client.shutdown().await;
        println!("✅ SatClient concurrent operations test passed");
    }

    /// Test client cloning and independent operation
    #[tokio::test]
    async fn test_client_cloning() {
        let client1 = SatClient::init("http://localhost:50057", "test_client_id", "test_service").await;

        // Clone the client
        let client2 = client1.clone();

        // Both clients should work independently
        let result1 = client1.modify_used(123, 456, 100).await;
        let result2 = client2.recall_rq(789).await;

        // Both should return errors (service unavailable) but not panic
        assert!(result1.is_err());
        assert!(result2.is_err());

        // Shutdown both clients
        client1.shutdown().await;
        client2.shutdown().await;

        println!("✅ SatClient cloning test passed");
    }
}
