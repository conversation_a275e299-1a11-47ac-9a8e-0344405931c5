//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "tfeeset")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub c_fee_type: String,
    #[sea_orm(primary_key, auto_increment = false)]
    pub l_exchange_id: i32,
    #[sea_orm(column_type = "Decimal(Some((12, 8)))")]
    pub en_capital_ratio: Decimal,
    #[sea_orm(column_type = "Decimal(Some((12, 8)))")]
    pub en_face_value_ratio: Decimal,
    #[sea_orm(column_type = "Decimal(Some((12, 8)))", nullable)]
    pub en_amount_ratio: Option<Decimal>,
    #[sea_orm(column_type = "Decimal(Some((12, 4)))", nullable)]
    pub en_maximum_fee: Option<Decimal>,
    #[sea_orm(column_type = "Decimal(Some((12, 4)))", nullable)]
    pub en_minimum_fee: Option<Decimal>,
    pub vc_currency_no: Option<String>,
    pub l_modify_date: i32,
    #[sea_orm(primary_key, auto_increment = false)]
    pub c_order_direction: String,
    #[sea_orm(primary_key, auto_increment = false)]
    pub l_unit_id: i32,
    pub c_serfee_type: String,
    pub c_close_type: String,
    #[sea_orm(primary_key, auto_increment = false)]
    pub c_stock_type: String,
    pub c_decimal_type: Option<String>,
    #[sea_orm(primary_key, auto_increment = false)]
    pub l_channel_id: i32,
    pub l_plate_id: i32,
    pub l_tid: i64,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
