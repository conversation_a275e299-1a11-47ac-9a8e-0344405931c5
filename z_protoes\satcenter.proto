// 数据交互proto文件
syntax = "proto3";

package phoenixsatcenter;


service SatCenter{
  rpc ModifyRq(ModifyRqReq) returns(ModifyRqResp){} //融券召回操作
  //rpc ModifyRqByAssets(ModifyRqAssetsReq) returns(ModifyRqResp){} //运营中心操作
}
message ModifyRqReqList{
  int64 stock_id=1; //品种id
  int64 user_id=2;//用户id
  int32 use_num=3;//更新数量
  int32 credit_num=4;//额度
  string mark=5;//备注
  int32 exec=6;//执行目的:1删除 , 2召回 ,3更新
  int64 rq_id=7;//删除,更新的时候传这个id
}

message ModifyRqReq{
  repeated ModifyRqReqList rqlist=1;
}

message ModifyRqResp{
  string err_msg = 1;
  sint32 err_code = 2;
}

//message ModifyRqAssetsReq{
//  int64 rq_id=1; //id
//  int32 credit_num=2;//额度 更新操作的时候,必须传值
//  int32 use_num=3;//已用额度 更新操作的时候,必须传值
//  int32 state=4;//状态
//  string mark=5;//备注
//  int32 exec=6;//执行目的:1新增 , 2修改
//}
