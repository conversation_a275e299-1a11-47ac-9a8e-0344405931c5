use crate::dataservice::{
    entities::{
        prelude::{SysConfig, SysConfigEntity},
        sys_config,
    },
};
use dbconnection::DbConnection;
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, DbErr, EntityTrait, QueryFilter};

impl SysConfig {
    pub async fn _find_by_code(db: &DbConnection, code: &str) -> Result<SysConfig> {
        let ret_data: Result<Option<SysConfig>, DbErr> = SysConfigEntity::find().filter(sys_config::Column::ConfigCode.eq(code)).one(db.get_connection()).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        let data = ret_data.unwrap();
        return match data {
            Some(v) => Ok(v),
            None => Err(anyhow!("SysConfig数据不存在")),
        };
    }
}
