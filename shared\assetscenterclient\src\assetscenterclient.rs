//! Assets Center Client with Graceful Shutdown Support
//!
//! This client provides connection management for the Phoenix Assets Center service
//! with built-in heartbeat monitoring and graceful shutdown capabilities.
//!
//! # Example Usage
//!
//! ```rust
//! use assetscenterclient::AssetsCenterClient;
//! use std::time::Duration;
//!
//! #[tokio::main]
//! async fn main() -> anyhow::Result<()> {
//!     // Initialize the client
//!     let client = AssetsCenterClient::init(
//!         "http://localhost:50051",  // endpoint
//!         "my_client_id",            // client_id
//!         "my_service_name"          // service_name
//!     ).await;
//!     
//!     // Use the client for operations
//!     // ... perform your business operations ...
//!     
//!     // Gracefully shutdown when done
//!     client.shutdown().await?;
//!     
//!     Ok(())
//! }
//! ```
//!
//! # Graceful Shutdown
//!
//! The client supports graceful shutdown through the `shutdown()` method which:
//! - Stops the background connection management task
//! - Clears the active client connection
//! - Ensures proper cleanup of resources
//!
//! The background task will respond to shutdown signals quickly (within 1 second)
//! even during heartbeat monitoring loops.

use anyhow::{Result, anyhow};
use common::logclient::log_error;
use heartbeatclient::HeartbeatClient;

use protoes::assetscenter::{
    PhoenixassetscenterQueryRequest, PhoenixassetscenterRequest, PhoenixassetscenterResponse, PositionMarginRateReq, PositionMarginRateResp, PositionPriceChangeReq, PositionPriceChangeResp,
    phoenixassetscenter_client::PhoenixassetscenterClient,
};
use std::{sync::Arc, time::Duration};
use tokio::sync::{RwLock, broadcast};
use tonic::transport::Channel;
use tracing::*;
#[derive(Clone, Debug)]
pub struct AssetsCenterClient {
    client: Arc<RwLock<Option<PhoenixassetscenterClient<Channel>>>>,
    heartbeat: HeartbeatClient,
    shutdown_tx: broadcast::Sender<()>,
    // uri: String,
}

impl AssetsCenterClient {
    pub async fn init(endpoint: &str, client_id: &str, service_name: &str) -> Self {
        // 等待 HeartbeatClient 初始化（包含重試邏輯）
        // let heartbeat = HeartbeatClient::new(endpoint, client_id.to_string(), service_name.to_string()).await;
        let heartbeat = HeartbeatClient::new(endpoint, client_id.to_string(), service_name.to_string(), true).await;
        let client = Arc::new(RwLock::new(None));

        // Create shutdown channel
        let (shutdown_tx, _shutdown_rx) = broadcast::channel(1);

        let endpoint_owned = endpoint.to_string();
        if let Ok(channel) = tonic::transport::Channel::from_shared(endpoint_owned.clone()) {
            match channel.connect().await {
                Ok(channel) => {
                    let proto_client = PhoenixassetscenterClient::new(channel);
                    let mut wr = client.write().await;
                    *wr = Some(proto_client);
                }
                Err(e) => {
                    error!("连接资产中心失败: {}", e);
                }
            }
        }
        info!("资产中心客户端初始化结束...{:?}", &client);
        let client_clone = client.clone();

        // Clone values to move into the async task
        let endpoint_owned = endpoint.to_string();
        let service_name_owned = service_name.to_string();
        let heartbeat_clone = heartbeat.clone();
        let shutdown_tx_clone = shutdown_tx.clone();

        // 啟動後台任務管理業務客戶端連接
        tokio::spawn(async move {
            let mut shutdown_rx = shutdown_tx_clone.subscribe();
            loop {
                tokio::select! {
                    _ = shutdown_rx.recv() => {
                        info!(service = %service_name_owned, "Received shutdown signal, stopping connection management task");
                        break;
                    }
                    _ = async {
                        info!(service = %service_name_owned, endpoint = %endpoint_owned, "Attempting to connect Service client");
                        match tonic::transport::Channel::from_shared(endpoint_owned.clone()) {
                            Ok(channel) => match channel.connect().await {
                                Ok(channel) => {
                                    {
                                        if client_clone.read().await.is_none() {
                                            let proto_client = PhoenixassetscenterClient::new(channel);
                                            let mut wr = client_clone.write().await;
                                            *wr = Some(proto_client);
                                            // *client_clone.write().await.unwrap() = Some(proto_client);
                                            info!(service = %service_name_owned, "Service client connected");
                                        }
                                    }
                                    // 等待 HeartbeatClient 報告不健康，然後重試
                                    let mut heartbeat_shutdown_rx = shutdown_tx_clone.subscribe();
                                    loop {
                                        tokio::select! {
                                            _ = heartbeat_shutdown_rx.recv() => {
                                                info!(service = %service_name_owned, "Received shutdown signal during heartbeat monitoring");
                                                return;
                                            }
                                            _ = tokio::time::sleep(Duration::from_secs(1)) => {
                                                if !heartbeat_clone.is_healthy() {
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                    {
                                        let mut wr = client_clone.write().await;
                                        *wr = None::<PhoenixassetscenterClient<tonic::transport::Channel>>;

                                        error!(service = %service_name_owned, "Service client disconnected due to unhealthy state");
                                    }
                                }
                                Err(e) => {
                                    error!(service = %service_name_owned, "Service client connection failed: {}", e);
                                }
                            },
                            Err(e) => {
                                error!(service = %service_name_owned, "Invalid endpoint: {}", e);
                            }
                        }
                        warn!(service = %service_name_owned, "Retrying Service client connection in 2 seconds");
                        tokio::time::sleep(Duration::from_secs(2)).await;
                    } => {}
                }
            }
            info!(service = %service_name_owned, "Connection management task terminated");
        });

        Self {
            client,
            heartbeat,
            shutdown_tx,
            // uri: endpoint.to_string(),
        }
    }

    /// Helper method to get a healthy client connection
    /// Returns Ok(client) if healthy and connected, Err otherwise
    ///
    /// # Performance optimizations:
    /// - Fast health check (no async ops)
    /// - Functional chain with lazy error creation
    /// - Automatic lock release (no explicit drop needed)
    async fn get_healthy_client(&self) -> Result<PhoenixassetscenterClient<Channel>> {
        // Fast health check first (synchronous)
        if !self.heartbeat.is_healthy() {
            return Err(anyhow!("资产中心服务不健康，请稍后重试"));
        }

        // Functional chain: read lock -> clone if Some -> error if None
        self.client.read().await.as_ref().cloned().ok_or_else(|| anyhow!("资产中心客户端未连接"))
    }

    // Alternative ultra-optimized version (for extreme performance scenarios):
    // This could be even faster but sacrifices some readability
    /*
    async fn get_healthy_client_ultra_fast(&self) -> Result<PhoenixassetscenterClient<Channel>> {
        (!self.heartbeat.is_healthy())
            .then(|| Err(anyhow!("资产中心服务不健康，请稍后重试")))
            .unwrap_or_else(|| async {
                self.client.read().await.as_ref().cloned()
                    .ok_or_else(|| anyhow!("资产中心客户端未连接"))
            }.await)
    }
    */

    /// Helper method to handle client errors and reset connection
    async fn handle_client_error(&self, error: tonic::Status, operation: &str) -> anyhow::Error {
        // Reset client connection on error
        let mut write_guard = self.client.write().await;
        *write_guard = None;
        drop(write_guard);

        // Log error asynchronously
        let error_msg = format!("资产中心 {} 操作失败: {:?}", operation, error);
        log_error(&error_msg).await;

        anyhow!("资产中心操作失败")
    }

    pub async fn phoenix_assets_change(&self, request: &PhoenixassetscenterRequest) -> Result<PhoenixassetscenterResponse> {
        // Get healthy client (combines health check and client acquisition)
        let mut client = self.get_healthy_client().await?;

        // Make the RPC call - using request.clone() is necessary for tonic
        // but it's more efficient than .to_owned() for protobuf messages
        match client.phoenix_assets_change(request.clone()).await {
            Ok(response) => Ok(response.into_inner()),
            Err(err) => Err(self.handle_client_error(err, "phoenix_assets_change").await),
        }
    }

    pub async fn phoenix_assets_query(&self, request: &PhoenixassetscenterQueryRequest) -> Result<PhoenixassetscenterResponse> {
        info!("开始从资产中心通过phoenix_assets_query获取数据:{:?}", &request);

        // Get healthy client (combines health check and client acquisition)
        let mut client = self.get_healthy_client().await?;

        // Make the RPC call
        match client.phoenix_assets_query(request.clone()).await {
            Ok(response) => Ok(response.into_inner()),
            Err(err) => Err(self.handle_client_error(err, "phoenix_assets_query").await),
        }
    }

    pub async fn phoenix_positions_marginrate_change(&self, request: &PositionMarginRateReq) -> Result<PositionMarginRateResp> {
        // Get healthy client (combines health check and client acquisition)
        let mut client = self.get_healthy_client().await?;

        // Make the RPC call
        match client.phoenix_positions_marginrate_change(request.clone()).await {
            Ok(response) => Ok(response.into_inner()),
            Err(err) => Err(self.handle_client_error(err, "phoenix_positions_marginrate_change").await),
        }
    }

    pub async fn phoenix_positions_price_change(&self, request: &PositionPriceChangeReq) -> Result<PositionPriceChangeResp> {
        // Get healthy client (combines health check and client acquisition)
        let mut client = self.get_healthy_client().await?;

        // Make the RPC call
        match client.phoenix_positions_price_change(request.clone()).await {
            Ok(response) => Ok(response.into_inner()),
            Err(err) => Err(self.handle_client_error(err, "phoenix_positions_price_change").await),
        }
    }

    /// Gracefully shutdown the AssetsCenterClient
    /// This will stop the background connection management task and cleanup resources
    pub async fn shutdown(&self) -> Result<()> {
        info!("Initiating graceful shutdown of AssetsCenterClient");

        // Send shutdown signal to background task
        if let Err(e) = self.shutdown_tx.send(()) {
            warn!("Failed to send shutdown signal: {:?}", e);
        }

        // Clear the client connection
        {
            let mut write_guard = self.client.write().await;
            *write_guard = None;
        }

        info!("AssetsCenterClient shutdown completed");
        Ok(())
    }
}
