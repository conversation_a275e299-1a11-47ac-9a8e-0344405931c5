// // use crate::protofiles::hqcenter::YsHqInfo;
// use crate::client::satclient::SatClient;
// use crate::dataservice::{dbsetup::DbConnection, entities::prelude::*};
// use crate::server::controller::PhoenixController;
// use crate::server::service::accountassetsservice::PhoenixAccountAssetsService;
// use crate::server::service::accountstockpositionservice::PhoenixAccountStockPositionService;
// use crate::server::service::assetscenterservice::AssetsCenterServcie;
// use crate::server::service::basiccacheservice::BasicCacheService;
// use crate::server::service::manageservice::ManageService;
// use crate::server::service::securities_borrow_pool::SecuritiesBorrowPool;
// use crate::server::service::userassetsservice::UserAssetsService;
// use crate::server::service::userpositionservice::UserPositionService;
// use crate::{config::settings::Settings, server::controller::PersistData};
// use akaclient::akaclient::{AkaCacheOption, AkaClient};
// use anyhow::Result;
// use common::constant;
// use common::redisclient::redispool::{RedisClient, RedisConfig};
// use common::uidservice::UidgenService;
// use config::{Config, File};
// use messagecenter::notificationclient::NotificationClient;
// use protoes::phoenixnotification::NotificationMessage;
// use std::sync::Arc;
// use tokio::sync::{mpsc, RwLock};
// use tracing::*;

// #[tokio::test]
// async fn test_orderexec() {
//     //初始买单
//     // let order = NotificationOrderExec {
//     //   unit_id: 200840,
//     //   order_id: 89206523,
//     //   stock_id: 10773,
//     //   channel_id: 10,
//     //   channel_type: 1,
//     //   order_quantity: 100,
//     //   order_price: 8.3,
//     //   order_direction: 1,
//     //   exec_type: OrderExecType::NewOrder as i32,
//     //   deal_no: 0,
//     //   memo: "".to_string(),
//     //   msg_id: 70175227,
//     //   algorithm_id: 0,
//     //   user_id: 200840,
//     // };

//     // //2.成交回报
//     // // let order = NotificationOrderExec {
//     // //   unit_id: 200840,
//     // //   order_id: 3698683,
//     // //   stock_id: 10773,
//     // //   channel_id: 10,
//     // //   channel_type: 1,
//     // //   order_quantity: 100,
//     // //   order_price: 8.34,
//     // //   order_direction: 1,
//     // //   exec_type: OrderExecType::OrderFill as i32,
//     // //   deal_no: 46465274,
//     // //   memo: "".to_string(),
//     // //   msg_id: 46465275,
//     // // };
//     // //3.卖单

//     // let stub = self::new_stub().await;
//     // let ret = stub.handle_assets_by_dealinfo(&order).await;
//     // // info!("result is:{:?}", &ret);
//     // // let _ = persist(&stub).await;
//     // // let (tx_persist, mut rx_persist) = mpsc::channel::<PersistData>(1024);
//     // // let stub_clone = stub.clone();
//     // // tokio::spawn(async move {
//     // //   loop {
//     // //     tokio::select! {
//     // //       persist = rx_persist.recv()=>{
//     // //         info!("收到持久化消息......{:?}",&persist);
//     // //         if let Some(persist_data) = persist{
//     // //           let _ = stub_clone.persist_data(&persist_data).await;
//     // //         }
//     // //       }
//     // //     }
//     // //   }
//     // // });
//     // // std::thread::sleep(std::time::Duration::from_secs(5));
//     // loop {tokio::time::sleep(Duration::from_secs(1)).await;}
//     // assert_eq!(1, 1)
// }

// async fn _new_stub() -> PhoenixController {
//     // let version = env!("CARGO_PKG_VERSION");
//     // let pwd = env!("CARGO_TARGET_DIR");
//     println!("CARGO_MANIFEST_DIR : {}", env!("CARGO_MANIFEST_DIR"));
//     // println!("CARGO_BUILD_TARGET_DIR :{}", env!("CARGO_BUILD_TARGET_DIR"));
//     // log4rs::init_file(format!("{}/../config/log.yaml", env!("CARGO_MANIFEST_DIR")), Default::default()).unwrap();
//     let config: Settings = Config::builder()
//         .add_source(File::with_name(&format!("{}/../config/accountriskcenter", env!("CARGO_MANIFEST_DIR"))))
//         .build()
//         .expect("build config file error")
//         .try_deserialize()
//         .expect("deserialize error");
//     info!("configs:{:?}", &config);

//     let dbconn = DbConnection::new(&config.database.stock_uri.as_str()).await;

//     let (tx_persist, _rx_persist) = mpsc::channel::<PersistData>(1024);
//     let (tx_assets, mut _rx_assets) = tokio::sync::mpsc::channel::<(i64, i64)>(16);
//     let (tx_rq, _rx_rq) = tokio::sync::mpsc::channel::<(i64, i64, i32)>(16);

//     let opt = AkaCacheOption {
//         use_cache: true,
//         mq_uri: format!("{}{}", &config.mq.amqpaddr, &config.notification.vhost),
//         exchange: config.notification.notification_exchanger.clone(),
//         routing_keys: "notification.aka.#".to_owned(),
//     };
//     let aka_svc = AkaClient::init(config.servers.akacenterserver.to_string(), &opt).await;
//     let uidsvc = Arc::new(RwLock::new(UidgenService::new(config.application.machineid, config.application.nodeid)));
//     let account_position_svc = PhoenixAccountStockPositionService::new();
//     let account_assets_svc = PhoenixAccountAssetsService::new();
//     let sys_info = PhoenixSysSystem::query(&dbconn).await.expect("query phoenix_sys_system info error");
//     let ignore_accounts: Vec<i64> = config.system.ignore_user_account.split(',').map(|f| f.parse::<i64>().unwrap_or_default()).collect();
//     // let ignore_fee_accounts: Vec<i64> = config.system.ignore_fee_account.split(',').map(|f| f.parse::<i64>().unwrap_or_default()).collect();
//     let userassetssvc = UserAssetsService::new();
//     let userpositionsvc = UserPositionService::new();
//     let securities_borrow_pool = SecuritiesBorrowPool::new();
//     let basic_cache_svc = Arc::new(BasicCacheService::new());

//     let mut rdscfg = RedisConfig::default();
//     rdscfg.urls = config.redis.uri.to_owned();
//     rdscfg.max_size = 6;
//     let redis_client = RedisClient::new(&rdscfg).expect("redis cant connect");

//     let manager_svc = Arc::new(ManageService::new(&config.servers.managerserver).await);
//     let assetscenter_svc = Arc::new(AssetsCenterServcie::new(&config.servers.assetscenterserver).await);
//     let sat_client = SatClient::init(config.servers.satcenterserver.to_string()).await;

//     //推送mq信息给用户客户端消息
//     let (tx_user_notify, _rx_user_notify) = tokio::sync::mpsc::channel::<NotificationMessage>(1024);
//     let queue_name = "phoenix_user_notify_client";
//     let user_notify_client = NotificationClient::new(
//         &config.notification.notification_exchanger,
//         queue_name,
//         config.notification.accountriskcenter_routing_key.to_string(),
//         &format!("{}{}", &config.mq.amqpaddr, &config.notification.vhost),
//         tx_user_notify,
//     )
//     .await;

//     let data = aka_svc.query_stock_info(10312).await;
//     info!("{:?}", data.unwrap());

//     let (tx_notification, _rx_notification) = tokio::sync::mpsc::channel::<NotificationMessage>(1024);

//     //连接消息中心服务
//     let queue_name = "phoenix_notification_client_queue";
//     let notification_client = Arc::new(RwLock::new(
//         NotificationClient::new(
//             &config.notification.notification_exchanger.as_str(),
//             queue_name,
//             config.notification.accountriskcenter_routing_key.clone(),
//             format!("{}{}", &config.mq.amqpaddr, &config.notification.vhost).as_str(),
//             tx_notification,
//         )
//         .await,
//     ));

//     // let mut notification_client_clone = notification_client.clone();
//     messagecenter::init::init_notification_client(notification_client.clone()).await;
//     messagecenter::init::init_notification_listen(notification_client).await;

//     let stub = PhoenixController {
//         dbconn: Arc::new(dbconn.to_owned()),
//         // basedata_svc: Arc::new(basedata_svc),
//         account_position_svc: Arc::new(account_position_svc),
//         account_assets_svc: Arc::new(account_assets_svc),
//         aka_svc: Arc::new(aka_svc),
//         setting: Arc::new(RwLock::new(config)),
//         sys_info: Arc::new(RwLock::new(sys_info)),
//         tx_persist,
//         ignore_accounts,
//         // ignore_fee_accounts,
//         user_assets_svc: Arc::new(userassetssvc),
//         user_position_svc: Arc::new(userpositionsvc),
//         securities_borrow_pool: Arc::new(securities_borrow_pool),
//         basic_cache_svc: basic_cache_svc,
//         tx_assets: tx_assets,
//         tx_rq,
//         assetscenter_svc: assetscenter_svc,
//         manager_svc: manager_svc,
//         notify: Arc::new(user_notify_client),
//         uidsvc,
//         redis_client: Arc::new(redis_client),
//         sat_client: Arc::new(sat_client),
//     };
//     info!("开始初始化数据......");
//     let _ = stub.init(true).await;
//     info!("数据初始化结束......");

//     stub
// }

// async fn _persist(stub_clone: &PhoenixController) -> Result<()> {
//     // stub_clone.persist_data_interval().await
//     info!("start to persist data");
//     let account_assets = stub_clone.account_assets_svc.get_account_assets(constant::VALUE_ALL).await;
//     info!("all account assets:{:#?}", &account_assets);
//     let dt = PersistData::AccountAssets(Box::new(account_assets.to_owned()));
//     let _ = stub_clone.persist_data(&dt).await;
//     let account_positions = stub_clone.account_position_svc.get_account_stock_positions(constant::VALUE_ALL).await;
//     info!("all account positions:{:#?}", &account_positions);
//     let dt = PersistData::AccountPosition(Box::new(account_positions.to_owned()));
//     let _ = stub_clone.persist_data(&dt).await;
//     // let account_positions_his = stub_clone.
//     Ok(())
// }

// // #[tokio::test]
// // async fn modify_unit_state() {
// //     let mut hq_client = ManagerUnitClient::connect("http://52.131.220.224:8305").await.expect("管理端服务连接失败");
// //     let req = ModifyUnitReq {
// //         user_id: 200971,
// //         status: 1,
// //         push_msg: 0,
// //         rate: 1.0,
// //     };
// //     let ret = hq_client.modify_unit_state(req).await;
// //     println!("modify_unit_state!---------------,{:?}", ret);
// //     if let Err(err) = ret {
// //         info!("modify_unit_state异常,{:?}", err);
// //     }
// // }

// // #[tokio::test]
// // async fn grpc_test() {
// //     let mut hq_client = AccountRiskCenterClient::connect("http://40.72.97.246:8401").await.expect("服务连接失败");
// //     let req = UserPositionReq { unit_id: 200844, user_id: 0, stock_id: 0 };
// //     let ret = hq_client.query_user_positions(req).await;
// //     if ret.is_err() {
// //         info!("query_user_positions 异常,{:?}", ret.as_ref().unwrap_err());
// //     } else {
// //         println!("{:?}", ret.unwrap().into_inner().positions);
// //     }

// //     let req = UserAssetsReq { unit_id: vec![], user_id: vec![200844] };
// //     let ret = hq_client.query_user_assets(req).await;
// //     if ret.is_err() {
// //         info!("query_user_assets 异常,{:?}", ret.as_ref().unwrap_err());
// //     } else {
// //         println!("{:?}", ret.unwrap().into_inner());
// //     }

// //     let req = MarginRatioReq { user_id: 200844, stock_id: 0 };
// //     let ret = hq_client.query_margin_ratio(req).await;
// //     if ret.is_err() {
// //         info!("query_margin_ratio 异常,{:?}", ret.as_ref().unwrap_err());
// //     } else {
// //         println!("{:?}", ret.unwrap().into_inner());
// //     }

// //     let req = UserRqReq { user_id: 200844, stock_id: 0 };
// //     let ret = hq_client.query_user_rq(req).await;
// //     if ret.is_err() {
// //         info!("query_user_rq 异常,{:?}", ret.as_ref().unwrap_err());
// //     } else {
// //         println!("{:#?}", ret.unwrap().into_inner());
// //     }
// // }

// // #[tokio::test]
// // async fn test() {
// //     let num = -0.0000000037451282878464554;
// //     let n = format!("{:.6}", num);
// //     println!("{}", n);

// //     let n = n.parse::<f64>();
// //     let d = n.unwrap_or_default();
// //     if d < 0.0 {
// //         println!("!!!!!!!!!!!!!!")
// //     }
// // }
