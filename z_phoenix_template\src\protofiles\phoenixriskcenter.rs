// This file is @generated by prost-build.
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PhoenixRiskCheckRequest {
    #[prost(message, optional, tag = "1")]
    pub queryinfo: ::core::option::Option<PhoenixRiskCheckInfo>,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PhoenixRiskCheckInfo {
    /// 用户unit_id,
    #[prost(int64, tag = "1")]
    pub unit_id: i64,
    /// 股票ID,
    #[prost(int64, tag = "2")]
    pub stock_id: i64,
    /// 价格,
    #[prost(double, tag = "3")]
    pub order_price: f64,
    /// 数量，
    #[prost(int32, tag = "4")]
    pub order_amount: i32,
    /// 方向（ 1:买 2：卖）
    #[prost(int32, tag = "5")]
    pub order_direction: i32,
    /// 通道类型：1：外盘 2：内盘
    #[prost(int32, tag = "6")]
    pub channel_type: i32,
    /// 委托类型 1:app下单  2:跟单  3:风控止盈止损平仓单,4:风控总资产预警平仓单 5:pc客户端单 6:结算平仓单 7:管理端强平仓单,8:app清仓,9:pc清仓,10,管理员平仓,11,合约到期日强平
    #[prost(int32, tag = "7")]
    pub order_type: i32,
    /// 通道
    #[prost(int64, tag = "8")]
    pub order_channel: i64,
    /// 市场ID
    #[prost(int64, tag = "9")]
    pub market_id: i64,
    /// 1:USER(用户直连) 2:AGENT(代理托管)
    #[prost(int32, tag = "10")]
    pub trade_mode: i32,
    /// 代理账户
    #[prost(int64, tag = "11")]
    pub agent_account: i64,
    /// 是否融券订单
    #[prost(int32, tag = "12")]
    pub is_rq: i32,
    /// 保证金比例
    #[prost(double, tag = "13")]
    pub margin_ratio: f64,
    /// 用户user_id,
    #[prost(int64, tag = "14")]
    pub user_id: i64,
    /// 账户币种
    #[prost(string, tag = "15")]
    pub base_currency: ::prost::alloc::string::String,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PhoenixRiskCheckResponse {
    /// 返回结果
    #[prost(string, tag = "1")]
    pub ret_code: ::prost::alloc::string::String,
    /// 返回结果
    #[prost(string, tag = "2")]
    pub ret_msg: ::prost::alloc::string::String,
    #[prost(message, repeated, tag = "3")]
    pub retinfo: ::prost::alloc::vec::Vec<PhoenixRiskCheckInfo>,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PhoenixRiskRequest {
    #[prost(message, optional, tag = "1")]
    pub queryinfo: ::core::option::Option<PhoenixRiskCheckInfo>,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PhoenixRiskResponse {
    /// 返回结果
    #[prost(int32, tag = "1")]
    pub ret_code: i32,
    /// 返回信息
    #[prost(string, tag = "2")]
    pub ret_msg: ::prost::alloc::string::String,
}
/// Generated client implementations.
pub mod phoenix_riskcenter_client {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    use tonic::codegen::http::Uri;
    #[derive(Debug, Clone)]
    pub struct PhoenixRiskcenterClient<T> {
        inner: tonic::client::Grpc<T>,
    }
    impl PhoenixRiskcenterClient<tonic::transport::Channel> {
        /// Attempt to create a new client by connecting to a given endpoint.
        pub async fn connect<D>(dst: D) -> Result<Self, tonic::transport::Error>
        where
            D: TryInto<tonic::transport::Endpoint>,
            D::Error: Into<StdError>,
        {
            let conn = tonic::transport::Endpoint::new(dst)?.connect().await?;
            Ok(Self::new(conn))
        }
    }
    impl<T> PhoenixRiskcenterClient<T>
    where
        T: tonic::client::GrpcService<tonic::body::Body>,
        T::Error: Into<StdError>,
        T::ResponseBody: Body<Data = Bytes> + std::marker::Send + 'static,
        <T::ResponseBody as Body>::Error: Into<StdError> + std::marker::Send,
    {
        pub fn new(inner: T) -> Self {
            let inner = tonic::client::Grpc::new(inner);
            Self { inner }
        }
        pub fn with_origin(inner: T, origin: Uri) -> Self {
            let inner = tonic::client::Grpc::with_origin(inner, origin);
            Self { inner }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> PhoenixRiskcenterClient<InterceptedService<T, F>>
        where
            F: tonic::service::Interceptor,
            T::ResponseBody: Default,
            T: tonic::codegen::Service<
                http::Request<tonic::body::Body>,
                Response = http::Response<
                    <T as tonic::client::GrpcService<tonic::body::Body>>::ResponseBody,
                >,
            >,
            <T as tonic::codegen::Service<
                http::Request<tonic::body::Body>,
            >>::Error: Into<StdError> + std::marker::Send + std::marker::Sync,
        {
            PhoenixRiskcenterClient::new(InterceptedService::new(inner, interceptor))
        }
        /// Compress requests with the given encoding.
        ///
        /// This requires the server to support it otherwise it might respond with an
        /// error.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.send_compressed(encoding);
            self
        }
        /// Enable decompressing responses.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.accept_compressed(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_decoding_message_size(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_encoding_message_size(limit);
            self
        }
        pub async fn phoenix_risk_check(
            &mut self,
            request: impl tonic::IntoRequest<super::PhoenixRiskCheckRequest>,
        ) -> std::result::Result<
            tonic::Response<super::PhoenixRiskCheckResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/phoenixriskcenter.PhoenixRiskcenter/phoenix_risk_check",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "phoenixriskcenter.PhoenixRiskcenter",
                        "phoenix_risk_check",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        pub async fn phoenix_risk_test(
            &mut self,
            request: impl tonic::IntoRequest<super::PhoenixRiskRequest>,
        ) -> std::result::Result<
            tonic::Response<super::PhoenixRiskResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/phoenixriskcenter.PhoenixRiskcenter/phoenix_risk_test",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "phoenixriskcenter.PhoenixRiskcenter",
                        "phoenix_risk_test",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
    }
}
/// Generated server implementations.
pub mod phoenix_riskcenter_server {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    /// Generated trait containing gRPC methods that should be implemented for use with PhoenixRiskcenterServer.
    #[async_trait]
    pub trait PhoenixRiskcenter: std::marker::Send + std::marker::Sync + 'static {
        async fn phoenix_risk_check(
            &self,
            request: tonic::Request<super::PhoenixRiskCheckRequest>,
        ) -> std::result::Result<
            tonic::Response<super::PhoenixRiskCheckResponse>,
            tonic::Status,
        >;
        async fn phoenix_risk_test(
            &self,
            request: tonic::Request<super::PhoenixRiskRequest>,
        ) -> std::result::Result<
            tonic::Response<super::PhoenixRiskResponse>,
            tonic::Status,
        >;
    }
    #[derive(Debug)]
    pub struct PhoenixRiskcenterServer<T> {
        inner: Arc<T>,
        accept_compression_encodings: EnabledCompressionEncodings,
        send_compression_encodings: EnabledCompressionEncodings,
        max_decoding_message_size: Option<usize>,
        max_encoding_message_size: Option<usize>,
    }
    impl<T> PhoenixRiskcenterServer<T> {
        pub fn new(inner: T) -> Self {
            Self::from_arc(Arc::new(inner))
        }
        pub fn from_arc(inner: Arc<T>) -> Self {
            Self {
                inner,
                accept_compression_encodings: Default::default(),
                send_compression_encodings: Default::default(),
                max_decoding_message_size: None,
                max_encoding_message_size: None,
            }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> InterceptedService<Self, F>
        where
            F: tonic::service::Interceptor,
        {
            InterceptedService::new(Self::new(inner), interceptor)
        }
        /// Enable decompressing requests with the given encoding.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.accept_compression_encodings.enable(encoding);
            self
        }
        /// Compress responses with the given encoding, if the client supports it.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.send_compression_encodings.enable(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.max_decoding_message_size = Some(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.max_encoding_message_size = Some(limit);
            self
        }
    }
    impl<T, B> tonic::codegen::Service<http::Request<B>> for PhoenixRiskcenterServer<T>
    where
        T: PhoenixRiskcenter,
        B: Body + std::marker::Send + 'static,
        B::Error: Into<StdError> + std::marker::Send + 'static,
    {
        type Response = http::Response<tonic::body::Body>;
        type Error = std::convert::Infallible;
        type Future = BoxFuture<Self::Response, Self::Error>;
        fn poll_ready(
            &mut self,
            _cx: &mut Context<'_>,
        ) -> Poll<std::result::Result<(), Self::Error>> {
            Poll::Ready(Ok(()))
        }
        fn call(&mut self, req: http::Request<B>) -> Self::Future {
            match req.uri().path() {
                "/phoenixriskcenter.PhoenixRiskcenter/phoenix_risk_check" => {
                    #[allow(non_camel_case_types)]
                    struct phoenix_risk_checkSvc<T: PhoenixRiskcenter>(pub Arc<T>);
                    impl<
                        T: PhoenixRiskcenter,
                    > tonic::server::UnaryService<super::PhoenixRiskCheckRequest>
                    for phoenix_risk_checkSvc<T> {
                        type Response = super::PhoenixRiskCheckResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::PhoenixRiskCheckRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as PhoenixRiskcenter>::phoenix_risk_check(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = phoenix_risk_checkSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/phoenixriskcenter.PhoenixRiskcenter/phoenix_risk_test" => {
                    #[allow(non_camel_case_types)]
                    struct phoenix_risk_testSvc<T: PhoenixRiskcenter>(pub Arc<T>);
                    impl<
                        T: PhoenixRiskcenter,
                    > tonic::server::UnaryService<super::PhoenixRiskRequest>
                    for phoenix_risk_testSvc<T> {
                        type Response = super::PhoenixRiskResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::PhoenixRiskRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as PhoenixRiskcenter>::phoenix_risk_test(&inner, request)
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = phoenix_risk_testSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                _ => {
                    Box::pin(async move {
                        let mut response = http::Response::new(
                            tonic::body::Body::default(),
                        );
                        let headers = response.headers_mut();
                        headers
                            .insert(
                                tonic::Status::GRPC_STATUS,
                                (tonic::Code::Unimplemented as i32).into(),
                            );
                        headers
                            .insert(
                                http::header::CONTENT_TYPE,
                                tonic::metadata::GRPC_CONTENT_TYPE,
                            );
                        Ok(response)
                    })
                }
            }
        }
    }
    impl<T> Clone for PhoenixRiskcenterServer<T> {
        fn clone(&self) -> Self {
            let inner = self.inner.clone();
            Self {
                inner,
                accept_compression_encodings: self.accept_compression_encodings,
                send_compression_encodings: self.send_compression_encodings,
                max_decoding_message_size: self.max_decoding_message_size,
                max_encoding_message_size: self.max_encoding_message_size,
            }
        }
    }
    /// Generated gRPC service name
    pub const SERVICE_NAME: &str = "phoenixriskcenter.PhoenixRiskcenter";
    impl<T> tonic::server::NamedService for PhoenixRiskcenterServer<T> {
        const NAME: &'static str = SERVICE_NAME;
    }
}
