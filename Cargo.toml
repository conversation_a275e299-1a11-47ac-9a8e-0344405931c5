[workspace]
resolver = "2"
members = [
    # "client",
    # "phoenix_template",
    # "phoenix_migration_tool",
    "phoenix_logcenter",
    "phoenix_zfix",
    "quotation/*",
    "sapis/*",
    "shared/*",
    "strader/*",
]

# [resolver]
# version = "3"
# incompatible-rust-versions = "fallback"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
strip = true

[workspace.dependencies]
utility = { path = "./shared/utility" }
common = { path = "./shared/common" }
messagecenter = { path = "./shared/messagecenter" }
akaclient = { path = "./shared/akaclient" }
protoes = { path = "./shared/protoes" }

ordercenterclient = { path = "./shared/ordercenterclient" }
riskcenterclient = { path = "./shared/riskcenterclient" }
accountriskcenterclient = { path = "./shared/accountriskcenterclient" }
assetscenterclient = { path = "./shared/assetscenterclient" }
manageclient = { path = "./shared/manageclient" }
hqcenterclient = { path = "./shared/hqcenterclient" }
tokio = { version = "1.45.1", features = ["full"] }
tokio-util = { version = "0.7.15", features = ["full"] }
tokio-stream = { version = "0.1.17", features = ["net"] }
tokio-tungstenite = "0.27.0"
tonic = { version = "0.13.0" }
futures-lite = "2.6.0"
# futures = { version = "0.3.31", default-features = false, features = ["alloc"] }
futures = { version = "0.3.31" }
futures-core = "0.3.31"
futures-util = { version = "0.3.31", default-features = false, features = [
    "sink",
    "std",
] }
futures-channel = "0.3.31"

# 序列化
prost = "0.13.5"
prost-types = "0.13.5"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
serde_derive = "1.0.219"
# serde_yaml = { version = "0.9.33" }

# MQ
lapin = { version = "2.5.3", default-features = false, features = ["rustls"] }
deadpool-lapin = "0.12.1"

# 日志
log = "0.4.27"
log4rs = "1.3.0"

tracing-appender = "0.2.3"
tracing-log = { version = "0.2.0" }
tracing = "0.1.41"
env_logger = "0.11.8"
tracing-subscriber = { version = "0.3", features = [
    "env-filter",
    "fmt",
    "time",
] } # tracing 库的订阅者实现
tracing-error = "0.2" # 用于错误跟踪
file-rotate = "0.8.0"


# 网络
# axum = "0.7.9"
axum = { version = "0.8.4", features = [
    "multipart",
    "tokio",
    "original-uri",
    "ws",
] }
axum-extra = { version = "0.10.1", features = ["typed-header"] }
# tower-http = { version = "0.6.2", features = ["cors"] }
tower-http = { version = "0.6.5", features = ["trace", "fs", "cors"] }
http-body-util = "0.1.3"
h2 = "0.4.10"
socket2 = "0.6.0"
reqwest-websocket = "0.5.0"
reqwest = { version = "0.12.19", default-features = false, features = [
    "json",
    "rustls-tls",
    # "native-tls",
    "stream",
] }

# 数据库
sea-orm = { version = "1.1.12", features = [
    "sqlx-mysql",
    "runtime-tokio",
    "macros",
] }
scylla = "0.15.1"
sled = "0.34.7"
redis = { version = "0.32.3", features = [
    "tokio-comp",
    "cluster",
    "cluster-async",
] }
redis_cluster_async = "0.8.1"
r2d2 = "0.8.10"

# 时间
chrono = "0.4.41"
time = "0.3.41"

# 错误处理
anyhow = "1.0.98"

# 加密

# 配置
config = "0.15.6"

# 其他
strum = "0.27.1"
strum_macros = "0.27.1"
async-global-executor = "3.1.0"
once_cell = "1.21.3"
local-ip-address = "0.6.5"
rust_decimal = "1.37.1"
rust_decimal_macros = "1.37.1"
rs-snowflake = "0.6.0"
dashmap = "6.1.0"
rand = "0.9.1"
cached = "0.55.1"
lazy_static = "1.5.0"
async-stream = "0.3.6"
#tokio-rs web framework
tower = { version = "0.5.2", features = ["util", "filter"] }
itertools = "0.14.0"
linked-hash-map = "0.5.6"
thiserror = "2.0.12"
async-recursion = "1.1.1"
rayon = "1.10.0"
notify = "8.0.0"
evalexpr = "12.0.2"
getrandom = "0.3.3"
moka = { version = "0.12.10", features = ["future"] }
num-traits = "0.2.19"
statrs = "0.18.0"
async-channel = "2.5.0"

crc32fast = "1.4.2"
byteorder = "1.5.0"

jsonwebtoken = "9.3.0"
base64 = "0.22.1"

tonic-build = { version = "0.13.0", features = ["prost"] }


teloxide = { version = "0.16.0", default-features = false, features = [
    "rustls",
    "ctrlc_handler",
    "macros",
] }
fjall = { version = "2.11.0" }
# console-subscriber = "0.4.1"
# colored = "3.0.0"
# [workspace.build-dependencies]
# tonic-build = { version = "0.12.3", features = ["prost"] }
