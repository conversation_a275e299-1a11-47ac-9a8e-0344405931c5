// use crate::protoes::heartbeat::{heartbeat_service_client::HeartbeatServiceClient, HeartbeatRequest};
use chrono::Utc;
use common::logclient::log_error;
use protoes::{HeartbeatRequest, heartbeat_service_client::HeartbeatServiceClient};
use std::sync::Arc;
use tokio::sync::{Mutex, broadcast};
use tokio::time::{Duration, interval, timeout};
use tokio_stream::wrappers::ReceiverStream;
use tonic::{Request, Status};
use tracing::*;

#[derive(Clone, Debug)]
pub struct HeartbeatClient {
    client_id: String,
    pub service_name: String,
    is_healthy: tokio::sync::watch::Receiver<bool>,
    business_channel: Arc<Mutex<Option<tonic::transport::Channel>>>,
    shutdown_tx: broadcast::Sender<()>,                             // Add shutdown channel
    background_tasks: Arc<Mutex<Vec<tokio::task::Jo<PERSON><PERSON><PERSON><PERSON><()>>>>, // Track background tasks
}

impl HeartbeatClient {
    /// Initialize HeartbeatClient with background async connection attempts.
    ///
    /// This constructor returns immediately. Connection is handled in the background.
    /// Use `business_channel_if_ready()` to get the business channel if/when available.
    pub async fn new(
        endpoint: &str,
        client_id: String,
        service_name: String,
        send_heartbeat: bool, // 控制是否發送客戶端心跳
    ) -> Self {
        let (shutdown_tx, _) = broadcast::channel(1);
        let business_channel = Arc::new(Mutex::new(None));
        let background_tasks = Arc::new(Mutex::new(Vec::new()));

        let business_channel_clone = business_channel.clone();
        let endpoint_string = endpoint.to_string();
        let shutdown_rx = shutdown_tx.subscribe();
        let service_name_clone = service_name.clone();

        // Initial connection attempt
        if let Ok(ed) = tonic::transport::Channel::from_shared(endpoint_string.clone()) {
            if let Ok(channel) = ed.connect().await {
                let mut guard = business_channel_clone.lock().await;
                *guard = Some(channel.clone());
            }
        }

        // Spawn background connection task
        let tasks = background_tasks.clone();
        let connection_task = tokio::spawn(async move {
            loop {
                // Check for shutdown signal
                if shutdown_rx.resubscribe().try_recv().is_ok() {
                    info!(service = %service_name, "[business] Received shutdown signal, stopping connection task");
                    break;
                }
                if business_channel_clone.lock().await.is_some() {
                    tokio::time::sleep(std::time::Duration::from_secs(2)).await;
                    continue;
                }

                match tonic::transport::Channel::from_shared(endpoint_string.clone()) {
                    Ok(ch) => match ch.connect().await {
                        Ok(channel) => {
                            let mut guard = business_channel_clone.lock().await;
                            *guard = Some(channel.clone());
                            tracing::info!(service = %service_name, "[business] Connected business channel");
                            break;
                        }
                        Err(e) => {
                            // tracing::warn!(service = %service_name,"[business] Failed to connect business channel: {e}, retrying in 2s");
                            tokio::time::sleep(std::time::Duration::from_secs(2)).await;
                        }
                    },
                    Err(e) => {
                        tracing::warn!(service = %service_name,"[business] Invalid endpoint for business channel: {e}, retrying in 2s");
                        log_error(&format!("[business] Invalid endpoint for business channel: {e}, retrying in 2s")).await;
                        tokio::time::sleep(std::time::Duration::from_secs(2)).await;
                    }
                }
            }
        });

        // Store the task
        background_tasks.lock().await.push(connection_task);

        let (is_healthy, client_id_clone, service_name_clone) =
            Self::start_heartbeat_monitor_with_reconnect(endpoint.to_string(), client_id.clone(), service_name_clone, send_heartbeat, shutdown_tx.subscribe(), background_tasks.clone()).await;

        Self {
            client_id: client_id_clone,
            service_name: service_name_clone,
            is_healthy,
            business_channel,
            shutdown_tx,
            background_tasks,
        }
    }

    // Add shutdown method
    pub async fn shutdown(&self) {
        info!("[heartbeat] Initiating HeartbeatClient shutdown for {}...", self.service_name);

        // Send shutdown signal to all background tasks
        let _ = self.shutdown_tx.send(());

        // Wait for all background tasks to complete
        let mut tasks = self.background_tasks.lock().await;
        while let Some(task) = tasks.pop() {
            if let Err(e) = task.await {
                error!("[heartbeat] Task failed during shutdown: {}", e);
            }
        }

        // Clear the business channel
        let mut guard = self.business_channel.lock().await;
        *guard = None;

        info!("[heartbeat] HeartbeatClient shutdown complete for {}", self.service_name);
    }

    /// Get a clone of the business channel if connected, or None if not yet connected
    pub async fn business_channel_if_ready(&self) -> Option<tonic::transport::Channel> {
        let guard = self.business_channel.lock().await;
        guard.as_ref().cloned()
    }

    // Like start_heartbeat_monitor, but takes a channel directly
    async fn _start_heartbeat_monitor_on_channel(channel: tonic::transport::Channel, client_id: String, service_name: String, send_heartbeat: bool) -> (tokio::sync::watch::Receiver<bool>, String, String) {
        let (tx, rx) = tokio::sync::watch::channel(false);

        let client_id_clone = client_id.clone();
        let service_name_clone = service_name.clone();
        tokio::spawn(async move {
            loop {
                info!(service = %service_name, "[heartbeat] Attempting to connect (dedicated channel)");
                let mut client = HeartbeatServiceClient::new(channel.clone());
                debug!(service = %service_name, "[heartbeat] Connection established");
                if Self::monitor_heartbeat(&mut client, client_id.clone(), service_name.clone(), tx.clone(), send_heartbeat).await.is_ok() {
                    debug!(service = %service_name, "[heartbeat] Heartbeat monitor completed successfully");
                }
                warn!(service = %service_name, "[heartbeat] Retrying connection in 2 seconds");
                tokio::time::sleep(Duration::from_secs(2)).await;
            }
        });

        (rx, client_id_clone, service_name_clone)
    }

    /// Like start_heartbeat_monitor_on_channel, but will reconnect the channel if lost
    async fn start_heartbeat_monitor_with_reconnect(
        endpoint: String,
        client_id: String,
        service_name: String,
        send_heartbeat: bool,
        mut shutdown_rx: broadcast::Receiver<()>, // Accept shutdown receiver
        background_tasks: Arc<Mutex<Vec<tokio::task::JoinHandle<()>>>>,
    ) -> (tokio::sync::watch::Receiver<bool>, String, String) {
        let (tx, rx) = tokio::sync::watch::channel(false);
        let client_id_clone = client_id.clone();
        let service_name_clone = service_name.clone();

        let monitor_task = tokio::spawn(async move {
            loop {
                // Check for shutdown signal
                if shutdown_rx.try_recv().is_ok() {
                    info!(service = %service_name, "[heartbeat] Received shutdown signal, stopping heartbeat monitor");
                    let _ = tx.send(false);
                    break;
                }

                // Try to connect the heartbeat channel
                let channel = loop {
                    if shutdown_rx.try_recv().is_ok() {
                        return;
                    }

                    debug!(name = %service_name, "[heartbeat] Attempting to connect,");

                    match tonic::transport::Channel::from_shared(endpoint.clone()) {
                        Ok(ch) => match ch.connect().await {
                            Ok(channel) => {
                                let _ = tx.send(true);
                                tracing::info!(service = %service_name, "[heartbeat] Channel connected, setting healthy state,");
                                break channel;
                            }
                            Err(e) => {
                                let _ = tx.send(false);
                                // tracing::warn!(service = %service_name, "[heartbeat] Failed to connect heartbeat channel: {e}, retrying in 2s");
                                tokio::time::sleep(std::time::Duration::from_secs(2)).await;
                            }
                        },
                        Err(e) => {
                            let _ = tx.send(false);
                            // tracing::warn!(service = %service_name, "[heartbeat] Invalid endpoint: {e}, retrying in 2s");
                            tokio::time::sleep(std::time::Duration::from_secs(2)).await;
                        }
                    }
                };

                if shutdown_rx.try_recv().is_ok() {
                    break;
                }

                let mut client = HeartbeatServiceClient::new(channel.clone());
                tracing::info!(service = %service_name, "[heartbeat] Connected heartbeat channel, starting monitor,");
                if Self::monitor_heartbeat(&mut client, client_id.clone(), service_name.clone(), tx.clone(), send_heartbeat).await.is_ok() {
                    tracing::debug!(service = %service_name, "[heartbeat] Heartbeat monitor completed successfully");
                }
                let _ = tx.send(false);
                tracing::warn!(service = %service_name, "[heartbeat] Heartbeat lost, will reconnect in 2s,");
                tokio::time::sleep(Duration::from_secs(2)).await;
            }
        });

        // Store the monitor task
        background_tasks.lock().await.push(monitor_task);

        (rx, client_id_clone, service_name_clone)
    }

    async fn monitor_heartbeat(client: &mut HeartbeatServiceClient<tonic::transport::Channel>, client_id: String, service_name: String, tx: tokio::sync::watch::Sender<bool>, send_heartbeat: bool) -> Result<(), Status> {
        // 創建客戶端心跳流（即使不發送也要初始化流）
        let (tx_stream, rx_stream) = tokio::sync::mpsc::channel(100);

        // 如果啟用客戶端心跳，發送心跳消息
        if send_heartbeat {
            let client_id_clone = client_id.clone();
            tokio::spawn(async move {
                let mut interval = interval(Duration::from_secs(5));
                // 發送初始心跳以建立流
                if tx_stream
                    .send(HeartbeatRequest {
                        client_id: client_id_clone.clone(),
                        timestamp: Utc::now().timestamp(),
                    })
                    .await
                    .is_err()
                {
                    warn!(client_id = %client_id_clone, "Initial heartbeat send failed");
                    return;
                }

                loop {
                    interval.tick().await;
                    let timestamp = Utc::now().timestamp();
                    debug!(client_id = %client_id_clone, timestamp, "Sending heartbeat");
                    if tx_stream
                        .send(HeartbeatRequest {
                            client_id: client_id_clone.clone(),
                            timestamp,
                        })
                        .await
                        .is_err()
                    {
                        warn!(client_id = %client_id_clone, "Heartbeat send channel closed");
                        break;
                    }
                }
            });
        } else {
            // 發送初始心跳以建立流
            let _ = tx_stream
                .send(HeartbeatRequest {
                    client_id: client_id.clone(),
                    timestamp: Utc::now().timestamp(),
                })
                .await;
        }

        let request = Request::new(ReceiverStream::new(rx_stream));
        let mut server_stream = client.heartbeat_stream(request).await?.into_inner();
        let heartbeat_timeout = Duration::from_secs(10);

        // Note: We don't set healthy here anymore since it's set when channel connects
        // let _ = tx.send(true); // Removed this line as it's now handled earlier

        while let Ok(response) = timeout(heartbeat_timeout, server_stream.message()).await {
            match response {
                Ok(Some(response)) => {
                    debug!(
                        service = %service_name,
                        timestamp = response.timestamp,
                        status = response.status,
                        "Received heartbeat"
                    );
                    // Maintain healthy state
                    let _ = tx.send(true);
                }
                Ok(None) => {
                    warn!(service = %service_name, "Heartbeat stream closed by server");
                    let _ = tx.send(false);
                    return Err(Status::unavailable("Server closed stream"));
                }
                Err(e) => {
                    error!(service = %service_name, "Heartbeat error: {}", e);
                    let _ = tx.send(false);
                    return Err(e);
                }
            }
        }

        error!(service = %service_name, "Heartbeat timeout, server may be down");
        let _ = tx.send(false);
        Err(Status::deadline_exceeded("Heartbeat timeout"))
    }

    pub fn is_healthy(&self) -> bool {
        *self.is_healthy.borrow()
    }

    pub fn client_id(&self) -> &str {
        &self.client_id
    }
}
