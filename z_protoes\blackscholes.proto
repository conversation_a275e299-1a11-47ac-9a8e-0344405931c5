syntax = "proto3";
package blackscholes;

service PhoenixBlackscholes {
  // 查询看涨期权价格
  rpc QueryOptionPriceCall(OptionPriceReq) returns (OptionPriceResp) {}
  // 查询看跌期权价格
  rpc QueryOptionPricePut(OptionPriceReq) returns (OptionPriceResp) {}
  // 查询看涨期权价格
  rpc QueryOptionPriceCallWithVolatility(OptionPriceWithVolatilityReq)
      returns (OptionPriceResp) {}
  // 查询看跌期权价格
  rpc QueryOptionPricePutWithVolatility(OptionPriceWithVolatilityReq)
      returns (OptionPriceResp) {}

  // 期权报价更新通知
  rpc OptionPriceUpdateNotify(PriceUpdateNotifyReq)
      returns (PriceUpdateNotifyResp) {}
  // 期权报价匹配
  rpc OptionPriceMatch(OptionPriceMatchReq) returns (OptionPriceMatchResp) {}

  // 计算波动率
  rpc CalculateVolatility(VolatilityReq) returns (VolatilityResp) {}
}

// 请求信息
message OptionParams {
  string stock_code = 1;     // 股票代码
  double stock_price = 2;    // 股票价格 传0自动获取当前价
  double strike_price = 3;   // 行权价格
  double risk_free_rate = 4; // 无风险连续复利
  int32 days = 5;            // 期权天数
}

message OptionParamsWithVolatility {
  string stock_code = 1;     // 股票代码
  double stock_price = 2;    // 股票价格 传0自动获取当前价
  double strike_price = 3;   // 行权价格
  double risk_free_rate = 4; // 无风险连续复利
  int32 days = 5;            // 期权天数
  double volatility = 6;     // 年化波动率
}

message PriceInfo {
  string stock_code = 1;     // 股票代码
  double stock_price = 2;    // 股票价格 传0自动获取当前价
  double strike_price = 3;   // 行权价格
  double risk_free_rate = 4; // 无风险连续复利
  int32 days = 5;            // 期权天数
  double volatility = 6;     // 年化波动率
  double option_price = 7;
}

message OptionPriceReq { repeated OptionParams req = 1; }

message OptionPriceWithVolatilityReq {
  repeated OptionParamsWithVolatility req = 1;
}

message OptionPriceResp {
  string errMsg = 1;
  int32 errCode = 2;
  repeated PriceInfo price_infos = 3;
}

message PriceUpdateNotifyReq {}

message PriceUpdateNotifyResp {
  string errMsg = 1;
  int32 errCode = 2;
}

message OptionPriceMatchReq {
  string stock_code = 1;      // 股票代码
  int32 market_id = 2;        // 市场
  double inquiry_capital = 3; // 询价申请名义本金
  string option_struct = 4;   // 结构 100%
  string option_cate = 5;     // T+1：1，T+5：5
}

message OptionPriceMatchResp {
  string errMsg = 1;
  int32 errCode = 2;
  double quotation_capital = 3; // 报价名义本金
  repeated OptionData option_data = 4; //
}

message OptionData {
  string structs = 1; // 结构 100% ，90%等
  string months = 2;  // 期限 1M,2M等
  string cates = 3;   // T+1,T+5,两种
  string rates = 4;   //  10.01%
}

message VolatilityReq {
    repeated VolatilityParams req = 1;
}

message VolatilityParams {
    string stock_code = 1;     // 股票代码
    int32 market_id = 2;       // 市场 id 上海： 101，深圳：102，香港：103
    int32 days = 3;            // 期权天数 30， 60， 252
    // int32 profit_type = 4;     // 收益率类型 0：普通，1：对数
}

message VolatilityResp {
    string errMsg = 1;
    int32 errCode = 2;
    repeated VolatilityData resp = 3;
}

message VolatilityData {
    string stock_code = 1;     // 股票代码
    int32 market_id = 2;       // 市场 id 上海： 101，深圳：102，香港：103
    double volatility = 3;     // 普通波动率
    double ln_volatility = 4;  // 对数波动率
    // int32 profit_type = 4;     // 收益率类型 0：普通，1：对数
}