// mod client;
mod config;
mod controller;
mod dataservice;
// mod protofiles;
mod server;
mod service;
use crate::config::settings::Settings;
use anyhow::Result;
use common::logclient::*;
use heartbeat_common::HeartbeatServiceImpl;
use server::ServerHandler;
// use utility::loggings;
use tracing::*;

use protoes::{heartbeat_service_server::HeartbeatServiceServer, phoenixsatcenter::sat_center_server::SatCenterServer};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // let cfg = "config/satcenter.yaml";
    // loggings::log_init(cfg);
    let prefix = "phoenix_satcenter";
    let dir = "./log";

    let settings = Settings::new().expect("init configurtion error");
    // let level = "INFO";
    let level = &settings.system.loglevel.to_ascii_uppercase();
    let _guard = common::init_tracing(prefix, dir, level);
    info!("初始化配置信息:{:?}", &settings);

    init_logclient(&settings.servers.logcenterserver, &format!("{}_{prefix}", &settings.notification.vhost)).await;
    // init_logclient(&settings.servers.logcenterserver, "phoenix_satcenter").await;

    let server = prepare(&settings).await.expect("Init server error......");
    info!(
        "Server started on {}:{} name: {} version: {} description: {}",
        settings.application.apphost,
        settings.application.appport,
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    );
    log_debug(&format!(
        "Server started on {}:{} name: {} version: {} description: {}",
        settings.application.apphost,
        settings.application.appport,
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    ))
    .await;

    server_run(server, &settings).await
}

async fn prepare(settings: &Settings) -> anyhow::Result<ServerHandler> {
    let grpc = ServerHandler::new(&settings).await;

    Ok(grpc)
}

async fn server_run(mut svr: ServerHandler, settings: &Settings) -> Result<(), Box<dyn std::error::Error>> {
    let addr = format!("{}:{}", settings.application.apphost, settings.application.appport).parse().unwrap();
    // info!("Starting  service on {}", addr);
    // info!("name: {} version: {} description: {}", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"), env!("CARGO_PKG_DESCRIPTION"));
    // log_debug(&format!(
    //     "server started, name: {} version: {} description: {}",
    //     env!("CARGO_PKG_NAME"),
    //     env!("CARGO_PKG_VERSION"),
    //     env!("CARGO_PKG_DESCRIPTION")
    // ))
    // .await;

    let accountriskclient = svr.accountriskclient.clone();
    //receive ctrl-c exit signal
    let (tx, rx) = tokio::sync::oneshot::channel::<()>();
    let on_leave = svr.on_leave();

    // Get shutdown sender for background tasks and controller for graceful shutdown
    let shutdown_tx = svr.get_shutdown_sender();
    let controller = svr.get_controller().clone();

    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.ok();
        info!("Ctrl-c received, shutting down server...");

        // Gracefully shutdown ServerController (including AkaClient)
        if let Err(e) = controller.shutdown().await {
            error!("Error during controller shutdown: {:?}", e);
            log_error(&format!("Error during controller shutdown: {:?}", e)).await;
        }
        info!("服务器控制器已经关闭......");

        // Stop AccountRiskClient
        accountriskclient.shutdown().await;

        info!("账户风控中心客户端已经关闭......");

        // Stop all ServerHandler background tasks
        let _ = shutdown_tx.send(());
        info!("服务器后台任务已经关闭......");

        // Give a shorter delay for faster shutdown
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;

        info!("Sending shutdown signal to gRPC server");
        tx.send(()).ok();

        info!("Shutdown signal sent, waiting for gRPC server to stop...");

        // Force exit after 10 seconds if gRPC server doesn't respond
        tokio::spawn(async move {
            tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;
            warn!("Force exit: gRPC server didn't respond to shutdown within 10 seconds");
            std::process::exit(0);
        });
    });

    let hearbeat_service = HeartbeatServiceImpl::default();

    tonic::transport::Server::builder()
        .add_service(SatCenterServer::new(svr))
        .add_service(HeartbeatServiceServer::new(hearbeat_service))
        .serve_with_shutdown(addr, async {
            rx.await.ok();
        })
        .await?;

    info!("server has stopped, starting final cleanup");
    // Perform final cleanup
    let _ = on_leave.leave().await;

    info!("Server shutted down");
    Ok(())
}
