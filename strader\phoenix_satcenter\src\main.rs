// mod client;
mod config;
mod controller;
mod dataservice;
// mod protofiles;
mod server;
mod service;
use crate::config::settings::Settings;
use anyhow::Result;

use common::logclient::*;
use server::ServerHandler;
// use utility::loggings;
use tracing::*;

use protoes::phoenixsatcenter::sat_center_server::SatCenterServer;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // let cfg = "config/satcenter.yaml";
    // loggings::log_init(cfg);
    let prefix = "phoenix_satcenter";
    let dir = "./log";

    let settings = Settings::new().expect("init configurtion error");
    // let level = "INFO";
    let level = &settings.system.loglevel.to_ascii_uppercase();
    let _guard = common::init_tracing(prefix, dir, level);
    info!("初始化配置信息:{:?}", &settings);

    init_logclient(&settings.servers.logcenterserver, &format!("{}_{prefix}", &settings.notification.vhost)).await;
    // init_logclient(&settings.servers.logcenterserver, "phoenix_satcenter").await;

    let server = prepare(&settings).await.expect("Init server error......");
    info!(
        "Server started on {}:{} name: {} version: {} description: {}",
        settings.application.apphost,
        settings.application.appport,
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    );
    log_debug(&format!(
        "Server started on {}:{} name: {} version: {} description: {}",
        settings.application.apphost,
        settings.application.appport,
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    ))
    .await;

    server_run(server, &settings).await
}

async fn prepare(settings: &Settings) -> anyhow::Result<ServerHandler> {
    let grpc = ServerHandler::new(&settings).await;

    Ok(grpc)
}

async fn server_run(mut svr: ServerHandler, settings: &Settings) -> Result<(), Box<dyn std::error::Error>> {
    let addr = format!("{}:{}", settings.application.apphost, settings.application.appport).parse().unwrap();
    // info!("Starting  service on {}", addr);
    // info!("name: {} version: {} description: {}", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"), env!("CARGO_PKG_DESCRIPTION"));
    // log_debug(&format!(
    //     "server started, name: {} version: {} description: {}",
    //     env!("CARGO_PKG_NAME"),
    //     env!("CARGO_PKG_VERSION"),
    //     env!("CARGO_PKG_DESCRIPTION")
    // ))
    // .await;

    //receive ctrl-c exit signal
    let (tx, rx) = tokio::sync::oneshot::channel::<()>();
    let on_leave = svr.on_leave();
    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.ok();
        info!("Ctrl-c received, shutting down");
        tx.send(()).ok();
    });

    tonic::transport::Server::builder()
        .add_service(SatCenterServer::new(svr))
        .serve_with_shutdown(addr, async {
            rx.await.ok();
        })
        .await
        .expect("build server error");

    info!("Shutted down, wait for final clear");
    on_leave.leave().await;
    info!("Shutted down");
    Ok(())
}
