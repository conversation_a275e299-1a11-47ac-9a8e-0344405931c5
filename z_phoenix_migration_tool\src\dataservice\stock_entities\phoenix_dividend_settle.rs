//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_dividend_settle")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub settle_id: i64,
    pub sys_date: i32,
    pub unit_id: i32,
    pub busin_flag: i32,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub occur_capital: Decimal,
    pub exchange_id: i32,
    pub deal_amount: i32,
    pub status: i32,
    pub stock_code: String,
    #[sea_orm(column_type = "Decimal(Some((16, 4)))")]
    pub taxation: Decimal,
    pub dividend_no: i64,
    pub currency_no: String,
    pub channel_account: i32,
    pub create_time: i64,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
