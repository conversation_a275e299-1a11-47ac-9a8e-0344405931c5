use akaclient::akaclient::{AkaCacheOption, AkaClient};
use protoes::phoenixakacenter::StockChannelReq;
// use protoes::StockChannelReq;
use std::time::Duration;
use tokio::time::sleep;
use tracing::*;
use utility::loggings;

//52.131.220.224
//40.72.97.246
const AKASERVER_URL: &str = "http://52.131.220.224:8402";
// const AKASERVER_URL: &str = "http://40.72.97.246:8402";
// const AKASERVER_URL: &str = "http://127.0.0.1:8020";
// uat-stock-data.chinaeast2.cloudapp.chinacloudapi.cn
// dev-data.chinaeast2.cloudapp.chinacloudapi.cn
const MQ_URI: &str = "amqp://pp:<EMAIL>:5672/%2f";

#[tokio::test]
async fn test_akaclient_nocache() {
    let cfg = "config/log.yaml";
    loggings::log_init(cfg);

    let opt = AkaCacheOption::default();
    let aka = AkaClient::init(AKASERVER_URL.to_string(), &opt).await;
    // let res = aka.query_trade_date(0, 0, 1, 1).await;
    // if res.as_ref().is_err() {
    //   error!("ailed:{:?}", res.unwrap_err());
    // } else {
    //   info!("query_trade_date {:#?}", res.unwrap());
    // }
    let res = aka.query_account_info(605078).await;
    if res.as_ref().is_err() {
        info!("ailed:{:?}", res.unwrap_err());
    } else {
        info!("query_exchange_rate 1 {:#?}", res.unwrap());
    }
    let res = aka.query_exchange_rate("CNY", "CNH").await;
    if res.as_ref().is_err() {
        info!("ailed:{:?}", res.unwrap_err());
    } else {
        info!("query_exchange_rate 2 {:#?}", res.unwrap());
    }
    let res = aka.query_exchange_rate("CNY", "CNY").await;
    if res.as_ref().is_err() {
        info!("ailed:{:?}", res.unwrap_err());
    } else {
        info!("query_exchange_rate 2 {:#?}", res.unwrap());
    }
}

#[tokio::test]
async fn test_akaclient() {
    let cfg = "config/log.yaml";
    loggings::log_init(cfg);

    let opt = AkaCacheOption {
        use_cache: true,
        mq_uri: MQ_URI.to_string(),
        exchange: "notification_center".to_owned(),
        routing_keys: "notification.aka.#".to_owned(),
    };
    let aka = AkaClient::init(AKASERVER_URL.to_string(), &opt).await;

    let aka_ = aka.clone();
    tokio::spawn(async move {
        loop {
            let req = StockChannelReq {
                user_id: 201009,
                stock_id: 10312,
                order_direction: 2,
            };

            let res = aka_.query_stock_channel(req).await;
            if res.as_ref().is_err() {
                info!("failed:{:?}", res.unwrap_err());
            } else {
                info!("query_trade_date {:#?}", res.unwrap());
            }

            tokio::time::sleep(Duration::from_millis(200)).await;
        }
    });

    tokio::spawn(async move {
        loop {
            let res = aka.query_exchange_rate("USD", "CNH").await;
            if res.as_ref().is_err() {
                info!("failed:{:?}", res.unwrap_err());
            } else {
                info!("query_trade_date {:#?}", res.unwrap());
            }

            tokio::time::sleep(Duration::from_millis(100)).await;
        }
    });

    loop {
        tokio::time::sleep(Duration::from_secs(5)).await;
    }
}

#[tokio::test(flavor = "multi_thread")]
async fn test_reloadcache() {
    let cfg = "config/log.yaml";
    loggings::log_init(cfg);

    let opt = AkaCacheOption {
        use_cache: true,
        mq_uri: MQ_URI.to_string(),
        exchange: "notification_center".to_owned(),
        routing_keys: "notification.aka.#".to_owned(),
    };
    let aka = AkaClient::init(AKASERVER_URL.to_string(), &opt).await;
    let res = aka.query_exchange_rate("USD", "CNH").await;
    if res.as_ref().is_err() {
        info!("ailed:{:?}", res.unwrap_err());
    } else {
        info!("query_exchange_rate 1 {:#?}", res.unwrap());
    }
    sleep(std::time::Duration::from_secs(30)).await;
    let res = aka.query_exchange_rate("USD", "CNH").await;
    if res.as_ref().is_err() {
        info!("ailed:{:?}", res.unwrap_err());
    } else {
        info!("query_exchange_rate 2 {:#?}", res.unwrap());
    }

    info!("test done");
}

#[tokio::test]
async fn test_query_exchange_rate_noccahe() {
    let cfg = "config/log.yaml";
    loggings::log_init(cfg);

    let opt = AkaCacheOption::default();
    let aka = AkaClient::init(AKASERVER_URL.to_string(), &opt).await;
    let res = aka.query_exchange_rate("USD", "CNH").await;
    if res.as_ref().is_err() {
        info!("ailed:{:?}", res.unwrap_err());
    } else {
        info!("query_exchange_rate 1 {:#?}", res.unwrap());
    }
    let res = aka.query_exchange_rate("USD", "CNH").await;
    if res.as_ref().is_err() {
        info!("ailed:{:?}", res.unwrap_err());
    } else {
        info!("query_exchange_rate 2 {:#?}", res.unwrap());
    }

    info!("test done");
}

#[tokio::test]
async fn test_query_stock_trade_time() {
    let cfg = "config/log.yaml";
    loggings::log_init(cfg);
    let opt = AkaCacheOption {
        use_cache: true,
        mq_uri: MQ_URI.to_string(),
        exchange: "notification_center".to_owned(),
        routing_keys: "notification.aka.#".to_owned(),
    };
    let aka = AkaClient::init(AKASERVER_URL.to_string(), &opt).await;
    let res = aka.query_stock_trade_time(42410, 1).await;
    if res.as_ref().is_err() {
        info!("ailed:{:?}", res.unwrap_err());
    } else {
        info!("query_stock_trade_time 1 {:#?}", res.unwrap());
    }

    let res = aka.query_stock_trade_time(42410, 2).await;
    if res.as_ref().is_err() {
        info!("ailed:{:?}", res.unwrap_err());
    } else {
        info!("query_stock_trade_time 2 {:#?}", res.unwrap());
    }
    ///////////////////////运营中心修改交易时间////////////////////////////
    sleep(std::time::Duration::from_secs(30)).await;
    let res = aka.query_stock_trade_time(42410, 1).await;
    if res.as_ref().is_err() {
        info!("ailed:{:?}", res.unwrap_err());
    } else {
        info!("query_stock_trade_time 1 {:#?}", res.unwrap());
    }

    let res = aka.query_stock_trade_time(42410, 2).await;
    if res.as_ref().is_err() {
        info!("ailed:{:?}", res.unwrap_err());
    } else {
        info!("query_stock_trade_time 2 {:#?}", res.unwrap());
    }
    info!("test done");
}

#[tokio::test]
async fn test_query_stock_trade_time_noccahe() {
    let cfg = "config/log.yaml";
    loggings::log_init(cfg);

    let opt = AkaCacheOption::default();
    let aka = AkaClient::init(AKASERVER_URL.to_string(), &opt).await;
    let res = aka.query_stock_trade_time(42410, 1).await;
    if res.as_ref().is_err() {
        info!("ailed:{:?}", res.unwrap_err());
    } else {
        info!("query_stock_trade_time 1 {:#?}", res.unwrap());
    }

    let res = aka.query_stock_trade_time(42410, 2).await;
    if res.as_ref().is_err() {
        info!("ailed:{:?}", res.unwrap_err());
    } else {
        info!("query_stock_trade_time 2 {:#?}", res.unwrap());
    }
    ///////////////////////运营中心修改交易时间////////////////////////////
    sleep(std::time::Duration::from_secs(30)).await;
    let res = aka.query_stock_trade_time(42410, 1).await;
    if res.as_ref().is_err() {
        info!("ailed:{:?}", res.unwrap_err());
    } else {
        info!("query_stock_trade_time 1 {:#?}", res.unwrap());
    }

    let res = aka.query_stock_trade_time(42410, 2).await;
    if res.as_ref().is_err() {
        info!("ailed:{:?}", res.unwrap_err());
    } else {
        info!("query_stock_trade_time 2 {:#?}", res.unwrap());
    }
    info!("test done");
}
