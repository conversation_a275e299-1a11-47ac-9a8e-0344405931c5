[package]
name = "phoenix_satcenter"
version = "0.2.0"
edition = "2021"
description = "融券中心 - build time: 2025-07-07"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
utility = { workspace = true }
common = { workspace = true }
messagecenter = { workspace = true }
akaclient = { workspace = true }
protoes = { workspace = true }

accountriskcenterclient = { workspace = true }

config = { workspace = true }

serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
futures = { workspace = true }
lapin = { workspace = true }
tonic = { workspace = true }
prost = { workspace = true }
notify = { workspace = true }
rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }
sea-orm = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
chrono = { workspace = true }
tracing = { workspace = true }
# log = { workspace = true }

# [build-dependencies]
# # tonic-build = { workspace = true, features = ["prost"] }
# tonic-build.workspace = true


# [[bin]]
# name = "test"
# path = "src/test.rs"
