//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_level_record_copy1")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub user_id: i64,
    pub create_date: i64,
    pub business_type: i8,
    pub account_no: String,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub lever_rate: Decimal,
    #[sea_orm(column_type = "Decimal(Some((12, 6)))")]
    pub year_rate: Decimal,
    pub level_rate_text: String,
    pub effect_month: i32,
    pub effect_month_text: String,
    pub level_rate_key: i64,
    pub effect_month_key: i64,
    #[sea_orm(column_type = "Decimal(Some((12, 6)))")]
    pub close_line: Decimal,
    #[sea_orm(column_type = "Decimal(Some((12, 6)))")]
    pub warn_line: Decimal,
    pub apply_date: i64,
    pub check_date: i64,
    pub expire_date: i64,
    pub notify_state: i8,
    pub apply_id: i64,
    pub settle_type: i8,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub real_cash: Decimal,
    pub stop_date: i64,
    pub stop_sign_date: i64,
    pub renewal_type: i8,
    pub expire_remind: i8,
    pub mofiy_date: i64,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
