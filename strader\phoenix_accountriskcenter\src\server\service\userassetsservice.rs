use manageclient::Manageclient;
use std::collections::HashMap;
use std::sync::Arc;
use tracing::*;

use crate::dataservice::entities::prelude::PhoenixRiskDetails;
use crate::dataview::{userassets::UserAssetsData, userposition::UserPositionData};
use crate::server::controller::PersistData;
use akaclient::akaclient::AkaClient;
use anyhow::{anyhow, Result};
use chrono::{Local, Timelike};
use common::constant;
use common::constant::StockType;
use itertools::Itertools;
use protoes::assetscenter::PhoenixassetsResultInfo;
use protoes::managerunit::ModifyUnitReq;
use protoes::phoenixaccountriskcenter::{PhoenixUserAssets, UserAssetsReq, UserAssetsResp};
// use dashmap::{DashMap, Map};
use common::logclient::LOGCLIENT;
use protoes::phoenixnotification::AccountInfoChange;
use rust_decimal::Decimal;
use tokio::sync::{mpsc, RwLock};
// use messagecenter::protofiles::phoenixnotification::NotificationAsset;
use crate::dataservice::entities::prelude::*;
use crate::dataview::userassets::UserData;
use dbconnection::DbConnection;
use utility::timeutil::{self, current_timestamp};

pub struct UserAssetsService {
    user_assets_data: Arc<RwLock<HashMap<i64, UserData>>>,
    ids: Arc<RwLock<HashMap<i64, i64>>>,
}

impl UserAssetsService {
    pub fn new() -> Self {
        UserAssetsService {
            user_assets_data: Arc::new(RwLock::new(HashMap::new())),
            ids: Arc::new(Default::default()),
        }
    }
    //初始化数据
    pub async fn init(&self, ret_data: &Vec<(PhoenixassetsResultInfo, i64)>, akasvc: &AkaClient) -> Result<()> {
        self.user_assets_data.write().await.clear();
        self.ids.write().await.clear();
        if ret_data.len() <= 0 {
            return Err(anyhow!("Empty data"));
        }
        let mut assets_data: Vec<UserAssetsData> = ret_data
            .into_iter()
            .map(|(f, user_id)| UserAssetsData::convert_assetsinfo_to_assetsdata(f, *user_id, current_timestamp()))
            .collect();

        for val in assets_data.iter_mut() {
            let ret = self.set_unit_assets(val, akasvc).await;
            if ret.as_ref().is_err() {
                error!("query account by id:{} error:{}", val.user_id, ret.as_ref().unwrap_err().to_string());
                let log_client = LOGCLIENT.get();
                if log_client.is_some() {
                    log_client
                        .unwrap()
                        .push_error(&format!("query account by id:{} error:{}", val.user_id, ret.as_ref().unwrap_err().to_string()))
                        .await;
                }
                continue;
            }
        }
        // info!("初始化用户{}条", ret_data.len());
        Ok(())
    }
    //rpc接口查询资产
    pub async fn query_assets(&self, req: &UserAssetsReq) -> Result<UserAssetsResp> {
        let f_data = |asset_data: &UserAssetsData| -> PhoenixUserAssets {
            let mut data = PhoenixUserAssets { ..Default::default() };
            data.unit_id = asset_data.unit_id;
            data.user_id = asset_data.user_id;
            data.total_position_value = asset_data.position_value;
            data.gem_position_value = asset_data.position_value_cyb;
            data.real_margin = asset_data.margin_use;
            data.real_cash = asset_data.current_cash;
            data.risk_rate = asset_data.risk_rate;

            data.net_income = asset_data.total_deposit - asset_data.total_withdraw;
            data.hold_yk = asset_data.hold_yk;
            data.total_asset = asset_data.current_cash + asset_data.hold_yk - asset_data.frozen_capital;
            data.available_cash = data.total_asset - data.real_margin - asset_data.trade_frozen_capital;
            data.draw_frozen = asset_data.frozen_capital;
            data.lastday_frozen_capital = asset_data.last_frozen_capital;
            data.trade_frozen_capital = asset_data.trade_frozen_capital;
            data.today_yk = data.total_asset - asset_data.last_cash - asset_data.today_deposit + asset_data.today_withdraw + asset_data.frozen_capital - asset_data.last_frozen_capital - asset_data.transfer_cash;
            data.total_yk = data.total_asset - asset_data.total_deposit + asset_data.total_withdraw + asset_data.frozen_capital - asset_data.total_transfer_cash;
            data.lastday_cash = asset_data.last_cash;
            data.today_deposit = asset_data.today_deposit;
            data.today_withdrawal = asset_data.today_withdraw;
            data.gem_trade_frozen_capital = asset_data.trade_frozen_capital_cyb;
            data.gem_margin_frozen_capital = asset_data.margin_use_cyb;
            data.total_deposit = asset_data.total_deposit;
            data.total_withdrawal = asset_data.total_withdraw;
            data.currency = asset_data.currency_no.clone();
            data.transfer_cash = asset_data.transfer_cash;
            data.total_transfer_cash = asset_data.total_transfer_cash;
            data
        };

        let mut assets_vec = vec![];

        if req.user_id.eq(&vec![0]) {
            self.user_assets_data.read().await.iter().for_each(|(_, user_data)| {
                user_data.asset_vec.iter().for_each(|asset_data| {
                    let mut data = f_data(&asset_data);
                    data.trade_state = user_data.trade_flag;
                    data.warning_line = user_data.warn_line;
                    data.level_num = user_data.credit_multiple;
                    data.close_line = user_data.close_line;
                    assets_vec.push(data);
                })
            });

            return Ok(UserAssetsResp { assets: assets_vec, ..Default::default() });
        }

        if req.user_id.is_empty() {
            if req.unit_id.is_empty() {
                error!("unit_id and user_id are empty");
                return Err(anyhow!("unit_id and user_id are empty"));
            }

            for id in req.unit_id.iter() {
                if let Some((user_data, asset_data)) = self.user_assets_data.read().await.iter().find_map(|(_, user_data)| {
                    if let Some(asset_data) = user_data.asset_vec.iter().find(|asset_data| asset_data.unit_id == *id) {
                        Some((user_data, asset_data))
                    } else {
                        None
                    }
                }) {
                    let mut data = f_data(&asset_data);
                    data.trade_state = user_data.trade_flag;
                    data.warning_line = user_data.warn_line;
                    data.level_num = user_data.credit_multiple;
                    data.close_line = user_data.close_line;
                    assets_vec.push(data);
                }
            }
        } else {
            for id in req.user_id.iter() {
                if let Some(user_data) = self.user_assets_data.read().await.get(&id) {
                    user_data.asset_vec.iter().for_each(|asset_data| {
                        let mut data = f_data(&asset_data);
                        data.trade_state = user_data.trade_flag;
                        data.warning_line = user_data.warn_line;
                        data.level_num = user_data.credit_multiple;
                        data.close_line = user_data.close_line;
                        assets_vec.push(data);
                    })
                }
            }
        }

        return Ok(UserAssetsResp { assets: assets_vec, ..Default::default() });
    }

    //每日定时持久化用户风控数据，用于查询历史风控数据使用
    pub async fn interval_save_unit_risk_data(&self, sysdate: i32, db: &DbConnection) -> Result<()> {
        let ret = self.query_assets(&UserAssetsReq { unit_id: vec![0], user_id: vec![0] }).await;
        if let Err(err) = ret {
            error!("查询风控数据异常,{:?}", err);
            return Err(anyhow!("interval_save_unit_risk_data error"));
        }

        let ret_data = ret.unwrap();
        let ret_v = ret_data.assets;

        let mut db_vec = Vec::new();
        for i in ret_v.iter() {
            let item = PhoenixUserAssetsHis {
                sys_date: sysdate,
                unit_id: i.unit_id,
                total_asset: Decimal::from_f64_retain(i.total_asset).unwrap_or_default(),
                total_position_value: Decimal::from_f64_retain(i.total_position_value).unwrap_or_default(),
                gem_position_value: Decimal::from_f64_retain(i.gem_position_value).unwrap_or_default(),
                real_margin: Decimal::from_f64_retain(i.real_margin).unwrap_or_default(),
                real_cash: Decimal::from_f64_retain(i.real_cash).unwrap_or_default(),
                risk_rate: Decimal::from_f64_retain(i.risk_rate).unwrap_or_default(),
                available_cash: Decimal::from_f64_retain(i.available_cash).unwrap_or_default(),
                net_income: Decimal::from_f64_retain(i.net_income).unwrap_or_default(),
                hold_yk: Decimal::from_f64_retain(i.hold_yk).unwrap_or_default(),
                today_yk: Decimal::from_f64_retain(i.today_yk).unwrap_or_default(),
                total_yk: Decimal::from_f64_retain(i.total_yk).unwrap_or_default(),
                draw_frozen: Decimal::from_f64_retain(i.draw_frozen).unwrap_or_default(),
                gem_margin_frozen_capital: Decimal::from_f64_retain(i.gem_margin_frozen_capital).unwrap_or_default(),
                gem_trade_frozen_capital: Decimal::from_f64_retain(i.gem_trade_frozen_capital).unwrap_or_default(),
                lastday_cash: Decimal::from_f64_retain(i.lastday_cash).unwrap_or_default(),
                today_deposit: Decimal::from_f64_retain(i.today_deposit).unwrap_or_default(),
                today_withdrawal: Decimal::from_f64_retain(i.today_withdrawal).unwrap_or_default(),
                frozencash: Decimal::from_f64_retain(i.frozencash).unwrap_or_default(),
                trade_frozen_capital: Decimal::from_f64_retain(i.trade_frozen_capital).unwrap_or_default(),
                close_line: Decimal::from_f64_retain(i.close_line).unwrap_or_default(),
                warning_line: Decimal::from_f64_retain(i.warning_line).unwrap_or_default(),
                level_num: Decimal::from_f64_retain(i.level_num).unwrap_or_default(),
                trade_state: i.trade_state,
                id: 0,
                did: 0,
                user_id: i.user_id,
            };
            db_vec.push(item);
        }

        // info!("持久化风控数据：{}条", db_vec.len());
        PhoenixUserAssetsHis::delete_many(sysdate, db).await?;
        let ret = PhoenixUserAssetsHis::insert_many(&db_vec, db).await;
        if let Err(err) = ret {
            error!("PhoenixUserAssetsHis::insert_many异常,{:?}", err);
            return Err(anyhow!("interval_save_unit_risk_data error"));
        }
        Ok(())
    }

    //更新用户资产数据
    // pub async fn update_user_assets_by_resultinfo(&self, assets: &PhoenixassetsResultInfo, akasvc: &AkaClient) -> Result<()> {
    //   let userassetsdata = UserAssetsData::convert_assetsinfo_to_assetsdata(assets, current_timestamp());
    //   let ret = self.set_unit_assets(&userassetsdata, akasvc).await;
    //   if ret.as_ref().is_err() {
    //     return Err(anyhow!("set unit assets error"));
    //   }
    //   let positions: Vec<UserPositionData> = Vec::new();
    //   // let rateinfo = akasvc.query_exchange_rate(&userassetsdata.currency_no).await;
    //   let ret = UserAssetsService::compute_user_assets(&userassetsdata, &positions);
    //   if ret.is_err() {
    //     return Err(anyhow!("compute_user_assets error:{}", ret.as_ref().unwrap_err().to_string()));
    //   }
    //   Ok(())
    // }

    //更新用户资产数据
    pub async fn update_user_assets(&self, assets: &UserAssetsData, akasvc: &AkaClient) -> Result<()> {
        let ret = self.set_unit_assets(assets, akasvc).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!("set unit assets error"));
        }
        Ok(())
    }
    pub async fn get_user_total_assets(&self, total_account: i64) -> PhoenixAccountAssets {
        // info!("start to get user total assets");
        let mut account_total = PhoenixAccountAssets {
            id: 0,
            p_account_id: total_account,
            p_account_type: constant::AccountType::TotalAccount as i32,
            // p_date: current_date(),
            p_updatetime: current_timestamp() as i64,
            // p_lastdate: currency_rate(),
            ..Default::default()
        };

        self.user_assets_data.read().await.iter().for_each(|(_, asset_vec)| {
            asset_vec.asset_vec.iter().for_each(|asset_data| {
                // let total_capital = val.current_cash + val.position_value; //总资产=风控总资产+持仓市值
                // let current_cash = total_capital - val.en_credit_cash; //当前本金
                let mut current_borrowed = Decimal::from_f64_retain(asset_data.position_value - asset_data.current_cash).unwrap_or_default(); //已借金额
                if current_borrowed < Decimal::from(0) {
                    current_borrowed = Decimal::from(0);
                }
                account_total.p_real_profit += current_borrowed; //已借金额保存在"实际盈亏"字段
                account_total.p_current_principal += Decimal::from_f64_retain(asset_data.current_cash).unwrap_or_default();
                // account_total.p_credit_cash += Decimal::from_f64_retain(asset_data.en_credit_cash).unwrap_or_default();
                account_total.p_position_value += Decimal::from_f64_retain(asset_data.position_value).unwrap_or_default();
                account_total.p_position_value_star += Decimal::from_f64_retain(asset_data.position_value_cyb).unwrap_or_default();
                account_total.p_prebuy_capital_star += Decimal::from_f64_retain(asset_data.margin_use_cyb + asset_data.trade_frozen_capital_cyb).unwrap_or_default(); //创业板维持保证金保存在"p_prebuy_capital_star"字段
                account_total.p_financing_occupied += Decimal::from_f64_retain(asset_data.margin_use + asset_data.trade_frozen_capital).unwrap_or_default(); //维持保证金保存在"保证金占用"字段

                // account_total.p_prebuy_capital_star += Decimal::from_f64_retain(asset_data.st_prebuy_value_star).unwrap_or_default();

                // account_total.p_financing_borrowed += current_borrowed;
                account_total.p_floating_profit += Decimal::from_f64_retain(asset_data.hold_yk).unwrap_or_default();
                // account_total.p_current_financial = account_total.p_current_principal;
            })
        });

        // info!("summarized total account: {:#?}", &account_total);
        account_total
    }

    // pub async fn get_total_account_assets(&self, accountid: i64) -> UserAssetsData {
    //   let mut ret = UserAssetsData { ..Default::default() };
    //   for assets in self.user_assets_data.read().await.values() {
    //     if assets.unit_id == accountid {
    //       continue;
    //     }
    //     ret.begin_cash += assets.begin_cash;
    //     ret.cash_in_transit += assets.cash_in_transit;
    //     ret.current_cash += assets.current_cash;
    //     ret.frozen_capital += assets.frozen_capital;
    //     ret.hold_yk += assets.hold_yk;
    //     ret.last_cash += assets.last_cash;
    //     ret.today_deposit += assets.today_deposit;
    //     ret.today_withdraw += assets.today_withdraw;
    //     ret.total_deposit += assets.today_deposit;
    //     ret.total_withdraw += assets.total_withdraw;
    //     ret.trade_frozen_capital += assets.trade_frozen_capital;
    //     ret.position_value += assets.position_value;
    //     ret.position_value_cyb += assets.position_value_cyb;
    //     ret.margin_use += assets.margin_use;
    //   }
    //   ret.unit_id = accountid;
    //   return ret;
    // }

    async fn set_unit_assets(&self, val: &UserAssetsData, akasvc: &AkaClient) -> Result<()> {
        let mut flag = false;
        {
            let rd = self.user_assets_data.read().await;
            if rd.contains_key(&val.user_id) {
                flag = true;
            }
        }

        if !flag {
            //如果不存在
            let val = val.to_owned();
            let ret = akasvc.query_account_info(val.user_id).await;
            if ret.as_ref().is_err() {
                return Err(anyhow!("query account by id:{} error:{}", val.user_id, ret.as_ref().unwrap_err().to_string()));
            }
            let account_info = ret.unwrap();
            // info!("akasvc.query_account_info返回{:?}", account_info);
            // info!("set_unit_assets{}-----{:?}", val.unit_id, val);
            self.ids.write().await.insert(val.unit_id, val.user_id);

            self.user_assets_data.write().await.insert(
                val.user_id,
                UserData {
                    user_id: val.user_id,
                    credit_multiple: account_info.level_rate.parse::<f64>().unwrap_or_default(),
                    warn_line: account_info.warning_line,
                    close_line: account_info.close_line,
                    trade_flag: account_info.trade_state as i32,
                    risk_trigger_state: 0,
                    asset_vec: vec![val],
                },
            );
        } else {
            // info!("set_unit_assets{}-----{:?}", val.unit_id, val);
            self.user_assets_data.write().await.entry(val.user_id).and_modify(|f| {
                if let Some(data) = f.asset_vec.iter_mut().find(|data| data.unit_id == val.unit_id) {
                    data.begin_cash = val.begin_cash;
                    data.current_cash = val.current_cash;
                    data.frozen_capital = val.frozen_capital;
                    data.trade_frozen_capital = val.trade_frozen_capital;
                    data.cash_in_transit = val.cash_in_transit;
                    // data.credit_multiple = val.credit_multiple;
                    data.currency_no = val.currency_no.to_owned();
                    data.last_time = val.last_time;
                    data.total_deposit = val.total_deposit;
                    data.today_deposit = val.today_deposit;
                    data.today_withdraw = val.today_withdraw;
                    data.total_withdraw = val.total_withdraw;
                    data.last_cash = val.last_cash;
                    data.trade_frozen_capital_cyb = val.trade_frozen_capital_cyb;
                    data.transfer_cash = val.transfer_cash;
                    data.total_transfer_cash = val.total_transfer_cash;
                } else {
                    f.asset_vec.push(val.to_owned());
                }
            });
        }
        Ok(())
    }

    pub async fn query_user_id_by_unit_id(&self, unit_id: i64) -> Option<i64> {
        self.ids.read().await.get(&unit_id).map(|id| *id)
    }

    pub async fn query_currency_by_unit_id(&self, unit_id: i64) -> Option<String> {
        let user_id = self.ids.read().await.get(&unit_id).map(|id| *id)?;
        self.user_assets_data
            .read()
            .await
            .get(&user_id)?
            .asset_vec
            .iter()
            .find_map(|asset_data| if asset_data.unit_id == unit_id { Some(asset_data.currency_no.clone()) } else { None })
    }

    //获取所有资产用户id
    pub async fn get_all_unit_id(&self) -> Vec<(i64, i64)> {
        self.ids.read().await.iter().map(|(unit_id, user_id)| (*unit_id, *user_id)).collect_vec()
    }
    //获取用户的杠杠倍数
    pub async fn get_user_credit_multiple(&self, user_id: i64) -> Result<f64> {
        let assets = self.user_assets_data.read().await;
        if !assets.contains_key(&user_id) {
            return Err(anyhow!("用户不存在!"));
        }
        Ok(assets.get(&user_id).unwrap().credit_multiple)
    }

    //更新用户的杠杠倍数
    // pub async fn set_unit_credit_multiple(&self, unit_id: i64, credit_multiple: f64) -> Result<()> {
    //   self.user_assets_data.write().await.entry(unit_id).and_modify(|f| {
    //     f.credit_multiple = credit_multiple;
    //   });
    //   Ok(())
    // }

    //推送更新用户的交易状态
    pub async fn set_user_trade_flag(&self, unit_id: i64, trade_flag: i32, warnline: f64, closeline: f64, credit_multiple: f64) -> Result<()> {
        self.user_assets_data.write().await.entry(unit_id).and_modify(|f| {
            f.trade_flag = trade_flag;
            f.warn_line = warnline;
            f.close_line = closeline;
            f.credit_multiple = credit_multiple;
        });
        Ok(())
    }

    //接收推送更新用户的交易状态
    pub async fn is_changed(&self, req: &AccountInfoChange) -> bool {
        let ret = self.user_assets_data.read().await;
        if let Some(v) = ret.get(&req.user_id) {
            !(req.trade_state == v.trade_flag as i64 && req.warning_line == v.warn_line && req.close_line == v.close_line && v.credit_multiple == req.level_rate.parse::<f64>().unwrap_or_default())
        } else {
            true
        }
    }

    //重算资产
    pub async fn compute_user_assets(
        &self,
        manager_svc: &Manageclient,
        unit_id: i64,
        user_id: i64,
        postions: &Vec<UserPositionData>,
        tx_persist: &mpsc::Sender<PersistData>,
        risk_restore: f64,
        init_date: i32,
    ) -> Result<()> {
        let (asset_data, user_data) = if let Some(user_data) = self.user_assets_data.write().await.get_mut(&user_id) {
            if let Some(asset_data) = user_data.asset_vec.iter_mut().find(|asset_data| asset_data.unit_id == unit_id) {
                asset_data.position_value = 0_f64;
                asset_data.position_value_cyb = 0_f64;
                asset_data.margin_use_cyb = 0_f64;
                asset_data.margin_use = 0_f64;
                asset_data.hold_yk = 0_f64;
                postions.iter().filter(|pos| pos.unit_id == asset_data.unit_id && pos.current_amount != 0).for_each(|pos| {
                    let tvalue = pos.current_amount as f64 * pos.last_price * pos.currency_rate;
                    asset_data.position_value += tvalue;
                    if pos.stock_type == StockType::HSCY as i32 || pos.stock_type == StockType::HSKC as i32 {
                        asset_data.position_value_cyb += tvalue;
                        asset_data.margin_use_cyb += tvalue * pos.margin_rate;
                    }
                    asset_data.margin_use += tvalue * pos.margin_rate;
                    asset_data.hold_yk += tvalue - pos.total_value_hkd;
                });

                let cash = asset_data.current_cash + asset_data.hold_yk - asset_data.frozen_capital;
                asset_data.risk_rate = if cash.eq(&0.0) { 0.0 } else { (asset_data.margin_use + asset_data.trade_frozen_capital) / cash };
                (asset_data.to_owned(), user_data.to_owned())
            } else {
                return Ok(());
            }
        } else {
            return Ok(());
        };

        //若没有融资合同
        if user_data.credit_multiple == 0.0 || asset_data.current_cash == 0.0 {
            return Ok(());
        }

        if Local::now().timestamp() < Local::now().with_hour(9).unwrap_or_default().with_minute(30).unwrap_or_default().with_second(0).unwrap_or_default().timestamp()
            && Local::now().timestamp() > Local::now().with_hour(16).unwrap_or_default().with_minute(0).unwrap_or_default().with_second(0).unwrap_or_default().timestamp()
        {
            return Ok(());
        }

        //为0时，会显示出一个特别小的负数，这里需要处理一下
        let rate = format!("{:.6}", asset_data.risk_rate);
        let rate = rate.parse::<f64>().unwrap_or_default();

        //判断是否需要修改用户的状态为正常
        if user_data.trade_flag == constant::AccountStatus::AccountBuyClosed as i32 {
            if rate <= user_data.warn_line - risk_restore {
                // let ret = tx_persist.send(PersistData::RiskCancelUserAssets(Box::new(userassets.unit_id))).await;
                // if ret.as_ref().is_err() {
                //   error!("push error:{}", ret.as_ref().err().unwrap().to_string());
                // }
                self.risk_cancel_user_assets(manager_svc, user_id).await;
            }
        }

        //若是第一次触发预警
        if user_data.credit_multiple > 0.0 && (rate > user_data.warn_line || rate < 0.0) && user_data.risk_trigger_state == 0 {
            info!("第一次触发预警,{},{},{},{}", user_data.credit_multiple, rate, user_data.warn_line, user_data.risk_trigger_state);
            // let ret = tx_persist.send(PersistData::RiskUserAssets(Box::new(userassets.unit_id))).await;
            // if ret.as_ref().is_err() {
            //   error!("push error:{}", ret.as_ref().err().unwrap().to_string());
            // }
            self.risk_user_assets(init_date, manager_svc, tx_persist, unit_id, user_id).await;
        } else if user_data.credit_multiple > 0.0 && (rate > user_data.close_line || rate < 0.0) && user_data.risk_trigger_state == 1 {
            //若第一次触发平仓预警
            info!("第一次触发强平,{},{},{},{}", user_data.credit_multiple, rate, user_data.close_line, user_data.risk_trigger_state);
            // let ret = tx_persist.send(PersistData::RiskUserAssets(Box::new(userassets.unit_id))).await;
            // if ret.as_ref().is_err() {
            //   error!("push error:{}", ret.as_ref().err().unwrap().to_string());
            // }
            self.risk_user_assets(init_date, manager_svc, tx_persist, unit_id, user_id).await;
        }

        Ok(())
    }

    //预警处理
    pub async fn risk_user_assets(&self, init_date: i32, manager_svc: &Manageclient, tx_persist: &mpsc::Sender<PersistData>, unit_id: i64, user_id: i64) {
        let rate = 0.0;

        let assets_vec = match self.query_assets(&UserAssetsReq { unit_id: vec![unit_id], user_id: vec![] }).await {
            Ok(assets_result) => assets_result.assets,
            Err(err) => {
                error!("查询资产异常,{:?}", err);
                return;
            }
        };
        if assets_vec.is_empty() {
            return;
        }

        if let Some(f) = self.user_assets_data.write().await.get_mut(&user_id) {
            //默认预警状态,若风险率为负，
            let risk_type = if f.risk_trigger_state == 1 || rate < 0.0 { 1 } else { 2 };

            let details = PhoenixRiskDetails {
                sys_date: Some(init_date),
                user_id: f.user_id as i32,
                current_cash: Decimal::from_f64_retain(assets_vec[0].total_asset).unwrap_or_default(),
                credit_multiple: Decimal::from_f64_retain(assets_vec[0].level_num).unwrap_or_default(),
                warn_line: Decimal::from_f64_retain(assets_vec[0].warning_line).unwrap_or_default(),
                close_line: Decimal::from_f64_retain(assets_vec[0].close_line).unwrap_or_default(),
                risk_rate: Decimal::from_f64_retain(assets_vec[0].risk_rate).unwrap_or_default(),
                loan_cash: Decimal::from_f64_retain(assets_vec[0].total_position_value - assets_vec[0].total_asset).unwrap_or_default(),
                total_value: Decimal::from_f64_retain(assets_vec[0].total_position_value).unwrap_or_default(),
                credit_cash: Decimal::new(0, 0),
                real_cash: Decimal::from_f64_retain(assets_vec[0].real_cash).unwrap_or_default(),
                create_datetime: timeutil::current_timestamp(),
                risk_type: Some(risk_type),
                total_asset_value: Decimal::new(0, 0),
                id: 0,
                unit_id,
            };

            let ret = tx_persist.send(PersistData::RiskUserAssets(Box::new(details))).await;
            if ret.as_ref().is_err() {
                error!("push error:{}", ret.as_ref().err().unwrap().to_string());
            }

            if f.risk_trigger_state == 0 {
                f.risk_trigger_state = 1;
            } else if f.risk_trigger_state == 1 || rate < 0.0 {
                f.risk_trigger_state = 2;
            }

            //正常开仓状态改为禁止开仓
            if f.trade_flag == 1 {
                f.trade_flag = 2;
            }

            //发送邮件通知
            let sendreq = ModifyUnitReq {
                user_id,
                status: 1,
                rate,
                push_msg: f.risk_trigger_state,
            };
            if let Err(e) = manager_svc.modify_unit_state(sendreq).await {
                error!("{:?}", e);
            }
        }
    }

    //预警取消冻结处理
    pub async fn risk_cancel_user_assets(&self, manager_svc: &Manageclient, user_id: i64) {
        // info!("取消预警冻结 user_id: {}", user_id);
        self.user_assets_data.write().await.entry(user_id).and_modify(|f| {
            f.trade_flag = 1;
        });
        //解冻操作
        let sendreq = ModifyUnitReq {
            user_id,
            status: 2,
            rate: 0.0,
            push_msg: 0,
        };
        _ = manager_svc.modify_unit_state(sendreq).await
    }
}
