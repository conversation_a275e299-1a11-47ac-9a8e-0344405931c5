//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_wallet_cash_flow_copy1")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub user_id: i64,
    pub r#type: i8,
    pub status: i8,
    #[sea_orm(column_type = "Decimal(Some((10, 6)))")]
    pub rate: Decimal,
    pub source_currency: String,
    #[sea_orm(column_type = "Decimal(Some((15, 4)))")]
    pub source_money: Decimal,
    pub target_currency: String,
    #[sea_orm(column_type = "Decimal(Some((15, 4)))")]
    pub target_money: Decimal,
    pub create_date: i64,
    pub check_date: i64,
    pub check_mark: String,
    #[sea_orm(unique)]
    pub flow_no: String,
    pub other_flow_no: String,
    pub drawal_user_bank_id: i64,
    pub operator_name: String,
    pub check_name: String,
    pub trade_account: String,
    #[sea_orm(column_type = "Decimal(Some((15, 4)))")]
    pub blance_money: Decimal,
    #[sea_orm(column_type = "Decimal(Some((15, 4)))")]
    pub final_blance_money: Decimal,
    pub business_type: i8,
    pub channel: i8,
    pub handle_name: String,
    pub ticket_no: String,
    pub tranfer_id: i64,
    pub relation_account: String,
    pub isadd: i64,
    pub sub_staus: i8,
    pub jtxt: String,
    pub direction: i8,
    pub flow_type: i8,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
