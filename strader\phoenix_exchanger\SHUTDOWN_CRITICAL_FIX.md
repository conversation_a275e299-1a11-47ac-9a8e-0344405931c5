# Phoenix Exchanger Shutdown Issue Resolution

## Problem Identified
The phoenix_exchanger service was hanging on shutdown and requiring multiple Ctrl+C signals to exit. After investigation, we found **two critical issues**:

## Issue #1: Synchronous Ctrl+C Handling in main.rs
**Location**: `main.rs` - `server_run` function  
**Problem**: The `tokio::signal::ctrl_c().await.ok();` call was blocking the main execution thread instead of being handled asynchronously.

**Original Code**:
```rust
// Blocking call - prevents proper async shutdown coordination
tokio::signal::ctrl_c().await.ok();
info!("Ctrl-c received, shutting down");
// ... shutdown logic directly in main thread
```

**Fixed Code**:
```rust
// Proper async task spawning for signal handling
let controller = svr.get_controller().clone();
tokio::spawn(async move {
    tokio::signal::ctrl_c().await.ok();
    info!("Ctrl-c received, shutting down");
    // ... shutdown logic in dedicated task
    tx.send(()).ok(); // Signal completion
});

// Main thread waits for shutdown completion
rx.await.ok();
```

## Issue #2: Infinite Loop in Quotation Listening Task
**Location**: `server.rs` - quotation listening task  
**Problem**: The `messagecenter::init::init_quotation_listen` function spawns a task with an infinite loop that has **no shutdown signal handling**.

**Original Code**:
```rust
tokio::spawn(async move {
    messagecenter::init::init_quotation_listen(quotation_client).await;
    // This creates an uncontrolled infinite loop!
});
```

**Fixed Code**:
```rust
let mut shutdown_rx_quotation_listen = shutdown_tx.subscribe();
tokio::spawn(async move {
    let mut retry_interval = tokio::time::interval(std::time::Duration::from_secs(5));
    let mut quotation_client = quotation_client;
    
    loop {
        tokio::select! {
            _ = retry_interval.tick() => {
                // Quotation client processing
                if let Err(err) = quotation_client.try_consume().await {
                    error!("quotation client consume error: {:?}. start to re-connecting", err);
                    let _ = quotation_client.retry_consume().await;
                }
            }
            _ = shutdown_rx_quotation_listen.recv() => {
                info!("Quotation listen task received shutdown signal");
                break; // Proper exit on shutdown signal
            }
        }
    }
    info!("Quotation listen task has exited");
});
```

## Summary of Changes
1. **main.rs**: Restructured shutdown handling to use proper async task spawning
2. **server.rs**: Replaced uncontrolled quotation listening with shutdown-aware implementation
3. **Background Task Count**: Now **6 background tasks** all have shutdown coordination:
   - Main task dispatcher
   - Persistence task  
   - Logging task
   - Quotation processing task
   - OrderRouter retry task
   - **NEW**: Quotation listening task (now properly managed)

## Expected Result
The service should now exit cleanly on the **first Ctrl+C signal** without hanging, as all background tasks properly respond to shutdown signals and exit gracefully.

## Test Instructions
1. Run the service: `cargo run`
2. Press Ctrl+C once
3. Service should exit cleanly with proper shutdown messages
4. Verify all tasks report "has exited" in logs

Date: August 2, 2025
