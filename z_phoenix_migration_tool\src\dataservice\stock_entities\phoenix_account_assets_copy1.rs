//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_account_assets_copy1")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    #[sea_orm(unique)]
    pub p_account_id: i32,
    #[sea_orm(column_type = "Decimal(Some((20, 4)))")]
    pub p_current_principal: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 8)))")]
    pub p_position_value: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 8)))")]
    pub p_position_value_star: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 4)))")]
    pub p_prebuy_capital_star: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 4)))")]
    pub p_financing_occupied: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 8)))")]
    pub p_fee_total_hkd: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 8)))")]
    pub p_real_profit: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 8)))")]
    pub p_floating_profit: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 8)))")]
    pub p_prebuy_margin: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 8)))")]
    pub p_prebuy_margin_star: Decimal,
    pub p_account_type: i32,
    pub p_date: i32,
    pub p_updatetime: i64,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
