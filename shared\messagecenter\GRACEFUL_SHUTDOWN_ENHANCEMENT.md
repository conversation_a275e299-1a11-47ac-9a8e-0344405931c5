# Graceful Shutdown Enhancement for QuotationClient in MessageCenter

## Overview

Enhanced the shared `messagecenter` library to provide graceful shutdown capability for quotation listening tasks, ensuring all services using this library can properly coordinate shutdown of background tasks.

## Changes Made

### 1. Enhanced MessageCenter Library

**File**: `shared/messagecenter/src/init.rs`

**Added Dependencies**:

```rust
use tokio::sync::broadcast;
```

**New Function Added**:

```rust
pub async fn init_quotation_listen_with_shutdown(mut client: QuotationClient, mut shutdown_rx: broadcast::Receiver<()>) {
    let mut retry_interval = tokio::time::interval(Duration::from_secs(5));

    tokio::spawn(async move {
        loop {
            tokio::select! {
                _ = retry_interval.tick() => {
                    info!("trying quotation client consume in init......");
                    if let Err(err) = client.try_consume().await{
                        error!("quotation client consume error: {:?}. start to re-connecting", err);
                        let _ = client.retry_consume().await;
                    }
                }
                _ = shutdown_rx.recv() => {
                    info!("Quotation listen task received shutdown signal");
                    break;
                }
            }
        }
        info!("Quotation listen task has exited");
    });
}
```

**Key Features**:

- ✅ Accepts a `broadcast::Receiver<()>` for shutdown coordination
- ✅ Uses `tokio::select!` to listen for both retry intervals and shutdown signals
- ✅ Provides clean exit logging for monitoring
- ✅ Backward compatible - original `init_quotation_listen` function remains unchanged

### 2. Updated Phoenix Exchanger Implementation

**File**: `strader/phoenix_exchanger/src/server.rs`

**Before** (Manual implementation):

```rust
// Manual shutdown handling with duplicated logic
let mut shutdown_rx_quotation_listen = shutdown_tx.subscribe();
tokio::spawn(async move {
    // 20+ lines of duplicated shutdown logic...
});
```

**After** (Using shared library):

```rust
// Clean integration with shared library function
let shutdown_rx_quotation_listen = shutdown_tx.subscribe();
messagecenter::init::init_quotation_listen_with_shutdown(quotation_client, shutdown_rx_quotation_listen).await;
```

## Benefits

### 1. **Centralized Shutdown Logic**

- All quotation listening shutdown logic is now centralized in the shared library
- Consistent behavior across all services that use quotation clients
- Easier to maintain and update shutdown behavior

### 2. **Reusability**

- Other services (phoenix_accountriskcenter, phoenix_algorithmcenter, etc.) can easily adopt graceful shutdown
- No need to duplicate shutdown coordination code in each service

### 3. **Backward Compatibility**

- Existing services using `init_quotation_listen` continue to work unchanged
- Services can migrate to the shutdown-aware version when ready

### 4. **Cleaner Code**

- Reduced code duplication in phoenix_exchanger
- Better separation of concerns - shutdown logic belongs in the library

## Usage Pattern for Other Services

For any service that needs quotation listening with graceful shutdown:

```rust
// 1. Create shutdown broadcast channel
let (shutdown_tx, _) = broadcast::channel::<()>(16);

// 2. Create quotation client as usual
let quotation_client = QuotationClient::new(/* ... */).await;

// 3. Use shutdown-aware initialization
let shutdown_rx = shutdown_tx.subscribe();
messagecenter::init::init_quotation_listen_with_shutdown(quotation_client, shutdown_rx).await;

// 4. In shutdown handler, broadcast shutdown signal
shutdown_tx.send(()).ok();
```

## Services That Can Benefit

The following services currently use `init_quotation_listen` and can be upgraded:

- ✅ `phoenix_exchanger` (completed)
- 🔄 `phoenix_accountriskcenter`
- 🔄 `phoenix_algorithmcenter`
- 🔄 `phoenix_sockethqcenter`
- 🔄 `phoenix_stockaid`

## Testing

- ✅ MessageCenter library compiles successfully
- ✅ Phoenix Exchanger builds successfully with new integration
- ✅ All background tasks now have coordinated shutdown handling

## Result

Phoenix Exchanger now has 6 properly coordinated background tasks:

1. Main task dispatcher ✅
2. Persistence task ✅  
3. Logging task ✅
4. Quotation processing task ✅
5. OrderRouter retry task ✅
6. **Quotation listening task ✅** (now using shared library with shutdown support)

Date: August 2, 2025
