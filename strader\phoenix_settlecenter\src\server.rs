pub use super::controller::*;

use crate::config::settings::Settings;
use chrono::{Datelike, Local};
use common::uidservice::UidgenService;
use protoes::phoenixnotification::{MessageBody, NotificationDvdSettle, NotificationMessage, NotificationSettlement, NotificationType};
use protoes::phoenixsettlecenter::settlecenter_service_server::SettlecenterService;
use rust_decimal::prelude::ToPrimitive;
// use futures;
use tonic::{self, Request, Response, Status};
// use utility::constant::MessageType;
// use protoes::phoenixriskcenter::phoenix_riskcenter_server::PhoenixRiskcenter;
use chrono::Weekday;
use tracing::*;

use crate::dataservice::dbsetup::DbConnection;
use crate::dataservice::entities::prelude::PhoenixLiquidateDetail;
use crate::service::{LiquidateController, LiquidateNotice};
use akaclient::akaclient::time_parser;
use akaclient::akaclient::{AkaCacheOption, AkaClient};
use anyhow::Result;
use common::logclient::*;
use common::redisclient::redispool::RedisConfig;
use messagecenter::notificationclient::NotificationClient;
use protoes::phoenixsettlecenter::{
    DividendInfoReq, DividendInfoResp, LiqStep, LiquidateDetailReq, LiquidateDetailResp, LiquidateReq, LiquidateResp, PhoenixDividendComputeRequest, PhoenixDividendComputeResponse, ReSettleReq, ReSettleResp,
};
use rust_decimal::Decimal;
use std::collections::HashMap;
use std::fmt::Debug;
use std::pin::Pin;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::sync::{mpsc, oneshot, RwLock};

type StubType = Arc<RwLock<ServerController>>;
// type LiqStubType = Arc<RwLock<LiquidateController>>;

type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

#[allow(dead_code)]
pub struct ServerHandler {
    stub: StubType,
    // liq_stub: HashMap<i32, LiquidateController>,
    task_dispacther: mpsc::Sender<ControllerAction>,
    // order_dispacther: mpsc::Sender<PhoenixRiskCheckInfo>,
    set_close: Option<oneshot::Sender<()>>,
    // mqclient: QuotationClient,
}
struct ControllerDispatch<OT>(ControllerAction, oneshot::Receiver<OT>);

impl<OT: 'static + Debug + Send> ControllerDispatch<OT> {
    fn new<T>(f: T) -> Self
    where
        T: for<'c> FnOnce(&'c mut ServerController) -> Pin<Box<dyn futures::Future<Output = OT> + Send + 'c>>,
        T: Send + 'static,
    {
        let (tx, rx) = oneshot::channel();

        ControllerDispatch(
            Box::new(move |ctrl: StubType| -> Pin<Box<dyn futures::Future<Output = ()> + Send + 'static>> {
                Box::pin(async move {
                    let mut wg = ctrl.write().await;
                    if let Err(t) = tx.send(f(&mut wg).await) {
                        error!("Controller action can not be return: {:?}", t);
                    }
                })
            }),
            rx,
        )
    }
}

fn map_dispatch_err<T: 'static>(_: mpsc::error::SendError<T>) -> tonic::Status {
    tonic::Status::unknown("Server temporary unavaliable")
}
type ControllerRet<OT> = Result<OT, tonic::Status>;
type ServerRet<OT> = Result<Response<OT>, tonic::Status>;

fn map_dispatch_ret<OT: 'static>(recv_ret: Result<ControllerRet<OT>, oneshot::error::RecvError>) -> ServerRet<OT> {
    match recv_ret {
        Ok(ret) => {
            // info!("收到结果，开始分发结果返回客户端");
            ret.map(Response::new)
        }
        Err(_) => Err(Status::unknown("Dispatch ret unreach")),
    }
}
pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);

impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

impl ServerHandler {
    #[tracing::instrument(name = "初始化settlecenter服务", skip_all)]
    pub async fn new(settings: &Settings) -> Self {
        // 定时器
        let mut persist_interval = tokio::time::interval(std::time::Duration::from_secs(60));

        let (tx_notification, _rx_notification) = tokio::sync::mpsc::channel::<NotificationMessage>(128);

        let notification_client = Arc::new(RwLock::new(
            NotificationClient::new(
                &settings.notification.notification_exchanger.as_str(),
                "",
                settings.notification.settlecenter_routing_key.to_owned(),
                &format!("{}{}", &settings.mq.amqpaddr, &settings.notification.vhost),
                tx_notification,
            )
            .await,
        ));
        messagecenter::init::init_notification_client(notification_client.clone()).await;

        let uidsvc = Arc::new(RwLock::new(UidgenService::new(settings.application.machineid, settings.application.nodeid)));
        let (lqtx, mut lqrx) = mpsc::channel(16);
        let dbconn = DbConnection::new(&settings.database.stock_uri).await;
        let mut akacache = AkaCacheOption::default();
        akacache = AkaCacheOption {
            use_cache: settings.system.cacheflag,
            mq_uri: format!("{}{}", &settings.mq.amqpaddr, &settings.notification.vhost),
            exchange: settings.notification.notification_exchanger.to_string(),
            routing_keys: settings.notification.settlecenter_routing_key.to_string(),
            ..akacache.clone()
        };
        info!("缓存开关:{:?}", &akacache);
        let akacenterconn = AkaClient::init(settings.servers.akacenterserver.to_string(), &akacache).await;
        // let akacenterconn = AkaClient::init(settings.system.akaserver.to_string(), false, settings.system.cachelong as i64).await;
        let mut rds_cfg = RedisConfig::default();
        rds_cfg.urls = settings.redis.uri.to_owned();
        rds_cfg.prefix = settings.redis.prefix.to_owned();
        rds_cfg.max_size = 12;
        let redisclient = common::redisclient::redispool::RedisClient::new(&rds_cfg).expect("init redis cluster error");

        let stub1 = ServerController {
            settings: settings.clone(), // rbcon: rb.to_owned(),
            dbconn: dbconn.clone(),
            aka_cli: akacenterconn.clone(),
            uidsvc: uidsvc.clone(),
            liq_notification: lqtx.clone(),
            redisclient: redisclient.clone(),
        };

        let stub = Arc::new(RwLock::new(stub1));
        let liq_stub: Arc<RwLock<HashMap<i32, LiquidateController>>> = Arc::new(RwLock::new(HashMap::new()));

        let ret = PhoenixLiquidateDetail::query_many(0, dbconn.get_connection()).await;
        if ret.is_err() {
            log_error(&format!("query liquidate detail error:{:?}", ret.as_ref().unwrap_err().to_string())).await;
            error!("query liquidate detail error:{:?}", ret.as_ref().unwrap_err().to_string());
        }
        let res = ret.unwrap();
        for liqconf in res.iter() {
            info!(
                "市场类型:{} 结算时间:{} 权益分派时间:{}",
                liqconf.market_type,
                liqconf.liquidate_trigger_time.clone(),
                liqconf.dividend_trigger_time.clone()
            );
            liq_stub.write().await.insert(
                liqconf.market_type,
                LiquidateController {
                    market_type: liqconf.market_type,
                    current_step: LiqStep::None,
                    liquate_trigger_time: liqconf.liquidate_trigger_time.clone(),
                    dividend_trigger_time: liqconf.dividend_trigger_time.clone(),
                    dvd_state: 0,
                    sender: lqtx.clone(),
                },
            );
        }

        // let liq_stub =_liq_stub,// Arc::new(RwLock::new(_liq_stub));

        let (tx, mut rx) = mpsc::channel::<ControllerAction>(16);

        let (tx_close, mut rx_close) = oneshot::channel();

        let stub_for_dispatch = stub.clone();

        let svr_handler = ServerHandler {
            task_dispacther: tx,
            set_close: Some(tx_close),
            stub,
        };

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    may_task = rx.recv() => {
                        if let Some(task) = may_task{
                            task(stub_for_dispatch.clone()).await;
                        }
                    },
                    _ = &mut rx_close => {
                        info!("Server scheduler is notified to close");
                        rx.close();
                        break;
                    }
                }
            }

            //drain unhandled task
            while let Some(task) = rx.recv().await {
                task(stub_for_dispatch.clone()).await;
            }

            warn!("Server scheduler has exited");
        });

        let liq_stub_tmp = liq_stub.clone();
        let aka_client = akacenterconn.clone();
        tokio::spawn(async move {
            // persist_interval.tick().await; //skip first tick
            loop {
                persist_interval.tick().await;

                // info!("Start a time interval task (persist, computing):{:?}",std::thread::current());
                let current_time = SystemTime::now().duration_since(UNIX_EPOCH).expect("get_current_unix_err").as_secs().to_i64().unwrap();
                // 节假日控制
                let cur_weekday = Local::now().weekday();
                // info!("today weekday :{}, ", cur_weekday);
                if cur_weekday == Weekday::Sat || cur_weekday == Weekday::Sun {
                    // 周末不做结算
                    // info!("today weekday :{}, 不做结算", cur_weekday);
                    continue;
                }
                //非周末节假日不做结算
                let current_ret = aka_client.query_trade_date(0, 0, 0, 0).await;
                if current_ret.as_ref().is_err() {
                    error!("查询交易日error:{:?}", current_ret.as_ref().err().unwrap());
                    log_error(&format!("查询交易日error:{:?}", current_ret.as_ref().err().unwrap())).await;
                    // if let Ok(log_client) = LogClient::get() {
                    //     log_client.push_error(&format!("查询交易日error:{:?}", current_ret.as_ref().err().unwrap())).await;
                    // }
                    panic!("查询交易日error");
                }
                let current_date_ret = current_ret.unwrap();
                // 当前时间
                let current_date = current_date_ret[0].current_date;
                // info!("current_date:{:?}", current_date);
                let trade_ret = aka_client.query_trade_date(0, 0, 1, 0).await;
                if trade_ret.as_ref().is_err() {
                    error!("查询交易日error:{:?}", trade_ret.as_ref().err().unwrap());
                    log_error(&format!("查询交易日error:{:?}", trade_ret.as_ref().err().unwrap())).await;
                    // if let Ok(log_client) = LogClient::get() {
                    //     log_client.push_error(&format!("查询交易日error:{:?}", trade_ret.as_ref().err().unwrap())).await;
                    // }
                    panic!("查询交易日error");
                }
                //交易时间
                let trade_date_ret = trade_ret.unwrap();
                let mut trade_date: i32 = 0;
                for td in trade_date_ret.iter() {
                    // info!("查询交易日tradedate:{:?}", &td);
                    //交易日
                    if trade_date == 0 || trade_date > td.target_date {
                        trade_date = td.target_date;
                    }
                }

                for (mt, ctrl) in liq_stub_tmp.read().await.iter() {
                    // info!("{mt} {:?}", ctrl);
                    let mut liquate_trigger_time = 0;
                    let mut dividend_trigger_time = 0;

                    if let Ok(liquatett) = time_parser(&ctrl.liquate_trigger_time) {
                        //.expect("parse liquate_trigger_time error");
                        liquate_trigger_time = liquatett;
                    } else {
                        error!("parse liquate_trigger_time error");
                        log_error(&format!("parse liquate_trigger_time error,{}", ctrl.liquate_trigger_time)).await;
                    }
                    if let Ok(dividend_trigger_t) = time_parser(&ctrl.dividend_trigger_time) {
                        dividend_trigger_time = dividend_trigger_t;
                    } else {
                        error!("parse dividend_trigger_time error");
                    }
                    // 检查是否结算时间
                    // info!(
                    //     "市场:{} 结算时间:{} 当前时间:{}",
                    //     mt,
                    //     utility::timeutil::format_timestamp(liquate_trigger_time),
                    //     utility::timeutil::format_timestamp(current_time)
                    // );
                    if liquate_trigger_time <= current_time && (liquate_trigger_time + 60) > current_time {
                        // debug!("trade_date:{:?}", trade_date);
                        if current_date < trade_date {
                            // info!("today非交易日, 不做结算");
                            continue;
                        }

                        info!(mt, "市场 {} 开始结算", mt);
                        log_info(&format!("市场 {} 开始结算", mt)).await;
                        let _ = lqtx
                            .send(LiquidateNotice {
                                liq_step: LiqStep::LiqPrepare,
                                market_type: *mt,
                            })
                            .await;
                    }
                    // 检查是否分红送股时间
                    if dividend_trigger_time < current_time && (dividend_trigger_time + 60) > current_time {
                        info!(mt, "市场 {} 开始分红送股处理", mt);
                        let _ = lqtx
                            .send(LiquidateNotice {
                                liq_step: LiqStep::DvdRegister,
                                market_type: *mt,
                            })
                            .await;
                    }
                }
            }
        });

        let liq_stub_tmp = liq_stub.clone();
        tokio::spawn(async move {
            loop {
                if let Some(lqntc) = lqrx.recv().await {
                    let step = lqntc.liq_step;
                    let mut wr = liq_stub_tmp.write().await;
                    let ctrler = wr.get_mut(&lqntc.market_type); // 获取相应市场的ctrler
                    match ctrler {
                        Some(_ctrler) => match step {
                            // 日终处理和结算前准备工作
                            LiqStep::LiqPrepare => {
                                info!("市场 {} 系统开始清算准备", lqntc.market_type);

                                // 推送结算通知
                                let mut msgbody = MessageBody { ..Default::default() };
                                msgbody.msg_settlement = Some(NotificationSettlement {
                                    settle_status: 1,
                                    exchange_type: lqntc.market_type,
                                });
                                _ = notification_client
                                    .read()
                                    .await
                                    .try_publish(
                                        &format!("notification.settlement.{}", lqntc.market_type),
                                        &NotificationMessage {
                                            msg_type: NotificationType::Settlement.into(),
                                            msg_body: Some(msgbody),
                                        },
                                    )
                                    .await;

                                let res = _ctrler.liq_prepare(dbconn.clone(), uidsvc.clone(), akacenterconn.clone()).await;

                                match res {
                                    // 更新phoenix_liquidate_detail
                                    Ok(_) => {
                                        _ = PhoenixLiquidateDetail::update_liquidate_state(lqntc.market_type, &LiqStep::LiqPrepare, 1, "", dbconn.get_connection()).await;
                                    }
                                    Err(err) => {
                                        _ = PhoenixLiquidateDetail::update_liquidate_state(lqntc.market_type, &LiqStep::LiqPrepare, 2, &(format!("{}", err)).to_owned(), dbconn.get_connection()).await;
                                        error!("{}", err)
                                    }
                                }
                                log_info(&format!("市场 {} 清算准备完成", lqntc.market_type)).await;
                            }
                            //归档
                            LiqStep::Archive => {
                                info!("市场 {} 开始归档", lqntc.market_type);

                                let res = _ctrler.archive(&dbconn, &akacenterconn).await;
                                match res {
                                    // 更新phoenix_liquidate_detail
                                    Ok(_) => {
                                        _ = PhoenixLiquidateDetail::update_liquidate_state(lqntc.market_type, &LiqStep::Archive, 1, "", dbconn.get_connection()).await;
                                    }
                                    Err(err) => {
                                        _ = PhoenixLiquidateDetail::update_liquidate_state(lqntc.market_type, &LiqStep::Archive, 2, &(format!("{}", err)).to_owned(), dbconn.get_connection()).await;
                                        error!("{}", err)
                                    }
                                }
                                info!("市场 {} 归档完成", lqntc.market_type);
                                log_info(&format!("市场 {} 归档完成", lqntc.market_type)).await;
                            }
                            //初始化
                            LiqStep::Initialize => {
                                info!("市场 {} 开始初始化", lqntc.market_type);
                                let res = _ctrler.initialize(&dbconn, &akacenterconn).await;
                                match res {
                                    // 更新phoenix_liquidate_detail
                                    Ok(_) => {
                                        _ = PhoenixLiquidateDetail::update_liquidate_state(lqntc.market_type, &LiqStep::Initialize, 1, "", dbconn.get_connection()).await;
                                    }
                                    Err(err) => {
                                        _ = PhoenixLiquidateDetail::update_liquidate_state(lqntc.market_type, &LiqStep::Initialize, 2, &(format!("{}", err)).to_owned(), dbconn.get_connection()).await;
                                        error!("{}", err)
                                    }
                                }
                                info!("市场 {} 初始化完成", lqntc.market_type);
                                log_debug(&format!("市场 {} 初始化完成", lqntc.market_type)).await;
                            }
                            //交收
                            LiqStep::Settle => {
                                info!("市场 {} 开始交收", lqntc.market_type);

                                let res = _ctrler.settle(&dbconn, &akacenterconn, &redisclient).await;
                                match res {
                                    // 更新phoenix_liquidate_detail
                                    Ok(_) => {
                                        _ = PhoenixLiquidateDetail::update_liquidate_state(lqntc.market_type, &LiqStep::Settle, 1, "", dbconn.get_connection()).await;
                                    }
                                    Err(err) => {
                                        _ = PhoenixLiquidateDetail::update_liquidate_state(lqntc.market_type, &LiqStep::Settle, 2, &(format!("{}", err)).to_owned(), dbconn.get_connection()).await;
                                        error!("{}", err)
                                    }
                                }
                                info!("市场 {} 交收完成", lqntc.market_type);
                                log_info(&format!("市场 {} 交收完成", lqntc.market_type)).await;
                                let mut msgbody = MessageBody { ..Default::default() };
                                msgbody.msg_settlement = Some(NotificationSettlement {
                                    settle_status: 2,
                                    exchange_type: lqntc.market_type,
                                });
                                _ = notification_client
                                    .read()
                                    .await
                                    .try_publish(
                                        &format!("notification.settlement.{}", lqntc.market_type),
                                        &NotificationMessage {
                                            msg_type: NotificationType::Settlement.into(),
                                            msg_body: Some(msgbody),
                                        },
                                    )
                                    .await;
                            }
                            // -----------------------权益分派----------------------
                            LiqStep::DvdRegister => {
                                info!("市场 {} 开始权益分派处理", lqntc.market_type);
                                let mut msgbody = MessageBody { ..Default::default() };
                                msgbody.msg_dvdsettle = Some(NotificationDvdSettle { status: 1 });
                                _ = notification_client
                                    .read()
                                    .await
                                    .try_publish(
                                        &format!("notification.dvd_settle.{}", lqntc.market_type),
                                        &NotificationMessage {
                                            msg_type: NotificationType::DvdSettle.into(),
                                            msg_body: Some(msgbody),
                                        },
                                    )
                                    .await;

                                let res = _ctrler.dvd_register(dbconn.clone(), uidsvc.clone(), akacenterconn.clone()).await;
                                match res {
                                    // 更新phoenix_liquidate_detail
                                    Ok(_) => {
                                        _ = PhoenixLiquidateDetail::update_liquidate_state(lqntc.market_type, &LiqStep::DvdRegister, 1, "", dbconn.get_connection()).await;
                                    }
                                    Err(err) => {
                                        _ = PhoenixLiquidateDetail::update_liquidate_state(lqntc.market_type, &LiqStep::DvdRegister, 2, &(format!("{}", err)).to_owned(), dbconn.get_connection()).await;
                                        error!("{}", err)
                                    }
                                }
                                info!("市场 {} 权益分派处理完", lqntc.market_type);
                                log_info(&format!("市场 {} 权益分派处理完", lqntc.market_type)).await;
                                let mut msgbody = MessageBody { ..Default::default() };
                                msgbody.msg_dvdsettle = Some(NotificationDvdSettle { status: 2 });
                                _ = notification_client
                                    .read()
                                    .await
                                    .try_publish(
                                        &format!("notification.dvd_settle.{}", lqntc.market_type),
                                        &NotificationMessage {
                                            msg_type: NotificationType::DvdSettle.into(),
                                            msg_body: Some(msgbody),
                                        },
                                    )
                                    .await;
                            }
                            _ => error!("unknown liquidate step"),
                        },
                        None => error!("unknow market type"),
                    }
                }

                // notification = rx_notification.recv() => {
                //     //收到消息通知
                //     if let Some(message) = &notification{
                //         //receive message
                //         info!("receive message from message center: {:?}", &message);

                //     }
                // }
            }
        });

        svr_handler
    }
    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

//这里实现grpc的接口
#[tonic::async_trait]
impl SettlecenterService for ServerHandler {
    async fn query_liquidate_detail(&self, request: Request<LiquidateDetailReq>) -> Result<Response<LiquidateDetailResp>, Status> {
        // let stub = self.stub.read().await;
        // Ok(Response::new(stub.phoenix_place_order(request.into_inner())?))
        let ControllerDispatch(act, rt) = ControllerDispatch::new(move |ctrl: &mut ServerController| Box::pin(async move { ctrl.query_liquidate_detail(request.into_inner()).await }));

        self.task_dispacther.send(act).await.map_err(map_dispatch_err)?;
        map_dispatch_ret(rt.await)
    }

    async fn do_liquidate(&self, request: Request<LiquidateReq>) -> Result<Response<LiquidateResp>, Status> {
        // let stub = self.stub.read().await;
        // Ok(Response::new(stub.phoenix_place_order(request.into_inner())?))
        let ControllerDispatch(act, rt) = ControllerDispatch::new(move |ctrl: &mut ServerController| Box::pin(async move { ctrl.do_liquidate(request.into_inner()).await }));

        self.task_dispacther.send(act).await.map_err(map_dispatch_err)?;
        map_dispatch_ret(rt.await)
    }

    async fn dividend_info(&self, request: Request<DividendInfoReq>) -> Result<Response<DividendInfoResp>, Status> {
        // let stub = self.stub.read().await;
        // Ok(Response::new(stub.phoenix_place_order(request.into_inner())?))
        let ControllerDispatch(act, rt) = ControllerDispatch::new(move |ctrl: &mut ServerController| Box::pin(async move { ctrl.dividend_info(request.into_inner()).await }));

        self.task_dispacther.send(act).await.map_err(map_dispatch_err)?;
        map_dispatch_ret(rt.await)
    }
    async fn re_settle(&self, request: Request<ReSettleReq>) -> Result<Response<ReSettleResp>, Status> {
        // let stub = self.stub.read().await;
        // Ok(Response::new(stub.phoenix_place_order(request.into_inner())?))
        let ControllerDispatch(act, rt) = ControllerDispatch::new(move |ctrl: &mut ServerController| Box::pin(async move { ctrl.resettle(request.into_inner()).await }));

        self.task_dispacther.send(act).await.map_err(map_dispatch_err)?;
        map_dispatch_ret(rt.await)
    }

    async fn compute_dvd_register(&self, request: Request<PhoenixDividendComputeRequest>) -> std::result::Result<Response<PhoenixDividendComputeResponse>, Status> {
        let req = request.into_inner();
        let ControllerDispatch(act, rt) = ControllerDispatch::new(move |ctrl: &mut ServerController| {
            Box::pin(async move {
                ctrl.compute_dvd_register(
                    req.before_date as i32,
                    req.cur_date as i32,
                    Decimal::from_f64_retain(req.rate_hr).unwrap_or_default(),
                    Decimal::from_f64_retain(req.rate_hu).unwrap_or_default(),
                )
                .await
            })
        });

        self.task_dispacther.send(act).await.map_err(map_dispatch_err)?;
        map_dispatch_ret(rt.await)
    }
}
