syntax = "proto3";
package phoenixmessagerouter;

import "ordercenter.proto";
import "ordermsg.proto";

service MessageRouterService {
  rpc OrderMessageTransfer(phoenixordercenter.OrderReq) returns (phoenixordercenter.OrderResp) {}
  rpc ExecOrderMessageTransfer(phoenixordermsg.RouterMsg) returns (phoenixordermsg.RouterMsg) {}
  rpc ReplenishmentOrderTransfer(phoenixordercenter.ReplenishOrderReq) returns (phoenixordercenter.ReplenishOrderResp) {}
}
