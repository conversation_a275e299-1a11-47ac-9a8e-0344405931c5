# Phoenix Exchanger Shutdown Hanging Issue - Analysis and Fix

## Problem Analysis

Based on the shutdown logs, the service is hanging after all background tasks appear to shut down properly. The issue seems to be in the final cleanup phase where `on_leave.leave().await` is waiting indefinitely.

## Key Observations from Logs

1. ✅ **AkaClient shutdown**: Complete with all tasks exited
2. ✅ **ServerController shutdown**: Completed successfully  
3. ✅ **Background tasks shutdown**: All 4 core tasks shut down properly:
   - Main task dispatcher
   - Persistence task
   - Logging task
   - Quotation task
4. ✅ **OrderRouterClient shutdown**: All sub-tasks shut down properly
5. ❌ **Missing logs**: No "OrderRouter retry task" shutdown messages
6. ❌ **Missing logs**: No "Quotation listen task" shutdown messages
7. ❌ **Final hang**: `on_leave.leave().await` never completes

## Root Cause Analysis

The issue is likely that:

1. **OrderRouter retry task** - Not showing shutdown logs, might not be receiving shutdown signal
2. **Quotation listen task** - Using shared library function, might not be properly integrated
3. **Task dispatcher channel** - `on_leave.leave()` waits for `task_dispatcher.closed()` but some task might still hold a reference

## Implemented Fixes

### 1. Enhanced Timeout Handling
- Added timeout to main shutdown wait (30 seconds)
- Added timeout to `on_leave.leave()` (5 seconds)
- Added more diagnostic logging

### 2. Increased Wait Times
- Extended background task wait from 1000ms to 2000ms
- Extended individual task waits from 300ms to 500ms

### 3. Better Error Handling
- Added timeout warnings for forced exit
- Added more detailed shutdown progress logging

## Expected Behavior After Fix

The service should now:
1. Respond to Ctrl+C immediately
2. Show progress through each shutdown phase
3. Either complete shutdown within 30 seconds OR timeout with clear warning
4. Not hang indefinitely

## Test Approach

1. Run the service
2. Press Ctrl+C once
3. Observe logs for:
   - "OrderRouter retry task received shutdown signal"
   - "Quotation listen task received shutdown signal"  
   - Successful completion OR timeout warning

## Fallback Plan

If the issue persists, we may need to:
1. Store task handles explicitly and await them
2. Use `tokio::task::AbortHandle` for force termination
3. Restructure the shutdown sequence to be more aggressive

Date: August 2, 2025
