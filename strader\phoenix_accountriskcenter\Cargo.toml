[package]
name = "phoenix_accountriskcenter"
version = "0.2.0"
edition = "2021"
description = "账户风控系统 build time: 2025-07-07"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
akaclient = { workspace = true }
assetscenterclient = { workspace = true }
manageclient = { workspace = true }
common = { workspace = true }
utility = { workspace = true }
messagecenter = { workspace = true }
protoes = { workspace = true }
tonic = { workspace = true }
prost = { workspace = true }
chrono = { workspace = true }
tokio = { workspace = true }
tokio-stream = { workspace = true }
futures = { workspace = true }
# async-stream = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
rust_decimal = { workspace = true }

tracing = { workspace = true }

dashmap = { workspace = true }
# log = { workspace = true }
# log4rs = { workspace = true }
sea-orm = { workspace = true }

config = { workspace = true }
anyhow = { workspace = true }
axum = { workspace = true }

#tokio-rs web framework
tower = { workspace = true }
env_logger = { workspace = true }
itertools = { workspace = true }
lazy_static = { workspace = true }
rand = { workspace = true }
linked-hash-map = { workspace = true }
# [build-dependencies]
# # tonic-build = { workspace = true, features = ["prost"] }
# tonic-build.workspace = true
