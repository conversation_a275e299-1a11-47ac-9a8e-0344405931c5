use crate::dataservice::{
    dbsetup::DbConnection,
    stock_entities::{
        phoenix_sys_system,
        prelude::{PhoenixSysSystem, PhoenixSysSystemEntity},
    },
};
use anyhow::{anyhow, Result};
use sea_orm::{EntityTrait, FromQueryResult, QuerySelect};

#[derive(<PERSON>bu<PERSON>, FromQueryResult)]
struct InitDate {
    init_date: i32,
}

impl PhoenixSysSystem {
    pub async fn find_init_date(db: &DbConnection) -> Result<i32> {
        let ret_data = PhoenixSysSystemEntity::find()
            .select_only()
            .column(phoenix_sys_system::Column::InitDate)
            .into_model::<InitDate>()
            .one(db.get_connection())
            .await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        let data = ret_data.unwrap();
        return match data {
            Some(v) => Ok(v.init_date),
            None => Err(anyhow!("PhoenixSysSystem数据不存在")),
        };
    }
}
