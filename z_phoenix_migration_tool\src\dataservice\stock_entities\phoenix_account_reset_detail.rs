//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "phoenix_account_reset_detail")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub p_account_unit_id: i32,
    #[sea_orm(column_type = "Decimal(Some((20, 8)))")]
    pub p_float_profit_before: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 8)))")]
    pub p_current_profit_before: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 8)))")]
    pub p_float_profit_after: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 8)))")]
    pub p_current_profit_after: Decimal,
    #[sea_orm(column_type = "Decimal(Some((20, 8)))")]
    pub p_current_principal: Decimal,
    pub p_account_no: i32,
    pub p_datetime: i64,
    pub p_remark: String,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
