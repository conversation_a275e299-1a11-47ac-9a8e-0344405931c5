//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_rq")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub commodity_id: i64,
    pub credit_num: i64,
    #[sea_orm(column_type = "Text", nullable)]
    pub userids: Option<String>,
    pub credate_date: i64,
    pub expire_date: i32,
    pub channel_id: i32,
    pub user_id: i64,
    pub start_date: i32,
    pub use_num: i32,
    pub state: i8,
    pub mark: String,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
